#===============================================================================
#  Extensions for the `String` data type
#===============================================================================
class ::String
  #-----------------------------------------------------------------------------
  #  turns string into an actual Ruby object
  #-----------------------------------------------------------------------------
  def constantize
    Object.const_get(self)
  end
  #-----------------------------------------------------------------------------
  #  turns string into an actual Ruby object if exists
  #-----------------------------------------------------------------------------
  def safe_constantize
    Object.const_get(self) if Object.const_defined?(self)
  end
  #-----------------------------------------------------------------------------
  #  capitalize first letter
  #-----------------------------------------------------------------------------
  def capitalize
    sub(/^\w/) { $&.upcase }
  end
  #-----------------------------------------------------------------------------
  #  turn to camel case
  #-----------------------------------------------------------------------------
  def camelize
    downcase.split('_').map(&:capitalize).join('')
  end
  #-----------------------------------------------------------------------------
  #  turn to snake case
  #-----------------------------------------------------------------------------
  def underscore
    return downcase if match(/\A[A-Z]+\z/)

    gsub(/([A-Z]+)([A-Z][a-z])/, '\1_\2').gsub(/([a-z])([A-Z])/, '\1_\2').tr('-', '_').downcase
  end
  #-----------------------------------------------------------------------------
  #  returns value if blank or present
  #-----------------------------------------------------------------------------
  def blank?
    self.eql?('') || self.chars.all? { |c| c.eql?(' ') }
  end

  def present?
    !blank?
  end
  #-----------------------------------------------------------------------------
end
