#===============================================================================
#  Extensions for the `Nil` data types
#===============================================================================
class ::Symbol
  #-----------------------------------------------------------------------------
  #  returns value if blank or present
  #-----------------------------------------------------------------------------
  def blank?
    false
  end

  def present?
    true
  end
  #-----------------------------------------------------------------------------
end
