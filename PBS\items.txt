﻿# See the documentation on the wiki to learn how to edit this file.
#-------------------------------
[REPEL]
Name = Repel
NamePlural = Repels
Pocket = 1
Price = 400
FieldUse = Direct
Flags = Repel,Fling_30
Description = An item that prevents weak wild Pokémon from appearing for 100 steps after its use.
#-------------------------------
[SUPERREPEL]
Name = Super Repel
NamePlural = Super Repels
Pocket = 1
Price = 700
FieldUse = Direct
Flags = Repel,Fling_30
Description = An item that prevents weak wild Pokémon from appearing for 200 steps after its use.
#-------------------------------
[MAXREPEL]
Name = Max Repel
NamePlural = Max Repels
Pocket = 1
Price = 900
FieldUse = Direct
Flags = Repel,Fling_30
Description = An item that prevents weak wild Pokémon from appearing for 250 steps after its use.
#-------------------------------
[BLACKFLUTE]
Name = Black Flute
NamePlural = Black Flutes
Pocket = 1
Price = 20
FieldUse = Direct
Consumable = false
Flags = Fling_30
Description = A black flute made from blown glass. Its melody makes wild Pokémon less likely to appear.
#-------------------------------
[WHITEFLUTE]
Name = White Flute
NamePlural = White Flutes
Pocket = 1
Price = 20
FieldUse = Direct
Consumable = false
Flags = Fling_30
Description = A white flute made from blown glass. Its melody makes wild Pokémon more likely to appear.
#-------------------------------
[HONEY]
Name = Honey
NamePlural = Honey
Pocket = 1
Price = 900
FieldUse = Direct
Flags = Fling_30
Description = A sweet honey with a lush aroma that attracts wild Pokémon when used in tall grass, in caves, or elsewhere.
#-------------------------------
[REDSHARD]
Name = Red Shard
NamePlural = Red Shards
Pocket = 1
Price = 1000
Flags = Fling_30
Description = A small red shard. It appears to be from some sort of implement made long ago.
#-------------------------------
[YELLOWSHARD]
Name = Yellow Shard
NamePlural = Yellow Shards
Pocket = 1
Price = 1000
Flags = Fling_30
Description = A small yellow shard. It appears to be from some sort of implement made long ago.
#-------------------------------
[BLUESHARD]
Name = Blue Shard
NamePlural = Blue Shards
Pocket = 1
Price = 1000
Flags = Fling_30
Description = A small blue shard. It appears to be from some sort of implement made long ago.
#-------------------------------
[GREENSHARD]
Name = Green Shard
NamePlural = Green Shards
Pocket = 1
Price = 1000
Flags = Fling_30
Description = A small green shard. It appears to be from some sort of implement made long ago.
#-------------------------------
[FIRESTONE]
Name = Fire Stone
NamePlural = Fire Stones
Pocket = 1
Price = 3000
FieldUse = OnPokemon
Flags = EvolutionStone,Fling_30
Description = A peculiar stone that makes certain species of Pokémon evolve. It is colored orange.
#-------------------------------
[THUNDERSTONE]
Name = Thunder Stone
NamePlural = Thunder Stones
Pocket = 1
Price = 3000
FieldUse = OnPokemon
Flags = EvolutionStone,Fling_30
Description = A peculiar stone that makes certain species of Pokémon evolve. It has a thunderbolt pattern.
#-------------------------------
[WATERSTONE]
Name = Water Stone
NamePlural = Water Stones
Pocket = 1
Price = 3000
FieldUse = OnPokemon
Flags = EvolutionStone,Fling_30
Description = A peculiar stone that makes certain species of Pokémon evolve. It is a clear, light blue.
#-------------------------------
[LEAFSTONE]
Name = Leaf Stone
NamePlural = Leaf Stones
Pocket = 1
Price = 3000
FieldUse = OnPokemon
Flags = EvolutionStone,Fling_30
Description = A peculiar stone that makes certain species of Pokémon evolve. It has a leaf pattern.
#-------------------------------
[MOONSTONE]
Name = Moon Stone
NamePlural = Moon Stones
Pocket = 1
Price = 3000
FieldUse = OnPokemon
Flags = EvolutionStone,Fling_30
Description = A peculiar stone that makes certain species of Pokémon evolve. It is as black as the night sky.
#-------------------------------
[SUNSTONE]
Name = Sun Stone
NamePlural = Sun Stones
Pocket = 1
Price = 3000
FieldUse = OnPokemon
Flags = EvolutionStone,Fling_30
Description = A peculiar stone that makes certain species of Pokémon evolve. It is as red as the sun.
#-------------------------------
[DUSKSTONE]
Name = Dusk Stone
NamePlural = Dusk Stones
Pocket = 1
Price = 3000
FieldUse = OnPokemon
Flags = EvolutionStone,Fling_80
Description = A peculiar stone that makes certain species of Pokémon evolve. It is as dark as dark can be.
#-------------------------------
[DAWNSTONE]
Name = Dawn Stone
NamePlural = Dawn Stones
Pocket = 1
Price = 3000
FieldUse = OnPokemon
Flags = EvolutionStone,Fling_80
Description = A peculiar stone that makes certain species of Pokémon evolve. It sparkles like eyes.
#-------------------------------
[SHINYSTONE]
Name = Shiny Stone
NamePlural = Shiny Stones
Pocket = 1
Price = 3000
FieldUse = OnPokemon
Flags = EvolutionStone,Fling_80
Description = A peculiar stone that makes certain species of Pokémon evolve. It shines with a dazzling light.
#-------------------------------
[ICESTONE]
Name = Ice Stone
NamePlural = Ice Stones
Pocket = 1
Price = 3000
FieldUse = OnPokemon
Flags = EvolutionStone,Fling_30
Description = A peculiar stone that makes certain species of Pokémon evolve. It has a snowflake pattern.
#-------------------------------
[SWEETAPPLE]
Name = Sweet Apple
NamePlural = Sweet Apples
Pocket = 1
Price = 2200
FieldUse = OnPokemon
Flags = EvolutionStone,Fling_30
Description = A peculiar apple that can make a certain species of Pokémon evolve. It's exceptionally sweet.
#-------------------------------
[TARTAPPLE]
Name = Tart Apple
NamePlural = Tart Apples
Pocket = 1
Price = 2200
FieldUse = OnPokemon
Flags = EvolutionStone,Fling_30
Description = A peculiar apple that can make a certain species of Pokémon evolve. It's exceptionally tart.
#-------------------------------
[CRACKEDPOT]
Name = Cracked Pot
NamePlural = Cracked Pots
Pocket = 1
Price = 3000
SellPrice = 800
FieldUse = OnPokemon
Flags = EvolutionStone,Fling_80
Description = A peculiar cracked teapot that can make a certain species of Pokémon evolve. It makes delicious tea.
#-------------------------------
[CHIPPEDPOT]
Name = Chipped Pot
NamePlural = Chipped Pots
Pocket = 1
Price = 3000
SellPrice = 19000
FieldUse = OnPokemon
Flags = EvolutionStone,Fling_80
Description = A peculiar chipped teapot that can make a certain species of Pokémon evolve. It makes delicious tea.
#-------------------------------
[GALARICACUFF]
Name = Galarica Cuff
NamePlural = Galarica Cuffs
Pocket = 1
Price = 6000
FieldUse = OnPokemon
Flags = EvolutionStone,Fling_30
Description = A cuff made from woven-together Galarica Twigs. Giving it to a Galarian Slowpoke would make it very happy.
#-------------------------------
[GALARICAWREATH]
Name = Galarica Wreath
NamePlural = Galarica Wreaths
Pocket = 1
Price = 6000
FieldUse = OnPokemon
Flags = EvolutionStone,Fling_30
Description = A wreath made from woven-together Galarica Twigs. A Galarian Slowpoke wearing this would be very happy.
#-------------------------------
[REDAPRICORN]
Name = Red Apricorn
NamePlural = Red Apricorns
Pocket = 1
Price = 200
Flags = Apricorn
Description = A red Apricorn. It assails your nostrils.
#-------------------------------
[YELLOWAPRICORN]
Name = Yellow Apricorn
NamePlural = Yellow Apricorns
Pocket = 1
Price = 200
Flags = Apricorn
Description = A yellow Apricorn. It has an invigorating scent.
#-------------------------------
[BLUEAPRICORN]
Name = Blue Apricorn
NamePlural = Blue Apricorns
Pocket = 1
Price = 200
Flags = Apricorn
Description = A blue Apricorn. It smells a bit like grass.
#-------------------------------
[GREENAPRICORN]
Name = Green Apricorn
NamePlural = Green Apricorns
Pocket = 1
Price = 200
Flags = Apricorn
Description = A green Apricorn. It has a mysterious, aromatic scent.
#-------------------------------
[PINKAPRICORN]
Name = Pink Apricorn
NamePlural = Pink Apricorns
Pocket = 1
Price = 200
Flags = Apricorn
Description = A pink Apricorn. It has a nice, sweet scent.
#-------------------------------
[WHITEAPRICORN]
Name = White Apricorn
NamePlural = White Apricorns
Pocket = 1
Price = 200
Flags = Apricorn
Description = A white Apricorn. It doesn't smell like anything.
#-------------------------------
[BLACKAPRICORN]
Name = Black Apricorn
NamePlural = Black Apricorns
Pocket = 1
Price = 200
Flags = Apricorn
Description = A black Apricorn. It has an indescribable scent.
#-------------------------------
[HELIXFOSSIL]
Name = Helix Fossil
NamePlural = Helix Fossils
Pocket = 1
Price = 7000
Flags = Fossil,Fling_100
Description = A fossil of an ancient Pokémon that lived in the sea. It appears to be part of a seashell.
#-------------------------------
[DOMEFOSSIL]
Name = Dome Fossil
NamePlural = Dome Fossils
Pocket = 1
Price = 7000
Flags = Fossil,Fling_100
Description = A fossil of an ancient Pokémon that lived in the sea. It appears to be part of a shell.
#-------------------------------
[OLDAMBER]
Name = Old Amber
NamePlural = Old Ambers
Pocket = 1
Price = 1000
Flags = Fossil,Fling_100
Description = A piece of amber that contains the genes of an ancient Pokémon. It is clear with a reddish tint.
#-------------------------------
[ROOTFOSSIL]
Name = Root Fossil
NamePlural = Root Fossils
Pocket = 1
Price = 7000
Flags = Fossil,Fling_100
Description = A fossil of an ancient Pokémon that lived in the sea. It appears to be part of a plant root.
#-------------------------------
[CLAWFOSSIL]
Name = Claw Fossil
NamePlural = Claw Fossils
Pocket = 1
Price = 7000
Flags = Fossil,Fling_100
Description = A fossil of an ancient Pokémon that lived in the sea. It appears to be part of a claw.
#-------------------------------
[SKULLFOSSIL]
Name = Skull Fossil
NamePlural = Skull Fossils
Pocket = 1
Price = 7000
Flags = Fossil,Fling_100
Description = A fossil from a prehistoric Pokémon that lived on the land. It appears to be part of a head.
#-------------------------------
[ARMORFOSSIL]
Name = Armor Fossil
NamePlural = Armor Fossils
Pocket = 1
Price = 7000
Flags = Fossil,Fling_100
Description = A fossil from a prehistoric Pokémon that lived on the land. It appears to be part of a collar.
#-------------------------------
[COVERFOSSIL]
Name = Cover Fossil
NamePlural = Cover Fossils
Pocket = 1
Price = 7000
Flags = Fossil,Fling_100
Description = A fossil of an ancient Pokémon that lived in the sea in ancient times. It appears to be part of its back.
#-------------------------------
[PLUMEFOSSIL]
Name = Plume Fossil
NamePlural = Plume Fossils
Pocket = 1
Price = 7000
Flags = Fossil,Fling_100
Description = A fossil of an ancient Pokémon that flew in the sky in ancient times. It appears to be part of its wing.
#-------------------------------
[JAWFOSSIL]
Name = Jaw Fossil
NamePlural = Jaw Fossils
Pocket = 1
Price = 7000
Flags = Fossil,Fling_100
Description = A fossil from a prehistoric Pokémon that once lived on the land. It appears to be part of a large jaw.
#-------------------------------
[SAILFOSSIL]
Name = Sail Fossil
NamePlural = Sail Fossils
Pocket = 1
Price = 7000
Flags = Fossil,Fling_100
Description = A fossil from a prehistoric Pokémon that once lived on the land. It looks like the impression from a skin sail.
#-------------------------------
[FOSSILIZEDBIRD]
Name = Fossilized Bird
NamePlural = Fossilized Birds
Pocket = 1
Price = 5000
Flags = Fling_100
Description = The fossil of an ancient Pokémon that once soared through the sky. What it looked like is a mystery.
#-------------------------------
[FOSSILIZEDFISH]
Name = Fossilized Fish
NamePlural = Fossilized Fishes
Pocket = 1
Price = 5000
Flags = Fling_100
Description = The fossil of an ancient Pokémon that once lived in the sea. What it looked like is a mystery.
#-------------------------------
[FOSSILIZEDDRAKE]
Name = Fossilized Drake
NamePlural = Fossilized Drakes
Pocket = 1
Price = 5000
Flags = Fling_100
Description = The fossil of an ancient Pokémon that once roamed the land. What it looked like is a mystery.
#-------------------------------
[FOSSILIZEDDINO]
Name = Fossilized Dino
NamePlural = Fossilized Dinos
Pocket = 1
Price = 5000
Flags = Fling_100
Description = The fossil of an ancient Pokémon that once lived in the sea. What it looked like is a mystery.
#-------------------------------
[PRETTYFEATHER]
Name = Pretty Feather
NamePlural = Pretty Feathers
Pocket = 1
Price = 1000
Flags = Fling_20
Description = Though this feather is beautiful, it's just a regular feather and has no effect on Pokémon.
#-------------------------------
[TINYMUSHROOM]
Name = Tiny Mushroom
NamePlural = Tiny Mushrooms
Pocket = 1
Price = 500
Flags = Fling_30
Description = A small and rare mushroom. It is sought after by collectors.
#-------------------------------
[BIGMUSHROOM]
Name = Big Mushroom
NamePlural = Big Mushrooms
Pocket = 1
Price = 5000
Flags = Fling_30
Description = A large and rare mushroom. It is sought after by collectors.
#-------------------------------
[BALMMUSHROOM]
Name = Balm Mushroom
NamePlural = Balm Mushrooms
Pocket = 1
Price = 15000
Flags = Fling_30
Description = A rare mushroom which gives off a nice fragrance. A maniac will buy it for a high price.
#-------------------------------
[PEARL]
Name = Pearl
NamePlural = Pearls
Pocket = 1
Price = 2000
Flags = Fling_30
Description = A somewhat-small pearl that sparkles in a pretty silver color. It can be sold cheaply to shops.
#-------------------------------
[BIGPEARL]
Name = Big Pearl
NamePlural = Big Pearls
Pocket = 1
Price = 8000
Flags = Fling_30
Description = A quite-large pearl that sparkles in a pretty silver color. It can be sold at a high price to shops.
#-------------------------------
[PEARLSTRING]
Name = Pearl String
NamePlural = Pearl Strings
Pocket = 1
Price = 20000
Flags = Fling_30
Description = Very large pearls that sparkle in a pretty silver color. A maniac will buy them for a high price.
#-------------------------------
[STARDUST]
Name = Stardust
NamePlural = Stardusts
Pocket = 1
Price = 3000
Flags = Fling_30
Description = Lovely, red-colored sand with a loose, silky feel. It can be sold at a high price to shops.
#-------------------------------
[STARPIECE]
Name = Star Piece
NamePlural = Star Pieces
Pocket = 1
Price = 12000
Flags = Fling_30
Description = A shard of a pretty gem that sparkles in a red color. It can be sold at a high price to shops.
#-------------------------------
[COMETSHARD]
Name = Comet Shard
NamePlural = Comet Shards
Pocket = 1
Price = 25000
Flags = Fling_30
Description = A shard which fell to the ground when a comet approached. A maniac will buy it for a high price.
#-------------------------------
[NUGGET]
Name = Nugget
NamePlural = Nuggets
Pocket = 1
Price = 10000
Flags = Fling_30
Description = A nugget of pure gold that gives off a lustrous gleam. It can be sold at a high price to shops.
#-------------------------------
[BIGNUGGET]
Name = Big Nugget
NamePlural = Big Nuggets
Pocket = 1
Price = 40000
Flags = Fling_30
Description = A big nugget of pure gold that gives off a lustrous gleam. A maniac will buy it for a high price.
#-------------------------------
[HEARTSCALE]
Name = Heart Scale
NamePlural = Heart Scales
Pocket = 1
Price = 100
Flags = Fling_30
Description = A pretty, heart-shaped scale that is extremely rare. It glows faintly in the colors of the rainbow.
#-------------------------------
[SLOWPOKETAIL]
Name = Slowpoke Tail
NamePlural = Slowpoke Tails
Pocket = 1
Price = 10000
Description = A very tasty tail of something. It can be sold at a high price to shops.
#-------------------------------
[RAREBONE]
Name = Rare Bone
NamePlural = Rare Bones
Pocket = 1
Price = 5000
Flags = Fling_100
Description = A bone that is extremely valuable for Pokémon archaeology. It can be sold for a high price to shops.
#-------------------------------
[RELICCOPPER]
Name = Relic Copper
NamePlural = Relic Coppers
Pocket = 1
Price = 0
Flags = Fling_30
Description = A copper coin used in a civilization about 3,000 years ago. A maniac will buy it for a high price.
#-------------------------------
[RELICSILVER]
Name = Relic Silver
NamePlural = Relic Silvers
Pocket = 1
Price = 0
Flags = Fling_30
Description = A silver coin used in a civilization about 3,000 years ago. A maniac will buy it for a high price.
#-------------------------------
[RELICGOLD]
Name = Relic Gold
NamePlural = Relic Golds
Pocket = 1
Price = 60000
Flags = Fling_30
Description = A gold coin used in a civilization about 3,000 years ago. A maniac will buy it for a high price.
#-------------------------------
[RELICVASE]
Name = Relic Vase
NamePlural = Relic Vases
Pocket = 1
Price = 0
Flags = Fling_30
Description = A vase made in a civilization about 3,000 years ago. A maniac will buy it for a high price.
#-------------------------------
[RELICBAND]
Name = Relic Band
NamePlural = Relic Bands
Pocket = 1
Price = 0
Flags = Fling_30
Description = A bracelet made in a civilization about 3,000 years ago. A maniac will buy it for a high price.
#-------------------------------
[RELICSTATUE]
Name = Relic Statue
NamePlural = Relic Statues
Pocket = 1
Price = 0
Flags = Fling_30
Description = A stone figure made in a civilization about 3,000 years ago. A maniac will buy it for a high price.
#-------------------------------
[RELICCROWN]
Name = Relic Crown
NamePlural = Relic Crowns
Pocket = 1
Price = 0
Flags = Fling_30
Description = A crown made in a civilization about 3,000 years ago. A maniac will buy it for a high price.
#-------------------------------
[GROWTHMULCH]
Name = Growth Mulch
NamePlural = Growth Mulch
Pocket = 1
Price = 200
Flags = Mulch,Fling_30
Description = A fertilizer to be spread on soft soil in regions where Berries are grown. A maniac will buy it for a high price.
#-------------------------------
[DAMPMULCH]
Name = Damp Mulch
NamePlural = Damp Mulch
Pocket = 1
Price = 200
Flags = Mulch,Fling_30
Description = A fertilizer to be spread on soft soil in regions where Berries are grown. A maniac will buy it for a high price.
#-------------------------------
[STABLEMULCH]
Name = Stable Mulch
NamePlural = Stable Mulch
Pocket = 1
Price = 200
Flags = Mulch,Fling_30
Description = A fertilizer to be spread on soft soil in regions where Berries are grown. A maniac will buy it for a high price.
#-------------------------------
[GOOEYMULCH]
Name = Gooey Mulch
NamePlural = Gooey Mulch
Pocket = 1
Price = 200
Flags = Mulch,Fling_30
Description = A fertilizer to be spread on soft soil in regions where Berries are grown. A maniac will buy it for a high price.
#-------------------------------
[SHOALSALT]
Name = Shoal Salt
NamePlural = Shoal Salts
Pocket = 1
Price = 20
Flags = Fling_30
Description = Pure salt that can be discovered deep inside the Shoal Cave. A maniac will buy it for a high price.
#-------------------------------
[SHOALSHELL]
Name = Shoal Shell
NamePlural = Shoal Shells
Pocket = 1
Price = 20
Flags = Fling_30
Description = A pretty seashell that can be found deep inside the Shoal Cave. A maniac will buy it for a high price.
#-------------------------------
[ODDKEYSTONE]
Name = Odd Keystone
NamePlural = Odd Keystones
Pocket = 1
Price = 2100
Flags = Fling_80
Description = A vital item that is needed to keep a stone tower from collapsing. Voices can be heard from it occasionally.
#-------------------------------
[REDNECTAR]
Name = Red Nectar
NamePlural = Red Nectars
Pocket = 1
Price = 300
FieldUse = OnPokemon
Flags = Fling_10
Description = A flower nectar obtained at Ula'ula Meadow. It changes the form of certain species of Pokémon.
#-------------------------------
[YELLOWNECTAR]
Name = Yellow Nectar
NamePlural = Yellow Nectars
Pocket = 1
Price = 300
FieldUse = OnPokemon
Flags = Fling_10
Description = A flower nectar obtained at Melemele Meadow. It changes the form of certain species of Pokémon.
#-------------------------------
[PINKNECTAR]
Name = Pink Nectar
NamePlural = Pink Nectars
Pocket = 1
Price = 300
FieldUse = OnPokemon
Flags = Fling_10
Description = A flower nectar obtained from shrubs on Royal Avenue. It changes the form of certain species of Pokémon.
#-------------------------------
[PURPLENECTAR]
Name = Purple Nectar
NamePlural = Purple Nectars
Pocket = 1
Price = 300
FieldUse = OnPokemon
Flags = Fling_10
Description = A flower nectar obtained at Poni Meadow. It changes the form of certain species of Pokémon.
#-------------------------------
[AIRBALLOON]
Name = Air Balloon
NamePlural = Air Balloons
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_10
Description = An item to be held by a Pokémon. The holder will float in the air until hit. Once hit, this item will burst.
#-------------------------------
[BRIGHTPOWDER]
Name = Bright Powder
NamePlural = Bright Powders
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_10
Description = An item to be held by a Pokémon. It casts a tricky glare that lowers the opponent's accuracy.
#-------------------------------
[EVIOLITE]
Name = Eviolite
NamePlural = Eviolites
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_40
Description = A mysterious evolutionary lump. When held, it raises the Defense and Sp. Def if the holder can still evolve.
#-------------------------------
[FLOATSTONE]
Name = Float Stone
NamePlural = Float Stones
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_30
Description = A very light stone. It reduces the weight of a Pokémon when held.
#-------------------------------
[DESTINYKNOT]
Name = Destiny Knot
NamePlural = Destiny Knots
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_10
Description = A long, thin, bright-red string to be held by a Pokémon. If the holder becomes infatuated, so does the foe.
#-------------------------------
[ROCKYHELMET]
Name = Rocky Helmet
NamePlural = Rocky Helmets
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_60
Description = If the holder of this item takes damage, the attacker will also be damaged upon contact.
#-------------------------------
[ASSAULTVEST]
Name = Assault Vest
NamePlural = Assault Vests
Pocket = 1
Price = 1000
Flags = Fling_80
Description = An item to be held by a Pokémon. This offensive vest raises Sp. Def but prevents the use of status moves.
#-------------------------------
[SAFETYGOGGLES]
Name = Safety Goggles
NamePlural = Safety Goggles
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_80
Description = An item to be held by a Pokémon. They protect the holder from weather-related damage and powder.
#-------------------------------
[PROTECTIVEPADS]
Name = Protective Pads
NamePlural = Protective Pads
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_30
Description = An item to be held by a Pokémon. They protect the holder from effects caused by making contact.
#-------------------------------
[HEAVYDUTYBOOTS]
Name = Heavy-Duty Boots
NamePlural = pairs of Heavy-Duty Boots
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_80
Description = These boots prevent the effects of traps set on the battlefield.
#-------------------------------
[UTILITYUMBRELLA]
Name = Utility Umbrella
NamePlural = Utility Umbrellas
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_60
Description = An item to be held by a Pokémon. This sturdy umbrella protects the holder from the effects of rain and sun.
#-------------------------------
[EJECTBUTTON]
Name = Eject Button
NamePlural = Eject Buttons
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_30
Description = If the holder is hit by an attack, it will switch with another Pokémon in your party.
#-------------------------------
[EJECTPACK]
Name = Eject Pack
NamePlural = Eject Packs
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_50
Description = An item to be held by a Pokémon. When the holder's stats are lowered, it will be switched out of battle.
#-------------------------------
[REDCARD]
Name = Red Card
NamePlural = Red Cards
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_10
Description = A card with a mysterious power. When the holder is struck by a foe, the attacker is removed from battle.
#-------------------------------
[SHEDSHELL]
Name = Shed Shell
NamePlural = Shed Shells
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_10
Description = An item to be held by a Pokémon. This discarded carapace lets the holder switch out of battle without fail.
#-------------------------------
[SMOKEBALL]
Name = Smoke Ball
NamePlural = Smoke Balls
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_30
Description = An item to be held by a Pokémon. It enables the holder to flee from any wild Pokémon without fail.
#-------------------------------
[LUCKYEGG]
Name = Lucky Egg
NamePlural = Lucky Eggs
Pocket = 1
Price = 10000
Flags = Fling_30
Description = An item to be held by a Pokémon. It is an egg filled with happiness that earns extra Exp. Points in battle.
#-------------------------------
[EXPSHARE]
Name = Exp. Share
NamePlural = Exp. Shares
Pocket = 1
Price = 3000
Flags = Fling_30
Description = An item to be held by a Pokémon. The holder gets a share of a battle's Exp. Points without battling.
#-------------------------------
[AMULETCOIN]
Name = Amulet Coin
NamePlural = Amulet Coins
Pocket = 1
Price = 10000
Flags = Fling_30
Description = An item to be held by a Pokémon. It doubles a battle's prize money if the holding Pokémon joins in.
#-------------------------------
[SOOTHEBELL]
Name = Soothe Bell
NamePlural = Soothe Bells
Pocket = 1
Price = 4000
Flags = Fling_10
Description = An item to be held by a Pokémon. The comforting chime of this bell calms the holder, making it friendly.
#-------------------------------
[CLEANSETAG]
Name = Cleanse Tag
NamePlural = Cleanse Tags
Pocket = 1
Price = 5000
Flags = Fling_30
Description = An item to be held by a Pokémon. It helps keep wild Pokémon away if the holder is the first one in the party.
#-------------------------------
[CHOICEBAND]
Name = Choice Band
NamePlural = Choice Bands
Pocket = 1
Price = 4000
Flags = Fling_10
Description = An item to be held by a Pokémon. This headband ups Attack, but allows the use of only one move.
#-------------------------------
[CHOICESPECS]
Name = Choice Specs
NamePlural = Choice Specs
Pocket = 1
Price = 4000
Flags = Fling_10
Description = An item to be held by a Pokémon. These curious glasses boost Sp. Atk but allows the use of only one move.
#-------------------------------
[CHOICESCARF]
Name = Choice Scarf
NamePlural = Choice Scarves
Pocket = 1
Price = 4000
Flags = Fling_10
Description = An item to be held by a Pokémon. This scarf boosts Speed but allows the use of only one move.
#-------------------------------
[HEATROCK]
Name = Heat Rock
NamePlural = Heat Rocks
Pocket = 1
Price = 4000
Flags = Fling_60
Description = A Pokémon held item that extends the duration of the move Sunny Day used by the holder.
#-------------------------------
[DAMPROCK]
Name = Damp Rock
NamePlural = Damp Rocks
Pocket = 1
Price = 4000
Flags = Fling_60
Description = A Pokémon held item that extends the duration of the move Rain Dance used by the holder.
#-------------------------------
[SMOOTHROCK]
Name = Smooth Rock
NamePlural = Smooth Rocks
Pocket = 1
Price = 4000
Flags = Fling_10
Description = A Pokémon held item that extends the duration of the move Sandstorm used by the holder.
#-------------------------------
[ICYROCK]
Name = Icy Rock
NamePlural = Icy Rocks
Pocket = 1
Price = 4000
Flags = Fling_40
Description = A Pokémon held item that extends the duration of the move Hail used by the holder.
#-------------------------------
[TERRAINEXTENDER]
Name = Terrain Extender
NamePlural = Terrain Extenders
Pocket = 1
Price = 4000
Flags = Fling_60
Description = An item to be held by a Pokémon. It extends the duration of the terrain caused by the holder.
#-------------------------------
[LIGHTCLAY]
Name = Light Clay
NamePlural = Light Clays
Pocket = 1
Price = 4000
Flags = Fling_30
Description = An item to be held by a Pokémon. Protective moves like Light Screen and Reflect will be effective longer.
#-------------------------------
[GRIPCLAW]
Name = Grip Claw
NamePlural = Grip Claws
Pocket = 1
Price = 4000
Flags = Fling_90
Description = A Pokémon held item that extends the duration of multiturn attacks like Bind and Wrap.
#-------------------------------
[BINDINGBAND]
Name = Binding Band
NamePlural = Binding Bands
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_30
Description = A band that increases the power of binding moves when held.
#-------------------------------
[BIGROOT]
Name = Big Root
NamePlural = Big Roots
Pocket = 1
Price = 4000
Flags = Fling_10
Description = A Pokémon held item that boosts the power of HP-stealing moves to let the holder recover more HP.
#-------------------------------
[BLACKSLUDGE]
Name = Black Sludge
NamePlural = Black Sludges
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_30
Description = A held item that gradually restores the HP of Poison-type Pokémon. It inflicts damage on all other types.
#-------------------------------
[LEFTOVERS]
Name = Leftovers
NamePlural = Leftovers
Pocket = 1
Price = 4000
Flags = Fling_10
Description = An item to be held by a Pokémon. The holder's HP is gradually restored during battle.
#-------------------------------
[SHELLBELL]
Name = Shell Bell
NamePlural = Shell Bells
Pocket = 1
Price = 4000
Flags = Fling_30
Description = An item to be held by a Pokémon. The holder's HP is restored a little every time it inflicts damage.
#-------------------------------
[MENTALHERB]
Name = Mental Herb
NamePlural = Mental Herbs
Pocket = 1
Price = 4000
Flags = Fling_10
Description = An item to be held by a Pokémon. It snaps the holder out of infatuation. It can be used only once.
#-------------------------------
[WHITEHERB]
Name = White Herb
NamePlural = White Herbs
Pocket = 1
Price = 4000
Flags = Fling_10
Description = An item to be held by a Pokémon. It restores any lowered stat in battle. It can be used only once.
#-------------------------------
[POWERHERB]
Name = Power Herb
NamePlural = Power Herbs
Pocket = 1
Price = 4000
Flags = Fling_10
Description = A single-use item to be held by a Pokémon. It allows the immediate use of a move that charges up first.
#-------------------------------
[ABSORBBULB]
Name = Absorb Bulb
NamePlural = Absorb Bulbs
Pocket = 1
Price = 4000
Flags = Fling_30
Description = A consumable bulb. If the holder is hit by a Water-type move, its Sp. Atk will rise.
#-------------------------------
[CELLBATTERY]
Name = Cell Battery
NamePlural = Cell Batteries
Pocket = 1
Price = 4000
Flags = Fling_30
Description = A consumable battery. If the holder is hit by an Electric-type move, its Attack will rise.
#-------------------------------
[LUMINOUSMOSS]
Name = Luminous Moss
NamePlural = Luminous Mosses
Pocket = 1
Price = 4000
Flags = Fling_30
Description = An item to be held by a Pokémon. It boosts Sp. Def if hit by a Water-type attack. It can only be used once.
#-------------------------------
[SNOWBALL]
Name = Snowball
NamePlural = Snowballs
Pocket = 1
Price = 4000
Flags = Fling_30
Description = An item to be held by a Pokémon. It boosts Attack if hit by an Ice-type attack. It can only be used once.
#-------------------------------
[WEAKNESSPOLICY]
Name = Weakness Policy
NamePlural = Weakness Policies
Pocket = 1
Price = 1000
Flags = Fling_80
Description = An item to be held by a Pokémon. The holder's Attack and Sp. Atk sharply increase if hit by a move it's weak to.
#-------------------------------
[BLUNDERPOLICY]
Name = Blunder Policy
NamePlural = Blunder Policies
Pocket = 1
Price = 4000
Flags = Fling_80
Description = Raises Speed sharply when a Pokémon misses with a move because of accuracy.
#-------------------------------
[THROATSPRAY]
Name = Throat Spray
NamePlural = Throat Sprays
Pocket = 1
Price = 4000
Flags = Fling_30
Description = Raises Sp. Atk when a Pokémon uses a sound-based move.
#-------------------------------
[ADRENALINEORB]
Name = Adrenaline Orb
NamePlural = Adrenaline Orbs
Pocket = 1
Price = 300
Flags = Fling_30
Description = An item to be held by a Pokémon. It boosts Speed when intimidated. It can be used only once.
#-------------------------------
[ROOMSERVICE]
Name = Room Service
NamePlural = Room Services
Pocket = 1
Price = 4000
Flags = Fling_100
Description = An item to be held by a Pokémon. Lowers Speed when Trick Room takes effect.
#-------------------------------
[ELECTRICSEED]
Name = Electric Seed
NamePlural = Electric Seeds
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_10
Description = An item to be held by a Pokémon. It boosts Defense on Electric Terrain. It can only be used once.
#-------------------------------
[GRASSYSEED]
Name = Grassy Seed
NamePlural = Grassy Seeds
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_10
Description = An item to be held by a Pokémon. It boosts Defense on Grassy Terrain. It can only be used once.
#-------------------------------
[MISTYSEED]
Name = Misty Seed
NamePlural = Misty Seeds
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_10
Description = An item to be held by a Pokémon. It boosts Sp. Def on Misty Terrain. It can only be used once.
#-------------------------------
[PSYCHICSEED]
Name = Psychic Seed
NamePlural = Psychic Seeds
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_10
Description = An item to be held by a Pokémon. It boosts Sp. Def on Psychic Terrain. It can only be used once.
#-------------------------------
[LIFEORB]
Name = Life Orb
NamePlural = Life Orbs
Pocket = 1
Price = 4000
Flags = Fling_30
Description = An item to be held by a Pokémon. It boosts the power of moves, but at the cost of some HP on each hit.
#-------------------------------
[EXPERTBELT]
Name = Expert Belt
NamePlural = Expert Belts
Pocket = 1
Price = 4000
Flags = Fling_10
Description = An item to be held by a Pokémon. It is a well-worn belt that slightly boosts the power of supereffective moves.
#-------------------------------
[METRONOME]
Name = Metronome
NamePlural = Metronomes
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_30
Description = A Pokémon held item that boosts a move used consecutively. Its effect is reset if another move is used.
#-------------------------------
[MUSCLEBAND]
Name = Muscle Band
NamePlural = Muscle Bands
Pocket = 1
Price = 4000
Flags = Fling_10
Description = An item to be held by a Pokémon. It is a headband that slightly boosts the power of physical moves.
#-------------------------------
[WISEGLASSES]
Name = Wise Glasses
NamePlural = Wise Glasses
Pocket = 1
Price = 4000
Flags = Fling_10
Description = An item to be held by a Pokémon. It is a thick pair of glasses that slightly boosts the power of special moves.
#-------------------------------
[RAZORCLAW]
Name = Razor Claw
NamePlural = Razor Claws
Pocket = 1
Price = 3000
SellPrice = 2500
Flags = Fling_80
Description = An item to be held by a Pokémon. It is a sharply hooked claw that ups the holder's critical-hit ratio.
#-------------------------------
[SCOPELENS]
Name = Scope Lens
NamePlural = Scope Lenses
Pocket = 1
Price = 4000
Flags = Fling_30
Description = An item to be held by a Pokémon. It is a lens that boosts the holder's critical-hit ratio.
#-------------------------------
[WIDELENS]
Name = Wide Lens
NamePlural = Wide Lenses
Pocket = 1
Price = 4000
Flags = Fling_10
Description = An item to be held by a Pokémon. It is a magnifying lens that slightly boosts the accuracy of moves.
#-------------------------------
[ZOOMLENS]
Name = Zoom Lens
NamePlural = Zoom Lenses
Pocket = 1
Price = 4000
Flags = Fling_10
Description = An item to be held by a Pokémon. If the holder moves after its target, its accuracy will be boosted.
#-------------------------------
[KINGSROCK]
Name = King's Rock
NamePlural = King's Rocks
Pocket = 1
Price = 5000
Flags = Fling_30
Description = An item to be held by a Pokémon. When the holder inflicts damage, the target may flinch.
#-------------------------------
[RAZORFANG]
Name = Razor Fang
NamePlural = Razor Fangs
Pocket = 1
Price = 5000
Flags = Fling_30
Description = An item to be held by a Pokémon. It may make foes and allies flinch when the holder inflicts damage.
#-------------------------------
[LAGGINGTAIL]
Name = Lagging Tail
NamePlural = Lagging Tails
Pocket = 1
Price = 4000
Flags = Fling_10
Description = An item to be held by a Pokémon. It is tremendously heavy and makes the holder move slower than usual.
#-------------------------------
[QUICKCLAW]
Name = Quick Claw
NamePlural = Quick Claws
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_80
Description = An item to be held by a Pokémon. A light, sharp claw that lets the bearer move first occasionally.
#-------------------------------
[FOCUSBAND]
Name = Focus Band
NamePlural = Focus Bands
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_10
Description = An item to be held by a Pokémon. The holder may endure a potential KO attack, leaving it with just 1 HP.
#-------------------------------
[FOCUSSASH]
Name = Focus Sash
NamePlural = Focus Sashes
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_10
Description = An item to be held by a Pokémon. If it has full HP, the holder will endure one potential KO attack, leaving 1 HP.
#-------------------------------
[FLAMEORB]
Name = Flame Orb
NamePlural = Flame Orbs
Pocket = 1
Price = 4000
Flags = Fling_30
Description = An item to be held by a Pokémon. It is a bizarre orb that inflicts a burn on the holder in battle.
#-------------------------------
[TOXICORB]
Name = Toxic Orb
NamePlural = Toxic Orbs
Pocket = 1
Price = 4000
Flags = Fling_30
Description = An item to be held by a Pokémon. It is a bizarre orb that badly poisons the holder in battle.
#-------------------------------
[STICKYBARB]
Name = Sticky Barb
NamePlural = Sticky Barbs
Pocket = 1
Price = 4000
Flags = Fling_80
Description = A held item that damages the holder on every turn. It may latch on to Pokémon that touch the holder.
#-------------------------------
[IRONBALL]
Name = Iron Ball
NamePlural = Iron Balls
Pocket = 1
Price = 4000
Flags = Fling_130
Description = A Pokémon held item that cuts Speed. It makes Flying-type and levitating holders susceptible to Ground moves.
#-------------------------------
[RINGTARGET]
Name = Ring Target
NamePlural = Ring Targets
Pocket = 1
Price = 3000
SellPrice = 2000
Flags = Fling_10
Description = Moves that would otherwise have no effect will land on the Pokémon that holds it.
#-------------------------------
[MACHOBRACE]
Name = Macho Brace
NamePlural = Macho Braces
Pocket = 1
Price = 3000
Flags = Fling_60
Description = An item to be held by a Pokémon. It is a stiff, heavy brace that promotes strong growth but lowers Speed.
#-------------------------------
[POWERWEIGHT]
Name = Power Weight
NamePlural = Power Weights
Pocket = 1
Price = 3000
Flags = Fling_70
Description = A Pokémon held item that promotes HP gain on leveling, but reduces the Speed stat.
#-------------------------------
[POWERBRACER]
Name = Power Bracer
NamePlural = Power Bracers
Pocket = 1
Price = 3000
Flags = Fling_70
Description = A Pokémon held item that promotes Attack gain on leveling, but reduces the Speed stat.
#-------------------------------
[POWERBELT]
Name = Power Belt
NamePlural = Power Belts
Pocket = 1
Price = 3000
Flags = Fling_70
Description = A Pokémon held item that promotes Defense gain on leveling, but reduces the Speed stat.
#-------------------------------
[POWERLENS]
Name = Power Lens
NamePlural = Power Lenses
Pocket = 1
Price = 3000
Flags = Fling_70
Description = A Pokémon held item that promotes Sp. Atk gain on leveling, but reduces the Speed stat.
#-------------------------------
[POWERBAND]
Name = Power Band
NamePlural = Power Bands
Pocket = 1
Price = 3000
Flags = Fling_70
Description = A Pokémon held item that promotes Sp. Def gain on leveling, but reduces the Speed stat.
#-------------------------------
[POWERANKLET]
Name = Power Anklet
NamePlural = Power Anklets
Pocket = 1
Price = 3000
Flags = Fling_70
Description = A Pokémon held item that promotes Speed gain on leveling, but reduces the Speed stat.
#-------------------------------
[LAXINCENSE]
Name = Lax Incense
NamePlural = Lax Incenses
Pocket = 1
Price = 5000
Flags = Fling_10
Description = An item to be held by a Pokémon. The tricky aroma of this incense may make attacks miss the holder.
#-------------------------------
[FULLINCENSE]
Name = Full Incense
NamePlural = Full Incenses
Pocket = 1
Price = 5000
Flags = Fling_10
Description = An item to be held by a Pokémon. This exotic-smelling incense makes the holder bloated and slow moving.
#-------------------------------
[LUCKINCENSE]
Name = Luck Incense
NamePlural = Luck Incenses
Pocket = 1
Price = 11000
Flags = Fling_10
Description = An item to be held by a Pokémon. It doubles a battle's prize money if the holding Pokémon joins in.
#-------------------------------
[PUREINCENSE]
Name = Pure Incense
NamePlural = Pure Incenses
Pocket = 1
Price = 6000
Flags = Fling_10
Description = An item to be held by a Pokémon. It helps keep wild Pokémon away if the holder is the first one in the party.
#-------------------------------
[SEAINCENSE]
Name = Sea Incense
NamePlural = Sea Incenses
Pocket = 1
Price = 2000
Flags = Fling_10
Description = An item to be held by a Pokémon. It has a curious aroma that boosts the power of Water-type moves.
#-------------------------------
[WAVEINCENSE]
Name = Wave Incense
NamePlural = Wave Incenses
Pocket = 1
Price = 2000
Flags = Fling_10
Description = An item to be held by a Pokémon. It has a curious aroma that boosts the power of Water-type moves.
#-------------------------------
[ROSEINCENSE]
Name = Rose Incense
NamePlural = Rose Incenses
Pocket = 1
Price = 2000
Flags = Fling_10
Description = An item to be held by a Pokémon. This exotic-smelling incense boosts the power of Grass-type moves.
#-------------------------------
[ODDINCENSE]
Name = Odd Incense
NamePlural = Odd Incenses
Pocket = 1
Price = 2000
Flags = Fling_10
Description = An item to be held by a Pokémon. This exotic-smelling incense boosts the power of Psychic-type moves.
#-------------------------------
[ROCKINCENSE]
Name = Rock Incense
NamePlural = Rock Incenses
Pocket = 1
Price = 2000
Flags = Fling_10
Description = An item to be held by a Pokémon. This exotic-smelling incense boosts the power of Rock-type moves.
#-------------------------------
[CHARCOAL]
Name = Charcoal
NamePlural = Charcoals
Pocket = 1
Price = 3000
SellPrice = 500
Flags = Fling_30
Description = An item to be held by a Pokémon. It is a combustible fuel that boosts the power of Fire-type moves.
#-------------------------------
[MYSTICWATER]
Name = Mystic Water
NamePlural = Mystic Waters
Pocket = 1
Price = 3000
SellPrice = 500
Flags = Fling_30
Description = An item to be held by a Pokémon. It is a teardrop-shaped gem that ups the power of Water-type moves.
#-------------------------------
[MAGNET]
Name = Magnet
NamePlural = Magnets
Pocket = 1
Price = 3000
SellPrice = 500
Flags = Fling_30
Description = An item to be held by a Pokémon. It is a powerful magnet that boosts the power of Electric-type moves.
#-------------------------------
[MIRACLESEED]
Name = Miracle Seed
NamePlural = Miracle Seeds
Pocket = 1
Price = 3000
SellPrice = 500
Flags = Fling_30
Description = An item to be held by a Pokémon. It is a seed imbued with life that ups the power of Grass-type moves.
#-------------------------------
[NEVERMELTICE]
Name = Never-Melt Ice
NamePlural = Never-Melt Ices
Pocket = 1
Price = 3000
SellPrice = 500
Flags = Fling_30
Description = An item to be held by a Pokémon. It is a piece of ice that repels heat and boosts Ice-type moves.
#-------------------------------
[BLACKBELT]
Name = Black Belt
NamePlural = Black Belts
Pocket = 1
Price = 3000
SellPrice = 500
Flags = Fling_30
Description = An item to be held by a Pokémon. It is a belt that boosts determination and Fighting-type moves.
#-------------------------------
[POISONBARB]
Name = Poison Barb
NamePlural = Poison Barbs
Pocket = 1
Price = 3000
SellPrice = 500
Flags = Fling_70
Description = An item to be held by a Pokémon. It is a small, poisonous barb that ups the power of Poison-type moves.
#-------------------------------
[SOFTSAND]
Name = Soft Sand
NamePlural = Soft Sand
Pocket = 1
Price = 3000
SellPrice = 500
Flags = Fling_10
Description = An item to be held by a Pokémon. It is a loose, silky sand that boosts the power of Ground-type moves.
#-------------------------------
[SHARPBEAK]
Name = Sharp Beak
NamePlural = Sharp Beaks
Pocket = 1
Price = 3000
SellPrice = 500
Flags = Fling_50
Description = An item to be held by a Pokémon. It is a long, sharp beak that boosts the power of Flying-type moves.
#-------------------------------
[TWISTEDSPOON]
Name = Twisted Spoon
NamePlural = Twisted Spoons
Pocket = 1
Price = 3000
SellPrice = 500
Flags = Fling_30
Description = An item to be held by a Pokémon. It is a spoon imbued with telekinetic power that boosts Psychic-type moves.
#-------------------------------
[SILVERPOWDER]
Name = Silver Powder
NamePlural = Silver Powders
Pocket = 1
Price = 3000
SellPrice = 500
Flags = Fling_10
Description = An item to be held by a Pokémon. It is a shiny, silver powder that ups the power of Bug-type moves.
#-------------------------------
[HARDSTONE]
Name = Hard Stone
NamePlural = Hard Stones
Pocket = 1
Price = 3000
SellPrice = 500
Flags = Fling_100
Description = An item to be held by a Pokémon. It is an unbreakable stone that ups the power of Rock-type moves.
#-------------------------------
[SPELLTAG]
Name = Spell Tag
NamePlural = Spell Tags
Pocket = 1
Price = 3000
SellPrice = 500
Flags = Fling_30
Description = An item to be held by a Pokémon. It is a sinister, eerie tag that boosts the power of Ghost-type moves.
#-------------------------------
[DRAGONFANG]
Name = Dragon Fang
NamePlural = Dragon Fangs
Pocket = 1
Price = 3000
SellPrice = 500
Flags = Fling_70
Description = An item to be held by a Pokémon. It is a hard and sharp fang that ups the power of Dragon-type moves.
#-------------------------------
[BLACKGLASSES]
Name = Black Glasses
NamePlural = Black Glasses
Pocket = 1
Price = 3000
SellPrice = 500
Flags = Fling_30
Description = An item to be held by a Pokémon. It is a shady-looking pair of glasses that boosts Dark-type moves.
#-------------------------------
[METALCOAT]
Name = Metal Coat
NamePlural = Metal Coats
Pocket = 1
Price = 3000
SellPrice = 1000
Flags = Fling_30
Description = An item to be held by a Pokémon. It is a special metallic film that ups the power of Steel-type moves.
#-------------------------------
[SILKSCARF]
Name = Silk Scarf
NamePlural = Silk Scarves
Pocket = 1
Price = 3000
SellPrice = 500
Flags = Fling_10
Description = An item to be held by a Pokémon. It is a sumptuous scarf that boosts the power of Normal-type moves.
#-------------------------------
[FLAMEPLATE]
Name = Flame Plate
NamePlural = Flame Plates
Pocket = 1
Price = 10000
SellPrice = 500
Flags = Fling_90
Description = An item to be held by a Pokémon. It is a stone tablet that boosts the power of Fire-type moves.
#-------------------------------
[SPLASHPLATE]
Name = Splash Plate
NamePlural = Splash Plates
Pocket = 1
Price = 10000
SellPrice = 500
Flags = Fling_90
Description = An item to be held by a Pokémon. It is a stone tablet that boosts the power of Water-type moves.
#-------------------------------
[ZAPPLATE]
Name = Zap Plate
NamePlural = Zap Plates
Pocket = 1
Price = 10000
SellPrice = 500
Flags = Fling_90
Description = An item to be held by a Pokémon. It is a stone tablet that boosts the power of Electric-type moves.
#-------------------------------
[MEADOWPLATE]
Name = Meadow Plate
NamePlural = Meadow Plates
Pocket = 1
Price = 10000
SellPrice = 500
Flags = Fling_90
Description = An item to be held by a Pokémon. It is a stone tablet that boosts the power of Grass-type moves.
#-------------------------------
[ICICLEPLATE]
Name = Icicle Plate
NamePlural = Icicle Plates
Pocket = 1
Price = 10000
SellPrice = 500
Flags = Fling_90
Description = An item to be held by a Pokémon. It is a stone tablet that boosts the power of Ice-type moves.
#-------------------------------
[FISTPLATE]
Name = Fist Plate
NamePlural = Fist Plates
Pocket = 1
Price = 10000
SellPrice = 500
Flags = Fling_90
Description = An item to be held by a Pokémon. It is a stone tablet that boosts the power of Fighting-type moves.
#-------------------------------
[TOXICPLATE]
Name = Toxic Plate
NamePlural = Toxic Plates
Pocket = 1
Price = 10000
SellPrice = 500
Flags = Fling_90
Description = An item to be held by a Pokémon. It is a stone tablet that boosts the power of Poison-type moves.
#-------------------------------
[EARTHPLATE]
Name = Earth Plate
NamePlural = Earth Plates
Pocket = 1
Price = 10000
SellPrice = 500
Flags = Fling_90
Description = An item to be held by a Pokémon. It is a stone tablet that boosts the power of Ground-type moves.
#-------------------------------
[SKYPLATE]
Name = Sky Plate
NamePlural = Sky Plates
Pocket = 1
Price = 10000
SellPrice = 500
Flags = Fling_90
Description = An item to be held by a Pokémon. It is a stone tablet that boosts the power of Flying-type moves.
#-------------------------------
[MINDPLATE]
Name = Mind Plate
NamePlural = Mind Plates
Pocket = 1
Price = 10000
SellPrice = 500
Flags = Fling_90
Description = An item to be held by a Pokémon. It is a stone tablet that boosts the power of Psychic-type moves.
#-------------------------------
[INSECTPLATE]
Name = Insect Plate
NamePlural = Insect Plates
Pocket = 1
Price = 10000
SellPrice = 500
Flags = Fling_90
Description = An item to be held by a Pokémon. It is a stone tablet that boosts the power of Bug-type moves.
#-------------------------------
[STONEPLATE]
Name = Stone Plate
NamePlural = Stone Plates
Pocket = 1
Price = 10000
SellPrice = 500
Flags = Fling_90
Description = An item to be held by a Pokémon. It is a stone tablet that boosts the power of Rock-type moves.
#-------------------------------
[SPOOKYPLATE]
Name = Spooky Plate
NamePlural = Spooky Plates
Pocket = 1
Price = 10000
SellPrice = 500
Flags = Fling_90
Description = An item to be held by a Pokémon. It is a stone tablet that boosts the power of Ghost-type moves.
#-------------------------------
[DRACOPLATE]
Name = Draco Plate
NamePlural = Draco Plates
Pocket = 1
Price = 10000
SellPrice = 500
Flags = Fling_90
Description = An item to be held by a Pokémon. It is a stone tablet that boosts the power of Dragon-type moves.
#-------------------------------
[DREADPLATE]
Name = Dread Plate
NamePlural = Dread Plates
Pocket = 1
Price = 10000
SellPrice = 500
Flags = Fling_90
Description = An item to be held by a Pokémon. It is a stone tablet that boosts the power of Dark-type moves.
#-------------------------------
[IRONPLATE]
Name = Iron Plate
NamePlural = Iron Plates
Pocket = 1
Price = 10000
SellPrice = 500
Flags = Fling_90
Description = An item to be held by a Pokémon. It is a stone tablet that boosts the power of Steel-type moves.
#-------------------------------
[PIXIEPLATE]
Name = Pixie Plate
NamePlural = Pixie Plates
Pocket = 1
Price = 10000
SellPrice = 500
Flags = Fling_90
Description = An item to be held by a Pokémon. It is a stone tablet that boosts the power of Fairy-type moves.
#-------------------------------
[FIREGEM]
Name = Fire Gem
NamePlural = Fire Gems
Pocket = 1
Price = 4000
Flags = TypeGem
Description = A gem with an essence of fire. When held, it strengthens the power of a Fire-type move only once.
#-------------------------------
[WATERGEM]
Name = Water Gem
NamePlural = Water Gems
Pocket = 1
Price = 4000
Flags = TypeGem
Description = A gem with an essence of water. When held, it strengthens the power of a Water-type move only once.
#-------------------------------
[ELECTRICGEM]
Name = Electric Gem
NamePlural = Electric Gems
Pocket = 1
Price = 4000
Flags = TypeGem
Description = A gem with an essence of electricity. When held, it strengthens the power of an Electric-type move only once.
#-------------------------------
[GRASSGEM]
Name = Grass Gem
NamePlural = Grass Gems
Pocket = 1
Price = 4000
Flags = TypeGem
Description = A gem with an essence of nature. When held, it strengthens the power of a Grass-type move only once.
#-------------------------------
[ICEGEM]
Name = Ice Gem
NamePlural = Ice Gems
Pocket = 1
Price = 4000
Flags = TypeGem
Description = A gem with an essence of ice. When held, it strengthens the power of an Ice-type move only once.
#-------------------------------
[FIGHTINGGEM]
Name = Fighting Gem
NamePlural = Fighting Gems
Pocket = 1
Price = 4000
Flags = TypeGem
Description = A gem with an essence of combat. When held, it strengthens the power of a Fighting-type move only once.
#-------------------------------
[POISONGEM]
Name = Poison Gem
NamePlural = Poison Gems
Pocket = 1
Price = 4000
Flags = TypeGem
Description = A gem with an essence of poison. When held, it strengthens the power of a Poison-type move only once.
#-------------------------------
[GROUNDGEM]
Name = Ground Gem
NamePlural = Ground Gems
Pocket = 1
Price = 4000
Flags = TypeGem
Description = A gem with an essence of land. When held, it strengthens the power of a Ground-type move only once.
#-------------------------------
[FLYINGGEM]
Name = Flying Gem
NamePlural = Flying Gems
Pocket = 1
Price = 4000
Flags = TypeGem
Description = A gem with an essence of air. When held, it strengthens the power of a Flying-type move only once.
#-------------------------------
[PSYCHICGEM]
Name = Psychic Gem
NamePlural = Psychic Gems
Pocket = 1
Price = 4000
Flags = TypeGem
Description = A gem with an essence of the mind. When held, it strengthens the power of a Psychic-type move only once.
#-------------------------------
[BUGGEM]
Name = Bug Gem
NamePlural = Bug Gems
Pocket = 1
Price = 4000
Flags = TypeGem
Description = A gem with an insect-like essence. When held, it strengthens the power of a Bug-type move only once.
#-------------------------------
[ROCKGEM]
Name = Rock Gem
NamePlural = Rock Gems
Pocket = 1
Price = 4000
Flags = TypeGem
Description = A gem with an essence of rock. When held, it strengthens the power of a Rock-type move only once.
#-------------------------------
[GHOSTGEM]
Name = Ghost Gem
NamePlural = Ghost Gems
Pocket = 1
Price = 4000
Flags = TypeGem
Description = A gem with a spectral essence. When held, it strengthens the power of a Ghost-type move only once.
#-------------------------------
[DRAGONGEM]
Name = Dragon Gem
NamePlural = Dragon Gems
Pocket = 1
Price = 4000
Flags = TypeGem
Description = A gem with a draconic essence. When held, it strengthens the power of a Dragon-type move only once.
#-------------------------------
[DARKGEM]
Name = Dark Gem
NamePlural = Dark Gems
Pocket = 1
Price = 4000
Flags = TypeGem
Description = A gem with an essence of darkness. When held, it strengthens the power of a Dark-type move only once.
#-------------------------------
[STEELGEM]
Name = Steel Gem
NamePlural = Steel Gems
Pocket = 1
Price = 4000
Flags = TypeGem
Description = A gem with an essence of steel. When held, it strengthens the power of a Steel-type move only once.
#-------------------------------
[FAIRYGEM]
Name = Fairy Gem
NamePlural = Fairy Gems
Pocket = 1
Price = 4000
Flags = TypeGem
Description = A gem with an essence of the fey. When held, it strengthens the power of a Fairy-type move only once.
#-------------------------------
[NORMALGEM]
Name = Normal Gem
NamePlural = Normal Gems
Pocket = 1
Price = 4000
Flags = TypeGem
Description = A gem with an ordinary essence. When held, it strengthens the power of a Normal-type move only once.
#-------------------------------
[LIGHTBALL]
Name = Light Ball
NamePlural = Light Balls
Pocket = 1
Price = 1000
Flags = Fling_30
Description = An item to be held by Pikachu. It is a puzzling orb that raises the Attack and Sp. Atk stat.
#-------------------------------
[LUCKYPUNCH]
Name = Lucky Punch
NamePlural = Lucky Punches
Pocket = 1
Price = 1000
Flags = Fling_40
Description = An item to be held by Chansey. It is a pair of gloves that boosts Chansey's critical-hit ratio.
#-------------------------------
[METALPOWDER]
Name = Metal Powder
NamePlural = Metal Powders
Pocket = 1
Price = 1000
Flags = Fling_10
Description = An item to be held by Ditto. Extremely fine yet hard, this odd powder boosts the Defense stat.
#-------------------------------
[QUICKPOWDER]
Name = Quick Powder
NamePlural = Quick Powders
Pocket = 1
Price = 1000
Flags = Fling_10
Description = An item to be held by Ditto. Extremely fine yet hard, this odd powder boosts the Speed stat.
#-------------------------------
[THICKCLUB]
Name = Thick Club
NamePlural = Thick Clubs
Pocket = 1
Price = 1000
Flags = Fling_90
Description = An item to be held by Cubone or Marowak. It is a hard bone of some sort that boosts the Attack stat.
#-------------------------------
[LEEK]
Name = Leek
NamePlural = Leeks
Pocket = 1
Price = 1000
Flags = Fling_60
Description = An item to be held by Farfetch'd. It is a very long and stiff stalk of leek that boosts the critical-hit ratio.
#-------------------------------
[SOULDEW]
Name = Soul Dew
NamePlural = Soul Dews
Pocket = 1
Price = 0
Flags = Fling_30
Description = A wondrous orb to be held by either Latios or Latias. It raises the power of Psychic- and Dragon-type moves.
#-------------------------------
[DEEPSEATOOTH]
Name = Deep Sea Tooth
NamePlural = Deep Sea Teeth
Pocket = 1
Price = 2000
Flags = Fling_90
Description = An item to be held by Clamperl. A fang that gleams a sharp silver, it raises the Sp. Atk stat.
#-------------------------------
[DEEPSEASCALE]
Name = Deep Sea Scale
NamePlural = Deep Sea Scales
Pocket = 1
Price = 2000
Flags = Fling_30
Description = An item to be held by Clamperl. A scale that shines a faint pink, it raises the Sp. Def stat.
#-------------------------------
[ADAMANTORB]
Name = Adamant Orb
NamePlural = Adamant Orbs
Pocket = 1
Price = 10000
SellPrice = 0
Flags = Fling_60
Description = A brightly gleaming orb to be held by Dialga. It boosts the power of Dragon- and Steel-type moves.
#-------------------------------
[LUSTROUSORB]
Name = Lustrous Orb
NamePlural = Lustrous Orbs
Pocket = 1
Price = 10000
SellPrice = 0
Flags = Fling_60
Description = A beautifully glowing orb to be held by Palkia. It boosts the power of Dragon- and Water-type moves.
#-------------------------------
[GRISEOUSORB]
Name = Griseous Orb
NamePlural = Griseous Orbs
Pocket = 1
Price = 10000
SellPrice = 0
Flags = Fling_60
Description = A glowing orb to be held by Giratina. It boosts the power of Dragon- and Ghost-type moves.
#-------------------------------
[RUSTEDSWORD]
Name = Rusted Sword
NamePlural = Rusted Swords
Pocket = 1
Price = 0
Description = It is said that a hero used this sword to halt a disaster in ancient times. But it's grown rusty and worn.
#-------------------------------
[RUSTEDSHIELD]
Name = Rusted Shield
NamePlural = Rusted Shields
Pocket = 1
Price = 0
Description = It is said that a hero used this shield to halt a disaster in ancient times. But it's grown rusty and worn.
#-------------------------------
[DOUSEDRIVE]
Name = Douse Drive
NamePlural = Douse Drives
Pocket = 1
Price = 0
Flags = Fling_70
Description = A cassette to be held by Genesect. It changes Techno Blast to a Water-type move.
#-------------------------------
[SHOCKDRIVE]
Name = Shock Drive
NamePlural = Shock Drives
Pocket = 1
Price = 0
Flags = Fling_70
Description = A cassette to be held by Genesect. It changes Techno Blast to an Electric-type move.
#-------------------------------
[BURNDRIVE]
Name = Burn Drive
NamePlural = Burn Drives
Pocket = 1
Price = 0
Flags = Fling_70
Description = A cassette to be held by Genesect. It changes Techno Blast to a Fire-type move.
#-------------------------------
[CHILLDRIVE]
Name = Chill Drive
NamePlural = Chill Drives
Pocket = 1
Price = 0
Flags = Fling_70
Description = A cassette to be held by Genesect. It changes Techno Blast to an Ice-type move.
#-------------------------------
[FIREMEMORY]
Name = Fire Memory
NamePlural = Fire Memories
Pocket = 1
Price = 1000
Flags = Fling_50
Description = A memory disc containing Fire-type data. It changes the holder's type if held by a certain Pokémon.
#-------------------------------
[WATERMEMORY]
Name = Water Memory
NamePlural = Water Memories
Pocket = 1
Price = 1000
Flags = Fling_50
Description = A memory disc containing Water-type data. It changes the holder's type if held by a certain Pokémon.
#-------------------------------
[ELECTRICMEMORY]
Name = Electric Memory
NamePlural = Electric Memories
Pocket = 1
Price = 1000
Flags = Fling_50
Description = A memory disc containing Electric-type data. It changes the holder's type if held by a certain Pokémon.
#-------------------------------
[GRASSMEMORY]
Name = Grass Memory
NamePlural = Grass Memories
Pocket = 1
Price = 1000
Flags = Fling_50
Description = A memory disc containing Grass-type data. It changes the holder's type if held by a certain Pokémon.
#-------------------------------
[ICEMEMORY]
Name = Ice Memory
NamePlural = Ice Memories
Pocket = 1
Price = 1000
Flags = Fling_50
Description = A memory disc containing Ice-type data. It changes the holder's type if held by a certain Pokémon.
#-------------------------------
[FIGHTINGMEMORY]
Name = Fighting Memory
NamePlural = Fighting Memories
Pocket = 1
Price = 1000
Flags = Fling_50
Description = A memory disc containing Fighting-type data. It changes the holder's type if held by a certain Pokémon.
#-------------------------------
[POISONMEMORY]
Name = Poison Memory
NamePlural = Poison Memories
Pocket = 1
Price = 1000
Flags = Fling_50
Description = A memory disc containing Poison-type data. It changes the holder's type if held by a certain Pokémon.
#-------------------------------
[GROUNDMEMORY]
Name = Ground Memory
NamePlural = Ground Memories
Pocket = 1
Price = 1000
Flags = Fling_50
Description = A memory disc containing Ground-type data. It changes the holder's type if held by a certain Pokémon.
#-------------------------------
[FLYINGMEMORY]
Name = Flying Memory
NamePlural = Flying Memories
Pocket = 1
Price = 1000
Flags = Fling_50
Description = A memory disc containing Flying-type data. It changes the holder's type if held by a certain Pokémon.
#-------------------------------
[PSYCHICMEMORY]
Name = Psychic Memory
NamePlural = Psychic Memories
Pocket = 1
Price = 1000
Flags = Fling_50
Description = A memory disc containing Psychic-type data. It changes the holder's type if held by a certain Pokémon.
#-------------------------------
[BUGMEMORY]
Name = Bug Memory
NamePlural = Bug Memories
Pocket = 1
Price = 1000
Flags = Fling_50
Description = A memory disc containing Bug-type data. It changes the holder's type if held by a certain Pokémon.
#-------------------------------
[ROCKMEMORY]
Name = Rock Memory
NamePlural = Rock Memories
Pocket = 1
Price = 1000
Flags = Fling_50
Description = A memory disc containing Rock-type data. It changes the holder's type if held by a certain Pokémon.
#-------------------------------
[GHOSTMEMORY]
Name = Ghost Memory
NamePlural = Ghost Memories
Pocket = 1
Price = 1000
Flags = Fling_50
Description = A memory disc containing Ghost-type data. It changes the holder's type if held by a certain Pokémon.
#-------------------------------
[DRAGONMEMORY]
Name = Dragon Memory
NamePlural = Dragon Memories
Pocket = 1
Price = 1000
Flags = Fling_50
Description = A memory disc containing Dragon-type data. It changes the holder's type if held by a certain Pokémon.
#-------------------------------
[DARKMEMORY]
Name = Dark Memory
NamePlural = Dark Memories
Pocket = 1
Price = 1000
Flags = Fling_50
Description = A memory disc containing Dark-type data. It changes the holder's type if held by a certain Pokémon.
#-------------------------------
[STEELMEMORY]
Name = Steel Memory
NamePlural = Steel Memories
Pocket = 1
Price = 1000
Flags = Fling_50
Description = A memory disc containing Steel-type data. It changes the holder's type if held by a certain Pokémon.
#-------------------------------
[FAIRYMEMORY]
Name = Fairy Memory
NamePlural = Fairy Memories
Pocket = 1
Price = 1000
Flags = Fling_50
Description = A memory disc containing Fairy-type data. It changes the holder's type if held by a certain Pokémon.
#-------------------------------
[EVERSTONE]
Name = Everstone
NamePlural = Everstones
Pocket = 1
Price = 3000
Flags = Fling_30
Description = An item to be held by a Pokémon. The Pokémon holding this peculiar stone is prevented from evolving.
#-------------------------------
[DRAGONSCALE]
Name = Dragon Scale
NamePlural = Dragon Scales
Pocket = 1
Price = 3000
SellPrice = 1000
Flags = Fling_30
Description = A thick and tough scale. Dragon-type Pokémon may be holding this item when caught.
#-------------------------------
[UPGRADE]
Name = Upgrade
NamePlural = Upgrades
Pocket = 1
Price = 3000
SellPrice = 1000
Flags = Fling_30
Description = A transparent device filled with all sorts of data. It was produced by Silph Co.
#-------------------------------
[DUBIOUSDISC]
Name = Dubious Disc
NamePlural = Dubious Discs
Pocket = 1
Price = 3000
SellPrice = 1000
Flags = Fling_50
Description = A transparent device overflowing with dubious data.  Its producer is unknown.
#-------------------------------
[PROTECTOR]
Name = Protector
NamePlural = Protectors
Pocket = 1
Price = 3000
SellPrice = 1000
Flags = Fling_80
Description = A protective item of some sort. It is extremely stiff and heavy. It is loved by a certain Pokémon.
#-------------------------------
[ELECTIRIZER]
Name = Electirizer
NamePlural = Electirizers
Pocket = 1
Price = 3000
SellPrice = 1000
Flags = Fling_80
Description = A box packed with a tremendous amount of electric energy. It is loved by a certain Pokémon.
#-------------------------------
[MAGMARIZER]
Name = Magmarizer
NamePlural = Magmarizers
Pocket = 1
Price = 3000
SellPrice = 1000
Flags = Fling_80
Description = A box packed with a tremendous amount of magma energy. It is loved by a certain Pokémon.
#-------------------------------
[REAPERCLOTH]
Name = Reaper Cloth
NamePlural = Reaper Cloths
Pocket = 1
Price = 3000
SellPrice = 1000
Flags = Fling_10
Description = A cloth imbued with horrifyingly strong spiritual energy. It is loved by a certain Pokémon.
#-------------------------------
[PRISMSCALE]
Name = Prism Scale
NamePlural = Prism Scales
Pocket = 1
Price = 3000
SellPrice = 1000
Flags = Fling_30
Description = A mysterious scale that evolves certain Pokémon. It shines in rainbow colors.
#-------------------------------
[OVALSTONE]
Name = Oval Stone
NamePlural = Oval Stones
Pocket = 1
Price = 3000
SellPrice = 1000
Flags = Fling_80
Description = A peculiar stone that makes certain species of Pokémon evolve. It is shaped like an egg.
#-------------------------------
[WHIPPEDDREAM]
Name = Whipped Dream
NamePlural = Whipped Dreams
Pocket = 1
Price = 3000
SellPrice = 1000
Flags = Fling_80
Description = A soft and sweet treat made of fluffy, puffy and whirled cream. It's loved by a certain Pokémon.
#-------------------------------
[SACHET]
Name = Sachet
NamePlural = Sachets
Pocket = 1
Price = 3000
SellPrice = 1000
Flags = Fling_80
Description = A sachet filled with slightly overwhelming fragrant perfumes. Yet it's loved by a certain Pokémon.
#-------------------------------
[STRAWBERRYSWEET]
Name = Strawberry Sweet
NamePlural = Strawberry Sweets
Pocket = 1
Price = 500
Flags = Fling_10
Description = A strawberry-shaped sweet. When a Milcery holds this, it will spin around happily.
#-------------------------------
[LOVESWEET]
Name = Love Sweet
NamePlural = Love Sweets
Pocket = 1
Price = 500
Flags = Fling_10
Description = A heart-shaped sweet. When a Milcery holds this, it spins around happily.
#-------------------------------
[BERRYSWEET]
Name = Berry Sweet
NamePlural = Berry Sweets
Pocket = 1
Price = 500
Flags = Fling_10
Description = A berry-shaped sweet. When a Milcery holds this, it spins around happily.
#-------------------------------
[CLOVERSWEET]
Name = Clover Sweet
NamePlural = Clover Sweets
Pocket = 1
Price = 500
Flags = Fling_10
Description = A clover-shaped sweet. When a Milcery holds this, it spins around happily.
#-------------------------------
[FLOWERSWEET]
Name = Flower Sweet
NamePlural = Flower Sweets
Pocket = 1
Price = 500
Flags = Fling_10
Description = A flower-shaped sweet. When a Milcery holds this, it spins around happily.
#-------------------------------
[STARSWEET]
Name = Star Sweet
NamePlural = Star Sweets
Pocket = 1
Price = 500
Flags = Fling_10
Description = A star-shaped sweet. When a Milcery holds this, it spins around happily.
#-------------------------------
[RIBBONSWEET]
Name = Ribbon Sweet
NamePlural = Ribbon Sweets
Pocket = 1
Price = 500
Flags = Fling_10
Description = A ribbon-shaped sweet. When a Milcery holds this, it spins around happily.
#-------------------------------
[VENUSAURITE]
Name = Venusaurite
NamePlural = Venusaurites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Venusaur hold it, and it will be able to Mega Evolve.
#-------------------------------
[CHARIZARDITEX]
Name = Charizardite X
NamePlural = Charizardite Xs
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Charizard hold it, and it will be able to Mega Evolve.
#-------------------------------
[CHARIZARDITEY]
Name = Charizardite Y
NamePlural = Charizardite Ys
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Charizard hold it, and it will be able to Mega Evolve.
#-------------------------------
[BLASTOISINITE]
Name = Blastoisinite
NamePlural = Blastoisinites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Blastoise hold it, and it will be able to Mega Evolve.
#-------------------------------
[BEEDRILLITE]
Name = Beedrillite
NamePlural = Beedrillites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Beedrill hold it, and it will be able to Mega Evolve.
#-------------------------------
[PIDGEOTITE]
Name = Pidgeotite
NamePlural = Pidgeotites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Pidgeot hold it, and it will be able to Mega Evolve.
#-------------------------------
[ALAKAZITE]
Name = Alakazite
NamePlural = Alakazites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Alakazam hold it, and it will be able to Mega Evolve.
#-------------------------------
[SLOWBRONITE]
Name = Slowbronite
NamePlural = Slowbronites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Slowbro hold it, and it will be able to Mega Evolve.
#-------------------------------
[GENGARITE]
Name = Gengarite
NamePlural = Gengarites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Gengar hold it, and it will be able to Mega Evolve.
#-------------------------------
[KANGASKHANITE]
Name = Kangaskhanite
NamePlural = Kangaskhanites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Kangaskhan hold it, and it will be able to Mega Evolve.
#-------------------------------
[PINSIRITE]
Name = Pinsirite
NamePlural = Pinsirites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Pinsir hold it, and it will be able to Mega Evolve.
#-------------------------------
[GYARADOSITE]
Name = Gyaradosite
NamePlural = Gyaradosites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Gyarados hold it, and it will be able to Mega Evolve.
#-------------------------------
[AERODACTYLITE]
Name = Aerodactylite
NamePlural = Aerodactylites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Aerodactyl hold it, and it will be able to Mega Evolve.
#-------------------------------
[MEWTWONITEX]
Name = Mewtwonite X
NamePlural = Mewtwonite Xs
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Mewtwo hold it, and it will be able to Mega Evolve.
#-------------------------------
[MEWTWONITEY]
Name = Mewtwonite Y
NamePlural = Mewtwonite Ys
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Mewtwo hold it, and it will be able to Mega Evolve.
#-------------------------------
[AMPHAROSITE]
Name = Ampharosite
NamePlural = Ampharosites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Ampharos hold it, and it will be able to Mega Evolve.
#-------------------------------
[STEELIXITE]
Name = Steelixite
NamePlural = Steelixites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Steelix hold it, and it will be able to Mega Evolve.
#-------------------------------
[SCIZORITE]
Name = Scizorite
NamePlural = Scizorites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Scizor hold it, and it will be able to Mega Evolve.
#-------------------------------
[HERACRONITE]
Name = Heracronite
NamePlural = Heracronites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Heracross hold it, and it will be able to Mega Evolve.
#-------------------------------
[HOUNDOOMINITE]
Name = Houndoominite
NamePlural = Houndoominites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Houndoom hold it, and it will be able to Mega Evolve.
#-------------------------------
[TYRANITARITE]
Name = Tyranitarite
NamePlural = Tyranitarites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Tyranitar hold it, and it will be able to Mega Evolve.
#-------------------------------
[SCEPTILITE]
Name = Sceptilite
NamePlural = Sceptilites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Sceptile hold it, and it will be able to Mega Evolve.
#-------------------------------
[BLAZIKENITE]
Name = Blazikenite
NamePlural = Blazikenites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Blaziken hold it, and it will be able to Mega Evolve.
#-------------------------------
[SWAMPERTITE]
Name = Swampertite
NamePlural = Swampertites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Swampert hold it, and it will be able to Mega Evolve.
#-------------------------------
[GARDEVOIRITE]
Name = Gardevoirite
NamePlural = Gardevoirites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Gardevoir hold it, and it will be able to Mega Evolve.
#-------------------------------
[SABLENITE]
Name = Sablenite
NamePlural = Sablenites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Sableye hold it, and it will be able to Mega Evolve.
#-------------------------------
[MAWILITE]
Name = Mawilite
NamePlural = Mawilites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Mawile hold it, and it will be able to Mega Evolve.
#-------------------------------
[AGGRONITE]
Name = Aggronite
NamePlural = Aggronites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Aggron hold it, and it will be able to Mega Evolve.
#-------------------------------
[MEDICHAMITE]
Name = Medichamite
NamePlural = Medichamites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Medicham hold it, and it will be able to Mega Evolve.
#-------------------------------
[MANECTITE]
Name = Manectite
NamePlural = Manectites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Manectric hold it, and it will be able to Mega Evolve.
#-------------------------------
[SHARPEDONITE]
Name = Sharpedonite
NamePlural = Sharpedonites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Sharpedo hold it, and it will be able to Mega Evolve.
#-------------------------------
[CAMERUPTITE]
Name = Cameruptite
NamePlural = Cameruptites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Camerupt hold it, and it will be able to Mega Evolve.
#-------------------------------
[ALTARIANITE]
Name = Altarianite
NamePlural = Altarianites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Altaria hold it, and it will be able to Mega Evolve.
#-------------------------------
[BANETTITE]
Name = Banettite
NamePlural = Banettites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Banette hold it, and it will be able to Mega Evolve.
#-------------------------------
[ABSOLITE]
Name = Absolite
NamePlural = Absolites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Absol hold it, and it will be able to Mega Evolve.
#-------------------------------
[GLALITITE]
Name = Glalitite
NamePlural = Glalitites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Glalie hold it, and it will be able to Mega Evolve.
#-------------------------------
[SALAMENCITE]
Name = Salamencite
NamePlural = Salamencites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Salamence hold it, and it will be able to Mega Evolve.
#-------------------------------
[METAGROSSITE]
Name = Metagrossite
NamePlural = Metagrossites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Metagross hold it, and it will be able to Mega Evolve.
#-------------------------------
[LATIASITE]
Name = Latiasite
NamePlural = Latiasites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Latias hold it, and it will be able to Mega Evolve.
#-------------------------------
[LATIOSITE]
Name = Latiosite
NamePlural = Latiosites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Latios hold it, and it will be able to Mega Evolve.
#-------------------------------
[LOPUNNITE]
Name = Lopunnite
NamePlural = Lopunnites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Lopunny hold it, and it will be able to Mega Evolve.
#-------------------------------
[GARCHOMPITE]
Name = Garchompite
NamePlural = Garchompites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Garchomp hold it, and it will be able to Mega Evolve.
#-------------------------------
[LUCARIONITE]
Name = Lucarionite
NamePlural = Lucarionites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Lucario hold it, and it will be able to Mega Evolve.
#-------------------------------
[ABOMASITE]
Name = Abomasite
NamePlural = Abomasites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Abomasnow hold it, and it will be able to Mega Evolve.
#-------------------------------
[GALLADITE]
Name = Galladite
NamePlural = Galladites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Gallade hold it, and it will be able to Mega Evolve.
#-------------------------------
[AUDINITE]
Name = Audinite
NamePlural = Audinites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Audino hold it, and it will be able to Mega Evolve.
#-------------------------------
[DIANCITE]
Name = Diancite
NamePlural = Diancites
Pocket = 1
Price = 0
Flags = MegaStone,Fling_80
Description = One of a variety of mysterious Mega Stones. Have Diancie hold it, and it will be able to Mega Evolve.
#-------------------------------
[REDORB]
Name = Red Orb
NamePlural = Red Orbs
Pocket = 1
Price = 10000
SellPrice = 0
Description = A shiny red orb that is said to have a legend tied to it. It's known to be connected with the Hoenn region.
#-------------------------------
[BLUEORB]
Name = Blue Orb
NamePlural = Blue Orbs
Pocket = 1
Price = 10000
SellPrice = 0
Description = A shiny blue orb that is said to have a legend tied to it. It's known to be connected with the Hoenn region.
#-------------------------------
[POTION]
Name = Potion
NamePlural = Potions
Pocket = 2
Price = 200
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A spray-type medicine for treating wounds. It can be used to restore 20 HP to a single Pokémon.
#-------------------------------
[ORANSMOOTHIE]
Name = Oran Smoothie
NamePlural = Oran Smoothies
Pocket = 2
Price = 225
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A blended drink of medicine and berries. Restores 30 HP to a single Pokémon.
#-------------------------------
[PECHASMOOTHIE]
Name = Pecha Smoothie
NamePlural = Pecha Smoothies
Pocket = 2
Price = 250
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A blended drink of medicine and berries. Restores 20 HP to a single Pokémon and cures poison.
#-------------------------------
[CHERISMOOTHIE]
Name = Cheri Smoothie
NamePlural = Cheri Smoothies
Pocket = 2
Price = 250
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A blended drink of medicine and berries. Restores 20 HP to a single Pokémon and cures paralysis.
#-------------------------------
[CHESTOSMOOTHIE]
Name = Chesto Smoothie
NamePlural = Chesto Smoothies
Pocket = 2
Price = 250
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A blended drink of medicine and berries. Restores 20 HP to a single Pokémon and cures sleep.
#-------------------------------
[RAWSTSMOOTHIE]
Name = Rawst Smoothie
NamePlural = Rawst Smoothies
Pocket = 2
Price = 250
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A blended drink of medicine and berries. Restores 20 HP to a single Pokémon and cures burns.
#-------------------------------
[SUPERPOTION]
Name = Super Potion
NamePlural = Super Potions
Pocket = 2
Price = 700
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A spray-type medicine for treating wounds. It can be used to restore 60 HP to a single Pokémon.
#-------------------------------
[HYPERPOTION]
Name = Hyper Potion
NamePlural = Hyper Potions
Pocket = 2
Price = 1500
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A spray-type medicine for treating wounds. It can be used to restore 120 HP to a single Pokémon.
#-------------------------------
[MAXPOTION]
Name = Max Potion
NamePlural = Max Potions
Pocket = 2
Price = 2500
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A spray-type medicine for treating wounds. It can completely restore the max HP of a single Pokémon.
#-------------------------------
[FULLRESTORE]
Name = Full Restore
NamePlural = Full Restores
Pocket = 2
Price = 3000
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A medicine that fully restores the HP and heals any status problems of a single Pokémon.
#-------------------------------
[SACREDASH]
Name = Sacred Ash
NamePlural = Sacred Ashes
Pocket = 2
Price = 50000
FieldUse = Direct
Flags = Fling_30
Description = It revives all fainted Pokémon. In doing so, it also fully restores their HP.
#-------------------------------
[AWAKENING]
Name = Awakening
NamePlural = Awakenings
Pocket = 2
Price = 200
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A spray-type medicine. It awakens a Pokémon from the clutches of sleep.
#-------------------------------
[ANTIDOTE]
Name = Antidote
NamePlural = Antidotes
Pocket = 2
Price = 200
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A spray-type medicine. It lifts the effect of poison from one Pokémon.
#-------------------------------
[BURNHEAL]
Name = Burn Heal
NamePlural = Burn Heals
Pocket = 2
Price = 200
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A spray-type medicine. It heals a single Pokémon that is suffering from a burn.
#-------------------------------
[PARALYZEHEAL]
Name = Paralyze Heal
NamePlural = Paralyze Heals
Pocket = 2
Price = 200
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A spray-type medicine. It eliminates paralysis from a single Pokémon.
#-------------------------------
[ICEHEAL]
Name = Ice Heal
NamePlural = Ice Heals
Pocket = 2
Price = 200
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A spray-type medicine. It defrosts a Pokémon that has been frozen solid.
#-------------------------------
[FULLHEAL]
Name = Full Heal
NamePlural = Full Heals
Pocket = 2
Price = 400
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A spray-type medicine. It heals all the status problems of a single Pokémon.
#-------------------------------
[PEWTERCRUNCHIES]
Name = Pewter Crunchies
NamePlural = Pewter Crunchies
Pocket = 2
Price = 250
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = Pewter City's famous crunchy snack. They can be used to heal all status conditions of a single Pokémon.
#-------------------------------
[RAGECANDYBAR]
Name = Rage Candy Bar
NamePlural = Rage Candy Bars
Pocket = 2
Price = 350
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = Mahogany Town's famous candy. It can be used once to heal all the status conditions of a Pokémon.
#-------------------------------
[LAVACOOKIE]
Name = Lava Cookie
NamePlural = Lava Cookies
Pocket = 2
Price = 350
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = Lavaridge Town's local specialty. It heals all the status problems of one Pokémon.
#-------------------------------
[OLDGATEAU]
Name = Old Gateau
NamePlural = Old Gateaux
Pocket = 2
Price = 350
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = Old Chateau's hidden specialty. It heals all the status problems of a single Pokémon.
#-------------------------------
[CASTELIACONE]
Name = Casteliacone
NamePlural = Casteliacones
Pocket = 2
Price = 350
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = Castelia City's specialty, soft-serve ice cream. It heals all the status problems of a single Pokémon.
#-------------------------------
[LUMIOSEGALETTE]
Name = Lumiose Galette
NamePlural = Lumiose Galettes
Pocket = 2
Price = 350
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A popular treat in Lumiose City. It can be used once to heal all the status conditions of a Pokémon.
#-------------------------------
[SHALOURSABLE]
Name = Shalour Sable
NamePlural = Shalour Sables
Pocket = 2
Price = 350
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = Shalour City's famous shortbread. It can be used once to heal all the status conditions of a Pokémon.
#-------------------------------
[BIGMALASADA]
Name = Big Malasada
NamePlural = Big Malasadas
Pocket = 2
Price = 350
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = The Alola region's specialty--fried bread. It can be used once to heal all the status conditions of a Pokémon.
#-------------------------------
[REVIVE]
Name = Revive
NamePlural = Revives
Pocket = 2
Price = 2000
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A medicine that revives a fainted Pokémon. It restores half the Pokémon's maximum HP.
#-------------------------------
[MAXREVIVE]
Name = Max Revive
NamePlural = Max Revives
Pocket = 2
Price = 4000
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A medicine that revives a fainted Pokémon. It fully restores the Pokémon's HP.
#-------------------------------
[BERRYJUICE]
Name = Berry Juice
NamePlural = Berry Juices
Pocket = 2
Price = 100
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A 100% pure juice made of Berries. It restores the HP of one Pokémon by just 20 points.
#-------------------------------
[SWEETHEART]
Name = Sweet Heart
NamePlural = Sweet Hearts
Pocket = 2
Price = 3000
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = Very sweet chocolate. It restores the HP of one Pokémon by only 20 points.
#-------------------------------
[FRESHWATER]
Name = Fresh Water
NamePlural = Fresh Waters
Pocket = 2
Price = 200
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = Water with high mineral content. It can be used to restore 30 HP to a single Pokémon.
#-------------------------------
[SODAPOP]
Name = Soda Pop
NamePlural = Soda Pops
Pocket = 2
Price = 300
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A highly carbonated soda drink. It can be used to restore 50 HP to a single Pokémon.
#-------------------------------
[LEMONADE]
Name = Lemonade
NamePlural = Lemonades
Pocket = 2
Price = 350
SellPrice = 200
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A very sweet and refreshing drink. It can be used to restore 70 HP to a single Pokémon.
#-------------------------------
[MOOMOOMILK]
Name = Moomoo Milk
NamePlural = Moomoo Milks
Pocket = 2
Price = 600
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = Milk with a very high nutrition content. It restores the HP of one Pokémon by 100 points.
#-------------------------------
[ENERGYPOWDER]
Name = Energy Powder
NamePlural = Energy Powders
Pocket = 2
Price = 500
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A very bitter medicinal powder. It can be used to restore 60 HP to a single Pokémon.
#-------------------------------
[ENERGYROOT]
Name = Energy Root
NamePlural = Energy Roots
Pocket = 2
Price = 1200
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = An extremely bitter medicinal root. It can be used to restore 120 HP to a single Pokémon.
#-------------------------------
[HEALPOWDER]
Name = Heal Powder
NamePlural = Heal Powders
Pocket = 2
Price = 300
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A very bitter medicine powder. It heals all the status problems of a single Pokémon.
#-------------------------------
[REVIVALHERB]
Name = Revival Herb
NamePlural = Revival Herbs
Pocket = 2
Price = 2800
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A very bitter medicinal herb. It revives a fainted Pokémon, fully restoring its HP.
#-------------------------------
[MAXHONEY]
Name = Max Honey
NamePlural = Max Honeys
Pocket = 2
Price = 8000
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = Honey that Dynamax Vespiquen produces. It has the same effect as a Max Revive.
#-------------------------------
[ETHER]
Name = Ether
NamePlural = Ethers
Pocket = 2
Price = 1200
FieldUse = OnPokemon
BattleUse = OnMove
Flags = Fling_30
Description = It restores the PP of a Pokémon's selected move by a maximum of 10 points.
#-------------------------------
[MAXETHER]
Name = Max Ether
NamePlural = Max Ethers
Pocket = 2
Price = 2000
FieldUse = OnPokemon
BattleUse = OnMove
Flags = Fling_30
Description = It fully restores the PP of a single selected move that has been learned by the target Pokémon.
#-------------------------------
[ELIXIR]
Name = Elixir
NamePlural = Elixirs
Pocket = 2
Price = 3000
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = It restores the PP of all the moves learned by the targeted Pokémon by 10 points each.
#-------------------------------
[MAXELIXIR]
Name = Max Elixir
NamePlural = Max Elixirs
Pocket = 2
Price = 4500
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = It fully restores the PP of all the moves learned by the targeted Pokémon.
#-------------------------------
[PPUP]
Name = PP Up
NamePlural = PP Ups
Pocket = 2
Price = 10000
FieldUse = OnPokemon
Flags = Fling_30
Description = It slightly raises the maximum PP of a selected move that has been learned by the target Pokémon.
#-------------------------------
[PPMAX]
Name = PP Max
NamePlural = PP Maxes
Pocket = 2
Price = 10000
FieldUse = OnPokemon
Flags = Fling_30
Description = It maximally raises the top PP of a selected move that has been learned by the target Pokémon.
#-------------------------------
[HPUP]
Name = HP Up
NamePlural = HP Ups
Pocket = 2
Price = 10000
FieldUse = OnPokemon
Flags = Fling_30
Description = A nutritious drink for Pokémon. It raises the base HP of a single Pokémon.
#-------------------------------
[PROTEIN]
Name = Protein
NamePlural = Proteins
Pocket = 2
Price = 10000
FieldUse = OnPokemon
Flags = Fling_30
Description = A nutritious drink for Pokémon. It raises the base Attack stat of a single Pokémon.
#-------------------------------
[IRON]
Name = Iron
NamePlural = Irons
Pocket = 2
Price = 10000
FieldUse = OnPokemon
Flags = Fling_30
Description = A nutritious drink for Pokémon. It raises the base Defense stat of a single Pokémon.
#-------------------------------
[CALCIUM]
Name = Calcium
NamePlural = Calciums
Pocket = 2
Price = 10000
FieldUse = OnPokemon
Flags = Fling_30
Description = A nutritious drink for Pokémon. It raises the base Sp. Atk (Special Attack) stat of a single Pokémon.
#-------------------------------
[ZINC]
Name = Zinc
NamePlural = Zincs
Pocket = 2
Price = 10000
FieldUse = OnPokemon
Flags = Fling_30
Description = A nutritious drink for Pokémon. It raises the base Sp. Def (Special Defense) stat of a single Pokémon.
#-------------------------------
[CARBOS]
Name = Carbos
NamePlural = Carbos
Pocket = 2
Price = 10000
FieldUse = OnPokemon
Flags = Fling_30
Description = A nutritious drink for Pokémon. It raises the base Speed stat of a single Pokémon.
#-------------------------------
[HEALTHFEATHER]
Name = Health Feather
NamePlural = Health Feathers
Pocket = 2
Price = 300
FieldUse = OnPokemon
Flags = Fling_20
Description = An item for use on a Pokémon. It slightly increases the base HP of a single Pokémon.
#-------------------------------
[MUSCLEFEATHER]
Name = Muscle Feather
NamePlural = Muscle Feathers
Pocket = 2
Price = 300
FieldUse = OnPokemon
Flags = Fling_20
Description = An item for use on a Pokémon. It slightly increases the base Attack stat of a single Pokémon.
#-------------------------------
[RESISTFEATHER]
Name = Resist Feather
NamePlural = Resist Feathers
Pocket = 2
Price = 300
FieldUse = OnPokemon
Flags = Fling_20
Description = An item for use on a Pokémon. It slightly increases the base Defense stat of a single Pokémon.
#-------------------------------
[GENIUSFEATHER]
Name = Genius Feather
NamePlural = Genius Feathers
Pocket = 2
Price = 300
FieldUse = OnPokemon
Flags = Fling_20
Description = An item for use on a Pokémon. It slightly increases the base Sp. Atk stat of a single Pokémon.
#-------------------------------
[CLEVERFEATHER]
Name = Clever Feather
NamePlural = Clever Feathers
Pocket = 2
Price = 300
FieldUse = OnPokemon
Flags = Fling_20
Description = An item for use on a Pokémon. It slightly increases the base Sp. Def stat of a single Pokémon.
#-------------------------------
[SWIFTFEATHER]
Name = Swift Feather
NamePlural = Swift Feathers
Pocket = 2
Price = 300
FieldUse = OnPokemon
Flags = Fling_20
Description = An item for use on a Pokémon. It slightly increases the base Speed stat of a single Pokémon.
#-------------------------------
[LONELYMINT]
Name = Lonely Mint
NamePlural = Lonely Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Attack will grow more easily, but its Defense will grow more slowly.
#-------------------------------
[ADAMANTMINT]
Name = Adamant Mint
NamePlural = Adamant Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Attack will grow more easily, but its Sp. Atk will grow more slowly.
#-------------------------------
[NAUGHTYMINT]
Name = Naughty Mint
NamePlural = Naughty Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Attack will grow more easily, but its Sp. Def will grow more slowly.
#-------------------------------
[BRAVEMINT]
Name = Brave Mint
NamePlural = Brave Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Attack will grow more easily, but its Speed will grow more slowly.
#-------------------------------
[BOLDMINT]
Name = Bold Mint
NamePlural = Bold Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Defense will grow more easily, but its Attack will grow more slowly.
#-------------------------------
[IMPISHMINT]
Name = Impish Mint
NamePlural = Impish Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Defense will grow more easily, but its Sp. Atk will grow more slowly.
#-------------------------------
[LAXMINT]
Name = Lax Mint
NamePlural = Lax Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Defense will grow more easily, but its Sp. Def will grow more slowly.
#-------------------------------
[RELAXEDMINT]
Name = Relaxed Mint
NamePlural = Relaxed Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Defense will grow more easily, but its Speed will grow more slowly.
#-------------------------------
[MODESTMINT]
Name = Modest Mint
NamePlural = Modest Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Sp. Atk will grow more easily, but its Attack will grow more slowly.
#-------------------------------
[MILDMINT]
Name = Mild Mint
NamePlural = Mild Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Sp. Atk will grow more easily, but its Defense will grow more slowly.
#-------------------------------
[RASHMINT]
Name = Rash Mint
NamePlural = Rash Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Sp. Atk will grow more easily, but its Sp. Def will grow more slowly.
#-------------------------------
[QUIETMINT]
Name = Quiet Mint
NamePlural = Quiet Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Sp. Atk will grow more easily, but its Speed will grow more slowly.
#-------------------------------
[CALMMINT]
Name = Calm Mint
NamePlural = Calm Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Sp. Def will grow more easily, but its Attack will grow more slowly.
#-------------------------------
[GENTLEMINT]
Name = Gentle Mint
NamePlural = Gentle Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Sp. Def will grow more easily, but its Defense will grow more slowly.
#-------------------------------
[CAREFULMINT]
Name = Careful Mint
NamePlural = Careful Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Sp. Def will grow more easily, but its Sp. Atk will grow more slowly.
#-------------------------------
[SASSYMINT]
Name = Sassy Mint
NamePlural = Sassy Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Sp. Def will grow more easily, but its Speed will grow more slowly.
#-------------------------------
[TIMIDMINT]
Name = Timid Mint
NamePlural = Timid Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Speed will grow more easily, but its Attack will grow more slowly.
#-------------------------------
[HASTYMINT]
Name = Hasty Mint
NamePlural = Hasty Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Speed will grow more easily, but its Defense will grow more slowly.
#-------------------------------
[JOLLYMINT]
Name = Jolly Mint
NamePlural = Jolly Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Speed will grow more easily, but its Sp. Atk will grow more slowly.
#-------------------------------
[NAIVEMINT]
Name = Naive Mint
NamePlural = Naive Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, its Speed will grow more easily, but its Sp. Def will grow more slowly.
#-------------------------------
[SERIOUSMINT]
Name = Serious Mint
NamePlural = Serious Mints
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_10
Description = When a Pokémon smells this mint, all of its stats will grow at an equal rate.
#-------------------------------
[ABILITYCAPSULE]
Name = Ability Capsule
NamePlural = Ability Capsule
Pocket = 2
Price = 10000
FieldUse = OnPokemon
Description = A capsule that allows a Pokémon with two Abilities to switch between these Abilities when it is used.
#-------------------------------
[ABILITYPATCH]
Name = Ability Patch
NamePlural = Ability Patches
Pocket = 2
Price = 10000
FieldUse = OnPokemon
Description = A patch that allows a Pokémon with a regular Ability to have a rare Ability.
#-------------------------------
[EXPCANDYXS]
Name = Exp. Candy XS
NamePlural = Exp. Candy XSs
Pocket = 2
Price = 20
FieldUse = OnPokemon
Flags = Fling_30
Description = A candy packed with energy. When consumed, it will grant a Pokémon a very small amount of Exp. Points.
#-------------------------------
[EXPCANDYS]
Name = Exp. Candy S
NamePlural = Exp. Candy Ss
Pocket = 2
Price = 240
FieldUse = OnPokemon
Flags = Fling_30
Description = A candy packed with energy. When consumed, it will grant a Pokémon a small amount of Exp. Points.
#-------------------------------
[EXPCANDYM]
Name = Exp. Candy M
NamePlural = Exp. Candy Ms
Pocket = 2
Price = 1000
FieldUse = OnPokemon
Flags = Fling_30
Description = A candy packed with energy. When consumed, it will grant a Pokémon a moderate amount of Exp. Points.
#-------------------------------
[EXPCANDYL]
Name = Exp. Candy L
NamePlural = Exp. Candy Ls
Pocket = 2
Price = 3000
FieldUse = OnPokemon
Flags = Fling_30
Description = A candy packed with energy. When consumed, it will grant a Pokémon a large amount of Exp. Points.
#-------------------------------
[EXPCANDYXL]
Name = Exp. Candy XL
NamePlural = Exp. Candy XLs
Pocket = 2
Price = 10000
FieldUse = OnPokemon
Flags = Fling_30
Description = A candy packed with energy. When consumed, it will grant a Pokémon a very large amount of Exp. Points.
#-------------------------------
[RARECANDY]
Name = Rare Candy
NamePlural = Rare Candies
Pocket = 2
Price = 10000
FieldUse = OnPokemon
Flags = Fling_30
Description = A candy that is packed with energy. It raises the level of a single Pokémon by one.
#-------------------------------
[MASTERBALL]
Name = Master Ball
NamePlural = Master Balls
Pocket = 3
Price = 0
BattleUse = OnFoe
Flags = PokeBall
Description = The best Ball with the ultimate level of performance. It will catch any wild Pokémon without fail.
#-------------------------------
[ULTRABALL]
Name = Ultra Ball
NamePlural = Ultra Balls
Pocket = 3
Price = 800
BattleUse = OnFoe
Flags = PokeBall
Description = An ultra-performance Ball that provides a higher Pokémon catch rate than a Great Ball.
#-------------------------------
[GREATBALL]
Name = Great Ball
NamePlural = Great Balls
Pocket = 3
Price = 600
BattleUse = OnFoe
Flags = PokeBall
Description = A good, high-performance Ball that provides a higher Pokémon catch rate than a standard Poké Ball.
#-------------------------------
[POKEBALL]
Name = Poké Ball
NamePlural = Poké Balls
Pocket = 3
Price = 200
BattleUse = OnFoe
Flags = PokeBall
Description = A device for catching wild Pokémon. It is thrown like a ball at the target. It is designed as a capsule system.
#-------------------------------
[SAFARIBALL]
Name = Safari Ball
NamePlural = Safari Balls
Pocket = 3
Price = 0
BattleUse = OnFoe
Flags = PokeBall
Description = A special Poké Ball that is used only in the Safari Zone. It is decorated in a camouflage pattern.
#-------------------------------
[SPORTBALL]
Name = Sport Ball
NamePlural = Sport Balls
Pocket = 3
Price = 300
BattleUse = OnFoe
Flags = PokeBall
Description = A special Poké Ball for the Bug-Catching Contest.
#-------------------------------
[NETBALL]
Name = Net Ball
NamePlural = Net Balls
Pocket = 3
Price = 1000
BattleUse = OnFoe
Flags = PokeBall
Description = A somewhat different Poké Ball that works especially well on Water- and Bug-type Pokémon.
#-------------------------------
[DIVEBALL]
Name = Dive Ball
NamePlural = Dive Balls
Pocket = 3
Price = 1000
BattleUse = OnFoe
Flags = PokeBall
Description = A somewhat different Poké Ball that works especially well on Pokémon that live underwater.
#-------------------------------
[NESTBALL]
Name = Nest Ball
NamePlural = Nest Balls
Pocket = 3
Price = 1000
BattleUse = OnFoe
Flags = PokeBall
Description = A somewhat different Poké Ball that works especially well on weaker Pokémon in the wild.
#-------------------------------
[REPEATBALL]
Name = Repeat Ball
NamePlural = Repeat Balls
Pocket = 3
Price = 1000
BattleUse = OnFoe
Flags = PokeBall
Description = A somewhat different Poké Ball that works especially well on Pokémon species that were previously caught.
#-------------------------------
[TIMERBALL]
Name = Timer Ball
NamePlural = Timer Balls
Pocket = 3
Price = 1000
BattleUse = OnFoe
Flags = PokeBall
Description = A somewhat different Ball that becomes progressively better the more turns there are in a battle.
#-------------------------------
[LUXURYBALL]
Name = Luxury Ball
NamePlural = Luxury Balls
Pocket = 3
Price = 3000
BattleUse = OnFoe
Flags = PokeBall
Description = A comfortable Poké Ball that makes a caught wild Pokémon quickly grow friendly.
#-------------------------------
[PREMIERBALL]
Name = Premier Ball
NamePlural = Premier Balls
Pocket = 3
Price = 200
SellPrice = 10
BattleUse = OnFoe
Flags = PokeBall
Description = A somewhat rare Poké Ball that has been specially made to commemorate an event of some sort.
#-------------------------------
[DUSKBALL]
Name = Dusk Ball
NamePlural = Dusk Balls
Pocket = 3
Price = 1000
BattleUse = OnFoe
Flags = PokeBall
Description = A somewhat different Poké Ball that makes it easier to catch wild Pokémon at night or in dark places like caves.
#-------------------------------
[HEALBALL]
Name = Heal Ball
NamePlural = Heal Balls
Pocket = 3
Price = 300
BattleUse = OnFoe
Flags = PokeBall
Description = A remedial Poké Ball that restores the caught Pokémon's HP and eliminates any status problem.
#-------------------------------
[QUICKBALL]
Name = Quick Ball
NamePlural = Quick Balls
Pocket = 3
Price = 1000
BattleUse = OnFoe
Flags = PokeBall
Description = A somewhat different Poké Ball that provides a better catch rate if used at the start of a wild encounter.
#-------------------------------
[CHERISHBALL]
Name = Cherish Ball
NamePlural = Cherish Balls
Pocket = 3
Price = 0
BattleUse = OnFoe
Flags = PokeBall
Description = A quite rare Poké Ball that has been specially crafted to commemorate an occasion of some sort.
#-------------------------------
[FASTBALL]
Name = Fast Ball
NamePlural = Fast Balls
Pocket = 3
Price = 300
BattleUse = OnFoe
Flags = PokeBall
Description = A Poké Ball that makes it easier to catch fast Pokémon.
#-------------------------------
[LEVELBALL]
Name = Level Ball
NamePlural = Level Balls
Pocket = 3
Price = 300
BattleUse = OnFoe
Flags = PokeBall
Description = A Poké Ball for catching Pokémon that are a lower level than your own.
#-------------------------------
[LUREBALL]
Name = Lure Ball
NamePlural = Lure Balls
Pocket = 3
Price = 300
BattleUse = OnFoe
Flags = PokeBall
Description = A Poké Ball for catching Pokémon hooked by a Rod when fishing.
#-------------------------------
[HEAVYBALL]
Name = Heavy Ball
NamePlural = Heavy Balls
Pocket = 3
Price = 300
BattleUse = OnFoe
Flags = PokeBall
Description = A Poké Ball for catching very heavy Pokémon.
#-------------------------------
[LOVEBALL]
Name = Love Ball
NamePlural = Love Balls
Pocket = 3
Price = 300
BattleUse = OnFoe
Flags = PokeBall
Description = A Poké Ball for catching Pokémon that are the opposite gender of your Pokémon.
#-------------------------------
[FRIENDBALL]
Name = Friend Ball
NamePlural = Friend Balls
Pocket = 3
Price = 300
BattleUse = OnFoe
Flags = PokeBall
Description = A Poké Ball that makes caught Pokémon more friendly.
#-------------------------------
[MOONBALL]
Name = Moon Ball
NamePlural = Moon Balls
Pocket = 3
Price = 300
BattleUse = OnFoe
Flags = PokeBall
Description = A Poké Ball for catching Pokémon that evolve using the Moon Stone.
#-------------------------------
[DREAMBALL]
Name = Dream Ball
NamePlural = Dream Balls
Pocket = 3
Price = 300
BattleUse = OnFoe
Flags = PokeBall
Description = A somewhat different Poké Ball that makes it easier to catch wild Pokémon while they're asleep.
#-------------------------------
[BEASTBALL]
Name = Beast Ball
NamePlural = Beast Balls
Pocket = 3
Price = 1000
BattleUse = OnFoe
Flags = PokeBall
Description = A special Poké Ball designed to catch Ultra Beasts. It has a low success rate for catching others.
#-------------------------------
[TM01]
Name = TM01
NamePlural = TM01s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_150
Move = FOCUSPUNCH
Description = The user focuses its mind before launching a punch. This move fails if the user is hit before it is used.
#-------------------------------
[TM02]
Name = TM02
NamePlural = TM02s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_80
Move = DRAGONCLAW
Description = The user slashes the target with huge sharp claws.
#-------------------------------
[TM03]
Name = TM03
NamePlural = TM03s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_60
Move = WATERPULSE
Description = The user attacks the target with a pulsing blast of water. This may also confuse the target.
#-------------------------------
[TM04]
Name = TM04
NamePlural = TM04s
Pocket = 4
Price = 1500
FieldUse = TR
Flags = Fling_10
Move = CALMMIND
Description = The user quietly focuses its mind and calms its spirit to raise its Sp. Atk and Sp. Def stats.
#-------------------------------
[TM05]
Name = TM05
NamePlural = TM05s
Pocket = 4
Price = 1000
FieldUse = TR
Flags = Fling_10
Move = ROAR
Description = The target is scared off, and a different Pokémon is dragged out. In the wild, this ends the battle.
#-------------------------------
[TM06]
Name = TM06
NamePlural = TM06s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_10
Move = TOXIC
Description = A move that leaves the target badly poisoned. Its poison damage worsens every turn.
#-------------------------------
[TM07]
Name = TM07
NamePlural = TM07s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_10
Move = HAIL
Description = The user summons a hailstorm lasting five turns. It damages all Pokémon except Ice types.
#-------------------------------
[TM08]
Name = TM08
NamePlural = TM08s
Pocket = 4
Price = 1500
FieldUse = TR
Flags = Fling_10
Move = BULKUP
Description = The user tenses its muscles to bulk up its body, raising both its Attack and Defense stats.
#-------------------------------
[TM09]
Name = TM09
NamePlural = TM09s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_25
Move = BULLETSEED
Description = The user forcefully shoots seeds at the target two to five times in a row.
#-------------------------------
[TM10]
Name = TM10
NamePlural = TM10s
Pocket = 4
Price = 1500
FieldUse = TR
Flags = Fling_10
Move = WORKUP
Description = The user is roused, and its Attack and Sp. Atk stats increase.
#-------------------------------
[TM11]
Name = TM11
NamePlural = TM11s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_10
Move = SUNNYDAY
Description = The user intensifies the sun for five turns, powering up Fire-type moves and weakening Water-type moves.
#-------------------------------
[TM12]
Name = TM12
NamePlural = TM12s
Pocket = 4
Price = 1500
FieldUse = TR
Flags = Fling_10
Move = TAUNT
Description = The target is taunted into a rage that allows it to use only attack moves for three turns.
#-------------------------------
[TM13]
Name = TM13
NamePlural = TM13s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_90
Move = ICEBEAM
Description = The target is struck with an icy-cold beam of energy. This may also leave the target frozen.
#-------------------------------
[TM14]
Name = TM14
NamePlural = TM14s
Pocket = 4
Price = 5500
FieldUse = TR
Flags = Fling_110
Move = BLIZZARD
Description = A howling blizzard is summoned to strike opposing Pokémon. This may also leave them frozen.
#-------------------------------
[TM15]
Name = TM15
NamePlural = TM15s
Pocket = 4
Price = 7500
FieldUse = TR
Flags = Fling_150
Move = HYPERBEAM
Description = The target is attacked with a powerful beam. The user can't move on the next turn.
#-------------------------------
[TM16]
Name = TM16
NamePlural = TM16s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_10
Move = LIGHTSCREEN
Description = A wondrous wall of light is put up to reduce damage from special attacks for five turns.
#-------------------------------
[TM17]
Name = TM17
NamePlural = TM17s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_10
Move = PROTECT
Description = The user protects itself from all attacks. Its chance of failing rises if it is used in succession.
#-------------------------------
[TM18]
Name = TM18
NamePlural = TM18s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_10
Move = RAINDANCE
Description = The user summons a heavy rain for five turns, powering up Water-type moves and weakening Fire-type ones.
#-------------------------------
[TM19]
Name = TM19
NamePlural = TM19s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_75
Move = GIGADRAIN
Description = A nutrient-draining attack. The user's HP is restored by half the damage taken by the target.
#-------------------------------
[TM20]
Name = TM20
NamePlural = TM20s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_10
Move = SAFEGUARD
Description = The user creates a protective field that prevents status conditions for five turns.
#-------------------------------
[TM21]
Name = TM21
NamePlural = TM21s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_80
Move = DAZZLINGGLEAM
Description = The user damages opposing Pokémon by emitting a powerful flash.
#-------------------------------
[TM22]
Name = TM22
NamePlural = TM22s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_120
Move = SOLARBEAM
Description = In this two-turn attack, the user gathers light, then blasts a bundled beam on the next turn.
#-------------------------------
[TM23]
Name = TM23
NamePlural = TM23s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_100
Move = IRONTAIL
Description = The target is slammed with a steel-hard tail. This may also lower the target's Defense stat.
#-------------------------------
[TM24]
Name = TM24
NamePlural = TM24s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_90
Move = THUNDERBOLT
Description = A strong electric blast crashes down on the target. This may also leave the target with paralysis.
#-------------------------------
[TM25]
Name = TM25
NamePlural = TM25s
Pocket = 4
Price = 5500
FieldUse = TR
Flags = Fling_110
Move = THUNDER
Description = A wicked thunderbolt is dropped on the target to inflict damage. This may also leave them with paralysis.
#-------------------------------
[TM26]
Name = TM26
NamePlural = TM26s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_100
Move = EARTHQUAKE
Description = The user sets off an earthquake that strikes every Pokémon around it.
#-------------------------------
[TM27]
Name = TM27
NamePlural = TM27s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_65
Move = LOWSWEEP
Description = The user makes a swift attack on the target's legs, which lowers the target's Speed stat.
#-------------------------------
[TM28]
Name = TM28
NamePlural = TM28s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_80
Move = DIG
Description = The user burrows into the ground, then attacks on the next turn. It can also be used to exit dungeons.
#-------------------------------
[TM29]
Name = TM29
NamePlural = TM29s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_90
Move = PSYCHIC
Description = The target is hit by a strong telekinetic force. This may also lower the target's Sp. Def stat.
#-------------------------------
[TM30]
Name = TM30
NamePlural = TM30s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_80
Move = SHADOWBALL
Description = The user hurls a shadowy blob at the target. This may also lower the target's Sp. Def stat.
#-------------------------------
[TM31]
Name = TM31
NamePlural = TM31s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_75
Move = BRICKBREAK
Description = The user attacks with a swift chop. It can also break barriers, such as Light Screen and Reflect.
#-------------------------------
[TM32]
Name = TM32
NamePlural = TM32s
Pocket = 4
Price = 1000
FieldUse = TR
Flags = Fling_10
Move = DOUBLETEAM
Description = By moving rapidly, the user makes illusory copies of itself to raise its evasiveness.
#-------------------------------
[TM33]
Name = TM33
NamePlural = TM33s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_10
Move = REFLECT
Description = A wondrous wall of light is put up to reduce damage from physical attacks for five turns.
#-------------------------------
[TM34]
Name = TM34
NamePlural = TM34s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_60
Move = SHOCKWAVE
Description = The user strikes the target with a quick jolt of electricity. This attack never misses.
#-------------------------------
[TM35]
Name = TM35
NamePlural = TM35s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_90
Move = FLAMETHROWER
Description = The target is scorched with an intense blast of fire. This may also leave the target with a burn.
#-------------------------------
[TM36]
Name = TM36
NamePlural = TM36s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_90
Move = SLUDGEBOMB
Description = Unsanitary sludge is hurled at the target. This may also poison the target.
#-------------------------------
[TM37]
Name = TM37
NamePlural = TM37s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_10
Move = SANDSTORM
Description = A five-turn sandstorm is summoned to hurt all combatants except Rock, Ground, and Steel types.
#-------------------------------
[TM38]
Name = TM38
NamePlural = TM38s
Pocket = 4
Price = 5500
FieldUse = TR
Flags = Fling_110
Move = FIREBLAST
Description = The target is attacked with an intense blast of all-consuming fire. This may also leave them with a burn.
#-------------------------------
[TM39]
Name = TM39
NamePlural = TM39s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_60
Move = ROCKTOMB
Description = Boulders are hurled at the target. This also lowers the target's Speed stat by preventing its movement.
#-------------------------------
[TM40]
Name = TM40
NamePlural = TM40s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_60
Move = AERIALACE
Description = The user confounds the target with speed, then slashes. This attack never misses.
#-------------------------------
[TM41]
Name = TM41
NamePlural = TM41s
Pocket = 4
Price = 1500
FieldUse = TR
Flags = Fling_10
Move = TORMENT
Description = The user torments and enrages the target, making it incapable of using the same move twice in a row.
#-------------------------------
[TM42]
Name = TM42
NamePlural = TM42s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_70
Move = FACADE
Description = This attack move doubles its power if the user is poisoned, burned, or paralyzed.
#-------------------------------
[TM43]
Name = TM43
NamePlural = TM43s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_70
Move = VOLTSWITCH
Description = After making its attack, the user rushes back to switch places with a party Pokémon in waiting.
#-------------------------------
[TM44]
Name = TM44
NamePlural = TM44s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_10
Move = REST
Description = The user goes to sleep for two turns. This fully restores the user's HP and heals any status conditions.
#-------------------------------
[TM45]
Name = TM45
NamePlural = TM45s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_10
Move = ATTRACT
Description = If it is the opposite gender of the user, the target becomes infatuated and less likely to attack.
#-------------------------------
[TM46]
Name = TM46
NamePlural = TM46s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_60
Move = THIEF
Description = The user attacks and steals the target's held item simultaneously.
#-------------------------------
[TM47]
Name = TM47
NamePlural = TM47s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_70
Move = STEELWING
Description = The target is hit with wings of steel. This may also raise the user's Defense stat.
#-------------------------------
[TM48]
Name = TM48
NamePlural = TM48s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_10
Move = SKILLSWAP
Description = The user employs its psychic power to exchange Abilities with the target.
#-------------------------------
[TM49]
Name = TM49
NamePlural = TM49s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_80
Move = SCALD
Description = The user shoots boiling hot water at its target. This may also leave the target with a burn.
#-------------------------------
[TM50]
Name = TM50
NamePlural = TM50s
Pocket = 4
Price = 5500
FieldUse = TR
Flags = Fling_130
Move = OVERHEAT
Description = The user attacks the target at full power. The attack's recoil harshly lowers the user's Sp. Atk stat.
#-------------------------------
[TM51]
Name = TM51
NamePlural = TM51s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_10
Move = ROOST
Description = The user lands and rests its body. This move restores the user's HP by up to half of its max HP.
#-------------------------------
[TM52]
Name = TM52
NamePlural = TM52s
Pocket = 4
Price = 5500
FieldUse = TR
Flags = Fling_120
Move = FOCUSBLAST
Description = The user heightens its mental focus and unleashes its power. This may also lower the target's Sp. Def stat.
#-------------------------------
[TM53]
Name = TM53
NamePlural = TM53s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_90
Move = ENERGYBALL
Description = The user draws power from nature and fires it at the target. This may also lower the target's Sp. Def stat.
#-------------------------------
[TM54]
Name = TM54
NamePlural = TM54s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_40
Move = FALSESWIPE
Description = A restrained attack that prevents the target from fainting. The target is left with at least 1 HP.
#-------------------------------
[TM55]
Name = TM55
NamePlural = TM55s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_65
Move = BRINE
Description = If the target's HP is half or less, this attack will hit with double the power.
#-------------------------------
[TM56]
Name = TM56
NamePlural = TM56s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_10
Move = FLING
Description = The user flings its held item at the target to attack. This move's power and effects depend on the item.
#-------------------------------
[TM57]
Name = TM57
NamePlural = TM57s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_50
Move = CHARGEBEAM
Description = The user attacks the target with an electric charge. The user may use any remaining charge to raise its Sp. Atk.
#-------------------------------
[TM58]
Name = TM58
NamePlural = TM58s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_10
Move = ENDURE
Description = The user endures any attack with at least 1 HP. Its chance of failing rises if it is used in succession.
#-------------------------------
[TM59]
Name = TM59
NamePlural = TM59s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_85
Move = DRAGONPULSE
Description = The target is attacked with a shock wave generated by the user's gaping mouth.
#-------------------------------
[TM60]
Name = TM60
NamePlural = TM60s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_75
Move = DRAINPUNCH
Description = An energy-draining punch. The user's HP is restored by half the damage taken by the target.
#-------------------------------
[TM61]
Name = TM61
NamePlural = TM61s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_10
Move = WILLOWISP
Description = The user shoots a sinister flame at the target to inflict a burn.
#-------------------------------
[TM62]
Name = TM62
NamePlural = TM62s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_90
Move = BUGBUZZ
Description = The user generates a damaging sound wave by vibration. This may also lower the target's Sp. Def stat.
#-------------------------------
[TM63]
Name = TM63
NamePlural = TM63s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_10
Move = NASTYPLOT
Description = The user stimulates its brain by thinking bad thoughts. This sharply raises the user's Sp. Atk stat.
#-------------------------------
[TM64]
Name = TM64
NamePlural = TM64s
Pocket = 4
Price = 7500
FieldUse = TR
Flags = Fling_250
Move = EXPLOSION
Description = The user attacks everything around it by causing a tremendous explosion. The user faints upon using this move.
#-------------------------------
[TM65]
Name = TM65
NamePlural = TM65s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_70
Move = SHADOWCLAW
Description = The user slashes with a sharp claw made from shadows. Critical hits land more easily.
#-------------------------------
[TM66]
Name = TM66
NamePlural = TM66s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_50
Move = PAYBACK
Description = The user stores power, then attacks. If the user moves after the target, this attack's power will be doubled.
#-------------------------------
[TM67]
Name = TM67
NamePlural = TM67s
Pocket = 4
Price = 1000
FieldUse = TR
Flags = Fling_10
Move = RECYCLE
Description = The user recycles a held item that has been used in battle so it can be used again.
#-------------------------------
[TM68]
Name = TM68
NamePlural = TM68s
Pocket = 4
Price = 7500
FieldUse = TR
Flags = Fling_150
Move = GIGAIMPACT
Description = The user charges at the target using every bit of its power. The user can't move on the next turn.
#-------------------------------
[TM69]
Name = TM69
NamePlural = TM69s
Pocket = 4
Price = 1500
FieldUse = TR
Flags = Fling_10
Move = ROCKPOLISH
Description = The user polishes its body to reduce drag. This sharply raises the Speed stat.
#-------------------------------
[TM70]
Name = TM70
NamePlural = TM70s
Pocket = 4
Price = 1000
FieldUse = TR
Flags = Fling_10
Move = FLASH
Description = The user flashes a light that lowers the target's accuracy. It can also be used to illuminate caves.
#-------------------------------
[TM71]
Name = TM71
NamePlural = TM71s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_100
Move = STONEEDGE
Description = The user stabs the target from below with sharpened stones. Critical hits land more easily.
#-------------------------------
[TM72]
Name = TM72
NamePlural = TM72s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_60
Move = AVALANCHE
Description = The power of this attack move is doubled if the user has been hurt by the target in the same turn.
#-------------------------------
[TM73]
Name = TM73
NamePlural = TM73s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_10
Move = THUNDERWAVE
Description = The user launches a weak jolt of electricity that paralyzes the target.
#-------------------------------
[TM74]
Name = TM74
NamePlural = TM74s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_10
Move = GYROBALL
Description = The user tackles with a high-speed spin. This move is stronger the slower the user is than the target.
#-------------------------------
[TM75]
Name = TM75
NamePlural = TM75s
Pocket = 4
Price = 1500
FieldUse = TR
Flags = Fling_10
Move = SWORDSDANCE
Description = A frenetic dance to uplift the fighting spirit. This sharply raises the user's Attack stat.
#-------------------------------
[TM76]
Name = TM76
NamePlural = TM76s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_10
Move = STEALTHROCK
Description = The user lays a trap of levitating stones around the opposing team that hurt Pokémon that switch in.
#-------------------------------
[TM77]
Name = TM77
NamePlural = TM77s
Pocket = 4
Price = 1500
FieldUse = TR
Flags = Fling_10
Move = PSYCHUP
Description = The user hypnotizes itself into copying any stat change made by the target.
#-------------------------------
[TM78]
Name = TM78
NamePlural = TM78s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_55
Move = SNARL
Description = The user yells as if it's ranting about something, which lowers the Sp. Atk stats of opposing Pokémon.
#-------------------------------
[TM79]
Name = TM79
NamePlural = TM79s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_80
Move = DARKPULSE
Description = The user releases a horrible aura imbued with dark thoughts. This may also make the target flinch.
#-------------------------------
[TM80]
Name = TM80
NamePlural = TM80s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_75
Move = ROCKSLIDE
Description = Large boulders are hurled at opposing Pokémon to inflict damage. This may also make them flinch.
#-------------------------------
[TM81]
Name = TM81
NamePlural = TM81s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_80
Move = XSCISSOR
Description = The user slashes at the target by crossing its scythes or claws as if they were a pair of scissors.
#-------------------------------
[TM82]
Name = TM82
NamePlural = TM82s
Pocket = 4
Price = 1000
FieldUse = TR
Flags = Fling_10
Move = SLEEPTALK
Description = While it is asleep, the user randomly uses one of the moves it knows.
#-------------------------------
[TM83]
Name = TM83
NamePlural = TM83s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_60
Move = BULLDOZE
Description = The user strikes everything around it by stomping down on the ground. This lowers the Speed of those hit.
#-------------------------------
[TM84]
Name = TM84
NamePlural = TM84s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_80
Move = POISONJAB
Description = The target is stabbed with a tentacle, arm, or the like steeped in poison. This may also poison them.
#-------------------------------
[TM85]
Name = TM85
NamePlural = TM85s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_100
Move = DREAMEATER
Description = The user eats the dreams of a sleeping target. The user's HP is restored by half the damage dealt.
#-------------------------------
[TM86]
Name = TM86
NamePlural = TM86s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_10
Move = GRASSKNOT
Description = The user snares the target with grass and trips it. The heavier the target, the strong the move is.
#-------------------------------
[TM87]
Name = TM87
NamePlural = TM87s
Pocket = 4
Price = 1500
FieldUse = TR
Flags = Fling_10
Move = SWAGGER
Description = The user enrages and confuses the target. However, this also sharply raises the target's Attack stat.
#-------------------------------
[TM88]
Name = TM88
NamePlural = TM88s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_60
Move = PLUCK
Description = The user pecks the target. If the target is holding a Berry, the user eats it and gains its effect.
#-------------------------------
[TM89]
Name = TM89
NamePlural = TM89s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_70
Move = UTURN
Description = After making its attack, the user rushes back to switch places with a party Pokémon in waiting.
#-------------------------------
[TM90]
Name = TM90
NamePlural = TM90s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_10
Move = SUBSTITUTE
Description = The user creates a substitute for itself using some of its HP. The substitute serves as a decoy.
#-------------------------------
[TM91]
Name = TM91
NamePlural = TM91s
Pocket = 4
Price = 3000
FieldUse = TR
Flags = Fling_80
Move = FLASHCANNON
Description = The user gathers all its light energy and releases it all at once. This may also lower the target's Sp. Def stat.
#-------------------------------
[TM92]
Name = TM92
NamePlural = TM92s
Pocket = 4
Price = 5500
FieldUse = TR
Flags = Fling_10
Move = TRICKROOM
Description = The user creates a bizarre area in which slower Pokémon get to move first for five turns.
#-------------------------------
[TM93]
Name = TM93
NamePlural = TM93s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_50
Move = CUT
Description = The target is cut with a scythe or claw.
#-------------------------------
[TM94]
Name = TM94
NamePlural = TM94s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_90
Move = FLY
Description = The user flies up into the sky and then strikes its target on the next turn.
#-------------------------------
[TM95]
Name = TM95
NamePlural = TM95s
Pocket = 4
Price = 2500
FieldUse = TR
Flags = Fling_90
Move = SURF
Description = The user attacks everything around it by swamping its surroundings with a giant wave.
#-------------------------------
[TM96]
Name = TM96
NamePlural = TM96s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_80
Move = STRENGTH
Description = The target is slugged with a punch thrown at maximum power.
#-------------------------------
[TM97]
Name = TM97
NamePlural = TM97s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_10
Move = DEFOG
Description = A strong wind blows away the target's barriers, such as Reflect, and lowers their evasiveness.
#-------------------------------
[TM98]
Name = TM98
NamePlural = TM98s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_40
Move = ROCKSMASH
Description = The user attacks with a punch that may lower the target's Defense stat.
#-------------------------------
[TM99]
Name = TM99
NamePlural = TM99s
Pocket = 4
Price = 2500
FieldUse = TR
Flags = Fling_80
Move = WATERFALL
Description = The user charges at the target and may make it flinch.
#-------------------------------
[TM100]
Name = TM100
NamePlural = TM100s
Pocket = 4
Price = 2000
FieldUse = TR
Flags = Fling_90
Move = ROCKCLIMB
Description = A charging attack that may also leave the foe confused.
#-------------------------------
[HM01]
Name = HM01
NamePlural = HM01s
Pocket = 4
Price = 0
FieldUse = HM
Move = CUT
Description = The target is cut with a scythe or claw. This can also be used to cut down thin trees.
#-------------------------------
[HM02]
Name = HM02
NamePlural = HM02s
Pocket = 4
Price = 0
FieldUse = HM
Move = FLY
Description = The user soars and then strikes on the next turn. This can also be used to fly to any familiar town.
#-------------------------------
[HM03]
Name = HM03
NamePlural = HM03s
Pocket = 4
Price = 0
FieldUse = HM
Move = SURF
Description = Attacks everything by swamping the surroundings with a giant wave. This can also be used to cross water.
#-------------------------------
[HM04]
Name = HM04
NamePlural = HM04s
Pocket = 4
Price = 0
FieldUse = HM
Move = STRENGTH
Description = The target is slugged with a punch thrown at maximum power. This can also be used to move heavy boulders.
#-------------------------------
[HM05]
Name = HM05
NamePlural = HM05s
Pocket = 4
Price = 0
FieldUse = HM
Move = WATERFALL
Description = The user charges at the target and may make it flinch. It can also be used to climb a waterfall.
#-------------------------------
[HM06]
Name = HM06
NamePlural = HM06s
Pocket = 4
Price = 0
FieldUse = HM
Move = DIVE
Description = Diving on the first turn, the user floats up and attacks on the second. It can be used to dive underwater.
#-------------------------------
[CHERIBERRY]
Name = Cheri Berry
NamePlural = Cheri Berries
Pocket = 5
Price = 20
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Berry,Fling_10,NaturalGift_FIRE_80
Description = It may be used or held by a Pokémon to recover from paralysis.
#-------------------------------
[CHESTOBERRY]
Name = Chesto Berry
NamePlural = Chesto Berries
Pocket = 5
Price = 20
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Berry,Fling_10,NaturalGift_WATER_80
Description = It may be used or held by a Pokémon to recover from sleep.
#-------------------------------
[PECHABERRY]
Name = Pecha Berry
NamePlural = Pecha Berries
Pocket = 5
Price = 20
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Berry,Fling_10,NaturalGift_ELECTRIC_80
Description = It may be used or held by a Pokémon to recover from poison.
#-------------------------------
[RAWSTBERRY]
Name = Rawst Berry
NamePlural = Rawst Berries
Pocket = 5
Price = 20
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Berry,Fling_10,NaturalGift_GRASS_80
Description = It may be used or held by a Pokémon to recover from a burn.
#-------------------------------
[ASPEARBERRY]
Name = Aspear Berry
NamePlural = Aspear Berries
Pocket = 5
Price = 20
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Berry,Fling_10,NaturalGift_ICE_80
Description = It may be used or held by a Pokémon to defrost it.
#-------------------------------
[LEPPABERRY]
Name = Leppa Berry
NamePlural = Leppa Berries
Pocket = 5
Price = 20
FieldUse = OnPokemon
BattleUse = OnMove
Flags = Berry,Fling_10,NaturalGift_FIGHTING_80
Description = It may be used or held by a Pokémon to restore a move's PP by 10.
#-------------------------------
[ORANBERRY]
Name = Oran Berry
NamePlural = Oran Berries
Pocket = 5
Price = 20
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Berry,Fling_10,NaturalGift_POISON_80
Description = It may be used or held by a Pokémon to heal the user by just 10 HP.
#-------------------------------
[PERSIMBERRY]
Name = Persim Berry
NamePlural = Persim Berries
Pocket = 5
Price = 20
BattleUse = OnBattler
Flags = Berry,Fling_10,NaturalGift_GROUND_80
Description = It may be used or held by a Pokémon to recover from confusion.
#-------------------------------
[LUMBERRY]
Name = Lum Berry
NamePlural = Lum Berries
Pocket = 5
Price = 20
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Berry,Fling_10,NaturalGift_FLYING_80
Description = It may be used or held by a Pokémon to recover from any status problem.
#-------------------------------
[SITRUSBERRY]
Name = Sitrus Berry
NamePlural = Sitrus Berries
Pocket = 5
Price = 20
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Berry,Fling_10,NaturalGift_PSYCHIC_80
Description = It may be used or held by a Pokémon to heal the user's HP a little.
#-------------------------------
[FIGYBERRY]
Name = Figy Berry
NamePlural = Figy Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_BUG_80
Description = If held by a Pokémon, it restores the user's HP in a pinch, but will cause confusion if it hates the taste.
#-------------------------------
[WIKIBERRY]
Name = Wiki Berry
NamePlural = Wiki Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_ROCK_80
Description = If held by a Pokémon, it restores the user's HP in a pinch, but will cause confusion if it hates the taste.
#-------------------------------
[MAGOBERRY]
Name = Mago Berry
NamePlural = Mago Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_GHOST_80
Description = If held by a Pokémon, it restores the user's HP in a pinch, but will cause confusion if it hates the taste.
#-------------------------------
[AGUAVBERRY]
Name = Aguav Berry
NamePlural = Aguav Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_DRAGON_80
Description = If held by a Pokémon, it restores the user's HP in a pinch, but will cause confusion if it hates the taste.
#-------------------------------
[IAPAPABERRY]
Name = Iapapa Berry
NamePlural = Iapapa Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_DARK_80
Description = If held by a Pokémon, it restores the user's HP in a pinch, but will cause confusion if it hates the taste.
#-------------------------------
[RAZZBERRY]
Name = Razz Berry
NamePlural = Razz Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_STEEL_80
Description = In the Sinnoh region, they like to make sweets known as Poffins with this Berry.
#-------------------------------
[BLUKBERRY]
Name = Bluk Berry
NamePlural = Bluk Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_FIRE_90
Description = In the Sinnoh region, they like to make sweets known as Poffins with this Berry.
#-------------------------------
[NANABBERRY]
Name = Nanab Berry
NamePlural = Nanab Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_WATER_90
Description = In the Sinnoh region, they like to make sweets known as Poffins with this Berry.
#-------------------------------
[WEPEARBERRY]
Name = Wepear Berry
NamePlural = Wepear Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_ELECTRIC_90
Description = In the Sinnoh region, they like to make sweets known as Poffins with this Berry.
#-------------------------------
[PINAPBERRY]
Name = Pinap Berry
NamePlural = Pinap Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_GRASS_90
Description = In the Sinnoh region, they like to make sweets known as Poffins with this Berry.
#-------------------------------
[POMEGBERRY]
Name = Pomeg Berry
NamePlural = Pomeg Berries
Pocket = 5
Price = 20
FieldUse = OnPokemon
Flags = Berry,Fling_10,NaturalGift_ICE_90
Description = Using it on a Pokémon makes it more friendly, but it also lowers its base HP.
#-------------------------------
[KELPSYBERRY]
Name = Kelpsy Berry
NamePlural = Kelpsy Berries
Pocket = 5
Price = 20
FieldUse = OnPokemon
Flags = Berry,Fling_10,NaturalGift_FIGHTING_90
Description = Using it on a Pokémon makes it more friendly, but it also lowers its base Attack stat.
#-------------------------------
[QUALOTBERRY]
Name = Qualot Berry
NamePlural = Qualot Berries
Pocket = 5
Price = 20
FieldUse = OnPokemon
Flags = Berry,Fling_10,NaturalGift_POISON_90
Description = Using it on a Pokémon makes it more friendly, but it also lowers its base Defense stat.
#-------------------------------
[HONDEWBERRY]
Name = Hondew Berry
NamePlural = Hondew Berries
Pocket = 5
Price = 20
FieldUse = OnPokemon
Flags = Berry,Fling_10,NaturalGift_GROUND_90
Description = Using it on a Pokémon makes it more friendly, but it also lowers its base Sp. Atk stat.
#-------------------------------
[GREPABERRY]
Name = Grepa Berry
NamePlural = Grepa Berries
Pocket = 5
Price = 20
FieldUse = OnPokemon
Flags = Berry,Fling_10,NaturalGift_FLYING_90
Description = Using it on a Pokémon makes it more friendly, but it also lowers its base Sp. Def stat.
#-------------------------------
[TAMATOBERRY]
Name = Tamato Berry
NamePlural = Tamato Berries
Pocket = 5
Price = 20
FieldUse = OnPokemon
Flags = Berry,Fling_10,NaturalGift_PSYCHIC_90
Description = Using it on a Pokémon makes it more friendly, but it also lowers its base Speed stat.
#-------------------------------
[CORNNBERRY]
Name = Cornn Berry
NamePlural = Cornn Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_BUG_90
Description = In the Sinnoh region, they like to make sweets known as Poffins with this Berry.
#-------------------------------
[MAGOSTBERRY]
Name = Magost Berry
NamePlural = Magost Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_ROCK_90
Description = In the Sinnoh region, they like to make sweets known as Poffins with this Berry.
#-------------------------------
[RABUTABERRY]
Name = Rabuta Berry
NamePlural = Rabuta Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_GHOST_90
Description = In the Sinnoh region, they like to make sweets known as Poffins with this Berry.
#-------------------------------
[NOMELBERRY]
Name = Nomel Berry
NamePlural = Nomel Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_DRAGON_90
Description = In the Sinnoh region, they like to make sweets known as Poffins with this Berry.
#-------------------------------
[SPELONBERRY]
Name = Spelon Berry
NamePlural = Spelon Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_DARK_90
Description = In the Sinnoh region, they like to make sweets known as Poffins with this Berry.
#-------------------------------
[PAMTREBERRY]
Name = Pamtre Berry
NamePlural = Pamtre Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_STEEL_90
Description = In the Sinnoh region, they like to make sweets known as Poffins with this Berry.
#-------------------------------
[WATMELBERRY]
Name = Watmel Berry
NamePlural = Watmel Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_FIRE_100
Description = In the Sinnoh region, they like to make sweets known as Poffins with this Berry.
#-------------------------------
[DURINBERRY]
Name = Durin Berry
NamePlural = Durin Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_WATER_100
Description = In the Sinnoh region, they like to make sweets known as Poffins with this Berry.
#-------------------------------
[BELUEBERRY]
Name = Belue Berry
NamePlural = Belue Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_ELECTRIC_100
Description = In the Sinnoh region, they like to make sweets known as Poffins with this Berry.
#-------------------------------
[OCCABERRY]
Name = Occa Berry
NamePlural = Occa Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_FIRE_80
Description = Weakens a supereffective Fire-type attack against the holding Pokémon.
#-------------------------------
[PASSHOBERRY]
Name = Passho Berry
NamePlural = Passho Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_WATER_80
Description = Weakens a supereffective Water-type attack against the holding Pokémon.
#-------------------------------
[WACANBERRY]
Name = Wacan Berry
NamePlural = Wacan Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_ELECTRIC_80
Description = Weakens a supereffective Electric-type attack against the holding Pokémon.
#-------------------------------
[RINDOBERRY]
Name = Rindo Berry
NamePlural = Rindo Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_GRASS_80
Description = Weakens a supereffective Grass-type attack against the holding Pokémon.
#-------------------------------
[YACHEBERRY]
Name = Yache Berry
NamePlural = Yache Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_ICE_80
Description = Weakens a supereffective Ice-type attack against the holding Pokémon.
#-------------------------------
[CHOPLEBERRY]
Name = Chople Berry
NamePlural = Chople Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_FIGHTING_80
Description = Weakens a supereffective Fighting-type attack against the holding Pokémon.
#-------------------------------
[KEBIABERRY]
Name = Kebia Berry
NamePlural = Kebia Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_POISON_80
Description = Weakens a supereffective Poison-type attack against the holding Pokémon.
#-------------------------------
[SHUCABERRY]
Name = Shuca Berry
NamePlural = Shuca Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_GROUND_80
Description = Weakens a supereffective Ground-type attack against the holding Pokémon.
#-------------------------------
[COBABERRY]
Name = Coba Berry
NamePlural = Coba Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_FLYING_80
Description = Weakens a supereffective Flying-type attack against the holding Pokémon.
#-------------------------------
[PAYAPABERRY]
Name = Payapa Berry
NamePlural = Payapa Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_PSYCHIC_80
Description = Weakens a supereffective Psychic-type attack against the holding Pokémon.
#-------------------------------
[TANGABERRY]
Name = Tanga Berry
NamePlural = Tanga Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_BUG_80
Description = Weakens a supereffective Bug-type attack against the holding Pokémon.
#-------------------------------
[CHARTIBERRY]
Name = Charti Berry
NamePlural = Charti Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_ROCK_80
Description = Weakens a supereffective Rock-type attack against the holding Pokémon.
#-------------------------------
[KASIBBERRY]
Name = Kasib Berry
NamePlural = Kasib Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_GHOST_80
Description = Weakens a supereffective Ghost-type attack against the holding Pokémon.
#-------------------------------
[HABANBERRY]
Name = Haban Berry
NamePlural = Haban Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_DRAGON_80
Description = Weakens a supereffective Dragon-type attack against the holding Pokémon.
#-------------------------------
[COLBURBERRY]
Name = Colbur Berry
NamePlural = Colbur Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_DARK_80
Description = Weakens a supereffective Dark-type attack against the holding Pokémon.
#-------------------------------
[BABIRIBERRY]
Name = Babiri Berry
NamePlural = Babiri Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_STEEL_80
Description = Weakens a supereffective Steel-type attack against the holding Pokémon.
#-------------------------------
[ROSELIBERRY]
Name = Roseli Berry
NamePlural = Roseli Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_FAIRY_80
Description = If held by a Pokémon, this Berry will lessen the damage taken from one supereffective Fairy-type attack.
#-------------------------------
[CHILANBERRY]
Name = Chilan Berry
NamePlural = Chilan Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_NORMAL_80
Description = Weakens a Normal-type attack against the Pokémon holding this berry.
#-------------------------------
[LIECHIBERRY]
Name = Liechi Berry
NamePlural = Liechi Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_GRASS_100
Description = If held by a Pokémon, it raises its Attack stat in a pinch.
#-------------------------------
[GANLONBERRY]
Name = Ganlon Berry
NamePlural = Ganlon Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_ICE_100
Description = If held by a Pokémon, it raises its Defense stat in a pinch.
#-------------------------------
[SALACBERRY]
Name = Salac Berry
NamePlural = Salac Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_FIGHTING_100
Description = If held by a Pokémon, it raises its Speed stat in a pinch.
#-------------------------------
[PETAYABERRY]
Name = Petaya Berry
NamePlural = Petaya Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_POISON_100
Description = If held by a Pokémon, it raises its Sp. Atk stat in a pinch.
#-------------------------------
[APICOTBERRY]
Name = Apicot Berry
NamePlural = Apicot Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_GROUND_100
Description = If held by a Pokémon, it raises its Sp. Def stat in a pinch.
#-------------------------------
[LANSATBERRY]
Name = Lansat Berry
NamePlural = Lansat Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_FLYING_100
Description = If held by a Pokémon, it raises its critical-hit ratio in a pinch.
#-------------------------------
[STARFBERRY]
Name = Starf Berry
NamePlural = Starf Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_PSYCHIC_100
Description = If held by a Pokémon, it sharply raises one of its stats in a pinch.
#-------------------------------
[ENIGMABERRY]
Name = Enigma Berry
NamePlural = Enigma Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_BUG_100
Description = If held by a Pokémon, it restores its HP if it is hit by any supereffective attack.
#-------------------------------
[MICLEBERRY]
Name = Micle Berry
NamePlural = Micle Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_ROCK_100
Description = If held by a Pokémon, it raises the accuracy of a move just once in a pinch.
#-------------------------------
[CUSTAPBERRY]
Name = Custap Berry
NamePlural = Custap Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_GHOST_100
Description = If held by a Pokémon, it gets to move first just once in a pinch.
#-------------------------------
[JABOCABERRY]
Name = Jaboca Berry
NamePlural = Jaboca Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_DRAGON_100
Description = If held by a Pokémon and a physical attack lands, the attacker also takes damage.
#-------------------------------
[ROWAPBERRY]
Name = Rowap Berry
NamePlural = Rowap Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_DARK_100
Description = If held by a Pokémon and a special attack lands, the attacker also takes damage.
#-------------------------------
[KEEBERRY]
Name = Kee Berry
NamePlural = Kee Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_FAIRY_100
Description = If held by a Pokémon, this Berry will increase the holder's Defense if it's hit with a physical move.
#-------------------------------
[MARANGABERRY]
Name = Maranga Berry
NamePlural = Maranga Berries
Pocket = 5
Price = 20
Flags = Berry,Fling_10,NaturalGift_DARK_100
Description = If held by a Pokémon, this Berry will increase the holder's Sp. Def if it's hit with a special move.
#-------------------------------
[GRASSMAIL]
Name = Grass Mail
NamePlural = Grass Mail
Pocket = 6
Price = 50
Flags = IconMail
Description = Stationery featuring a print of a refreshingly green field. Let a Pokémon hold it for delivery.
#-------------------------------
[FLAMEMAIL]
Name = Flame Mail
NamePlural = Flame Mail
Pocket = 6
Price = 50
Flags = IconMail
Description = Stationery featuring a print of flames in blazing red. Let a Pokémon hold it for delivery.
#-------------------------------
[BUBBLEMAIL]
Name = Bubble Mail
NamePlural = Bubble Mail
Pocket = 6
Price = 50
Flags = IconMail
Description = Stationery featuring a print of a blue world underwater. Let a Pokémon hold it for delivery.
#-------------------------------
[BLOOMMAIL]
Name = Bloom Mail
NamePlural = Bloom Mail
Pocket = 6
Price = 50
Flags = IconMail
Description = Stationery featuring a print of pretty floral patterns. Let a Pokémon hold it for delivery.
#-------------------------------
[TUNNELMAIL]
Name = Tunnel Mail
NamePlural = Tunnel Mail
Pocket = 6
Price = 50
Flags = IconMail
Description = Stationery featuring a print of a dimly lit coal mine. Let a Pokémon hold it for delivery.
#-------------------------------
[STEELMAIL]
Name = Steel Mail
NamePlural = Steel Mail
Pocket = 6
Price = 50
Flags = IconMail
Description = Stationery featuring a print of cool mechanical designs. Let a Pokémon hold it for delivery.
#-------------------------------
[HEARTMAIL]
Name = Heart Mail
NamePlural = Heart Mail
Pocket = 6
Price = 50
Flags = IconMail
Description = Stationery featuring a print of giant heart patterns. Let a Pokémon hold it for delivery.
#-------------------------------
[SNOWMAIL]
Name = Snow Mail
NamePlural = Snow Mail
Pocket = 6
Price = 50
Flags = IconMail
Description = Stationery featuring a print of a chilly, snow-covered world. Let a Pokémon hold it for delivery.
#-------------------------------
[SPACEMAIL]
Name = Space Mail
NamePlural = Space Mail
Pocket = 6
Price = 50
Flags = IconMail
Description = Stationery featuring a print depicting the huge expanse of space. Let a Pokémon hold it for delivery.
#-------------------------------
[AIRMAIL]
Name = Air Mail
NamePlural = Air Mail
Pocket = 6
Price = 50
Flags = IconMail
Description = Stationery featuring a print of colorful letter sets. Let a Pokémon hold it for delivery.
#-------------------------------
[MOSAICMAIL]
Name = Mosaic Mail
NamePlural = Mosaic Mail
Pocket = 6
Price = 50
Flags = IconMail
Description = Stationery featuring a print of a vivid rainbow pattern. Let a Pokémon hold it for delivery.
#-------------------------------
[BRICKMAIL]
Name = Brick Mail
NamePlural = Brick Mail
Pocket = 6
Price = 50
Flags = IconMail
Description = Stationery featuring a print of a tough-looking brick pattern. Let a Pokémon hold it for delivery.
#-------------------------------
[XATTACK]
Name = X Attack
NamePlural = X Attacks
Pocket = 7
Price = 1000
BattleUse = OnBattler
Flags = Fling_30
Description = An item that sharply boosts the Attack stat of a Pokémon while it remains in battle.
#-------------------------------
[XATTACK2]
Name = X Attack 2
NamePlural = X Attack 2s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It sharply raises the Attack stat of a Pokémon in battle. It wears off if the Pokémon is withdrawn.
#-------------------------------
[XATTACK3]
Name = X Attack 3
NamePlural = X Attack 3s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It drastically raises the Attack stat of a Pokémon in battle. It wears off if the Pokémon is withdrawn.
#-------------------------------
[XATTACK6]
Name = X Attack 6
NamePlural = X Attack 6s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It raises the Attack stat of a Pokémon in battle immensely. It wears off if the Pokémon is withdrawn.
#-------------------------------
[XDEFENSE]
Name = X Defense
NamePlural = X Defenses
Pocket = 7
Price = 2000
BattleUse = OnBattler
Flags = Fling_30
Description = An item that sharply boosts the Defense of a Pokémon while it remains in battle.
#-------------------------------
[XDEFENSE2]
Name = X Defense 2
NamePlural = X Defense 2s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It sharply raises the Defense stat of a Pokémon in battle. It wears off if the Pokémon is withdrawn.
#-------------------------------
[XDEFENSE3]
Name = X Defense 3
NamePlural = X Defense 3s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It drastically raises the Defense stat of a Pokémon in battle. It wears off if the Pokémon is withdrawn.
#-------------------------------
[XDEFENSE6]
Name = X Defense 6
NamePlural = X Defense 6s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It raises the Defense stat of a Pokémon in battle immensely. It wears off if the Pokémon is withdrawn.
#-------------------------------
[XSPATK]
Name = X Sp. Atk
NamePlural = X Sp. Atks
Pocket = 7
Price = 1000
BattleUse = OnBattler
Flags = Fling_30
Description = An item that sharply boosts the Sp. Atk stat of a Pokémon while it remains in battle.
#-------------------------------
[XSPATK2]
Name = X Sp. Atk 2
NamePlural = X Sp. Atk 2s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It sharply raises the Sp. Atk stat of a Pokémon in battle. It wears off if the Pokémon is withdrawn.
#-------------------------------
[XSPATK3]
Name = X Sp. Atk 3
NamePlural = X Sp. Atk 3s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It drastically raises the Sp. Atk stat of a Pokémon in battle. It wears off if the Pokémon is withdrawn.
#-------------------------------
[XSPATK6]
Name = X Sp. Atk 6
NamePlural = X Sp. Atk 6s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It raises the Sp. Atk stat of a Pokémon in battle immensely. It wears off if the Pokémon is withdrawn.
#-------------------------------
[XSPDEF]
Name = X Sp. Def
NamePlural = X Sp. Defs
Pocket = 7
Price = 2000
BattleUse = OnBattler
Flags = Fling_30
Description = An item that sharply boosts the Sp. Def stat of a Pokémon while it remains in battle.
#-------------------------------
[XSPDEF2]
Name = X Sp. Def 2
NamePlural = X Sp. Def 2s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It sharply raises the Sp. Def stat of a Pokémon in battle. It wears off if the Pokémon is withdrawn.
#-------------------------------
[XSPDEF3]
Name = X Sp. Def 3
NamePlural = X Sp. Def 3s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It drastically raises the Sp. Def stat of a Pokémon in battle. It wears off if the Pokémon is withdrawn.
#-------------------------------
[XSPDEF6]
Name = X Sp. Def 6
NamePlural = X Sp. Def 6s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It raises the Sp. Def stat of a Pokémon in battle immensely. It wears off if the Pokémon is withdrawn.
#-------------------------------
[XSPEED]
Name = X Speed
NamePlural = X Speeds
Pocket = 7
Price = 1000
BattleUse = OnBattler
Flags = Fling_30
Description = An item that sharply boosts the Speed stat of a Pokémon while it remains in battle.
#-------------------------------
[XSPEED2]
Name = X Speed 2
NamePlural = X Speed 2s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It sharply raises the Speed stat of a Pokémon in battle. It wears off if the Pokémon is withdrawn.
#-------------------------------
[XSPEED3]
Name = X Speed 3
NamePlural = X Speed 3s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It drastically raises the Speed stat of a Pokémon in battle. It wears off if the Pokémon is withdrawn.
#-------------------------------
[XSPEED6]
Name = X Speed 6
NamePlural = X Speed 6s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It raises the Speed stat of a Pokémon in battle immensely. It wears off if the Pokémon is withdrawn.
#-------------------------------
[XACCURACY]
Name = X Accuracy
NamePlural = X Accuracies
Pocket = 7
Price = 1000
BattleUse = OnBattler
Flags = Fling_30
Description = An item that sharply boosts the accuracy of a Pokémon while it remains in battle.
#-------------------------------
[XACCURACY2]
Name = X Accuracy 2
NamePlural = X Accuracy 2s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It sharply raises the accuracy of a Pokémon in battle. It wears off if the Pokémon is withdrawn.
#-------------------------------
[XACCURACY3]
Name = X Accuracy 3
NamePlural = X Accuracy 3s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It drastically raises the accuracy of a Pokémon in battle. It wears off if the Pokémon is withdrawn.
#-------------------------------
[XACCURACY6]
Name = X Accuracy 6
NamePlural = X Accuracy 6s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It raises the accuracy of a Pokémon in battle immensely. It wears off if the Pokémon is withdrawn.
#-------------------------------
[MAXMUSHROOMS]
Name = Max Mushrooms
NamePlural = clusters of Max Mushrooms
Pocket = 7
Price = 8000
BattleUse = OnBattler
Flags = Fling_30
Description = Mushrooms that boost all stats of a Pokémon during battle.
#-------------------------------
[DIREHIT]
Name = Dire Hit
NamePlural = Dire Hits
Pocket = 7
Price = 1000
BattleUse = OnBattler
Flags = Fling_30
Description = An item that raises the critical-hit ratio greatly. It wears off if the Pokémon is withdrawn.
#-------------------------------
[DIREHIT2]
Name = Dire Hit 2
NamePlural = Dire Hit 2s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It sharply raises the critical-hit ratio. It wears off if the Pokémon is withdrawn.
#-------------------------------
[DIREHIT3]
Name = Dire Hit 3
NamePlural = Dire Hit 3s
Pocket = 7
Price = 0
BattleUse = OnBattler
Flags = Fling_30
Description = It greatly raises the critical-hit ratio. It wears off if the Pokémon is withdrawn.
#-------------------------------
[GUARDSPEC]
Name = Guard Spec.
NamePlural = Guard Specs.
Pocket = 7
Price = 1500
BattleUse = Direct
Flags = Fling_30
Description = An item that prevents stat reduction among the Trainer's party Pokémon for five turns after use.
#-------------------------------
[RESETURGE]
Name = Reset Urge
NamePlural = Reset Urges
Pocket = 7
Price = 0
Flags = Fling_30
Description = When used, it restores any stat changes of an ally Pokémon.
#-------------------------------
[ABILITYURGE]
Name = Ability Urge
NamePlural = Ability Urges
Pocket = 7
Price = 0
Flags = Fling_30
Description = When used, it activates the Ability of an ally Pokémon.
#-------------------------------
[ITEMURGE]
Name = Item Urge
NamePlural = Item Urges
Pocket = 7
Price = 0
Flags = Fling_30
Description = When used, it causes an ally Pokémon to use its held item.
#-------------------------------
[ITEMDROP]
Name = Item Drop
NamePlural = Item Drops
Pocket = 7
Price = 0
Flags = Fling_30
Description = When used, it causes an ally Pokémon to drop a held item.
#-------------------------------
[BLUEFLUTE]
Name = Blue Flute
NamePlural = Blue Flutes
Pocket = 7
Price = 20
FieldUse = OnPokemon
BattleUse = OnPokemon
Consumable = false
Flags = Fling_30
Description = A blue flute made from blown glass. Its melody awakens a single Pokémon from sleep.
#-------------------------------
[YELLOWFLUTE]
Name = Yellow Flute
NamePlural = Yellow Flutes
Pocket = 7
Price = 20
BattleUse = OnBattler
Consumable = false
Flags = Fling_30
Description = A yellow flute made from blown glass. Its melody snaps a single Pokémon out of confusion.
#-------------------------------
[REDFLUTE]
Name = Red Flute
NamePlural = Red Flutes
Pocket = 7
Price = 20
BattleUse = OnBattler
Consumable = false
Flags = Fling_30
Description = A red flute made from blown glass. Its melody snaps a single Pokémon out of infatuation.
#-------------------------------
[POKEDOLL]
Name = Poké Doll
NamePlural = Poké Dolls
Pocket = 7
Price = 300
BattleUse = Direct
Flags = Fling_30
Description = A doll that attracts Pokémon. Use it to flee from any battle with a wild Pokémon.
#-------------------------------
[FLUFFYTAIL]
Name = Fluffy Tail
NamePlural = Fluffy Tails
Pocket = 7
Price = 300
BattleUse = Direct
Flags = Fling_30
Description = An item that attracts Pokémon. Use it to flee from any battle with a wild Pokémon.
#-------------------------------
[POKETOY]
Name = Poké Toy
NamePlural = Poké Toys
Pocket = 7
Price = 300
BattleUse = Direct
Flags = Fling_30
Description = An item that attracts Pokémon. Use it to flee from any battle with a wild Pokémon.
#-------------------------------
[BICYCLE]
Name = Bicycle
NamePlural = Bicycles
Pocket = 8
Price = 0
FieldUse = Direct
Flags = KeyItem
Description = A folding Bicycle that enables much faster movement than the Running Shoes.
#-------------------------------
[OLDROD]
Name = Old Rod
NamePlural = Old Rods
Pocket = 8
Price = 0
FieldUse = Direct
Flags = KeyItem
Description = An old and beat-up fishing rod. Use it by any body of water to fish for wild aquatic Pokémon.
#-------------------------------
[GOODROD]
Name = Good Rod
NamePlural = Good Rods
Pocket = 8
Price = 0
FieldUse = Direct
Flags = KeyItem
Description = A new, good-quality fishing rod. Use it by any body of water to fish for wild aquatic Pokémon.
#-------------------------------
[SUPERROD]
Name = Super Rod
NamePlural = Super Rods
Pocket = 8
Price = 0
FieldUse = Direct
Flags = KeyItem
Description = An awesome, high-tech fishing rod. Use it by any body of water to fish for wild aquatic Pokémon.
#-------------------------------
[ITEMFINDER]
Name = Itemfinder
NamePlural = Itemfinders
Pocket = 8
Price = 0
FieldUse = Direct
Flags = KeyItem
Description = A device used for finding items. If there is a hidden item nearby when it is used, it emits a signal.
#-------------------------------
[DOWSINGMACHINE]
Name = Dowsing Machine
NamePlural = Dowsing Machines
Pocket = 8
Price = 0
FieldUse = Direct
Flags = KeyItem
Description = It checks for unseen items in the area and makes noise and lights when it finds something.
#-------------------------------
[POKERADAR]
Name = Poké Radar
NamePlural = Poké Radars
Pocket = 8
Price = 0
FieldUse = Direct
Flags = KeyItem
Description = A tool that can search out Pokémon that are hiding in grass. Its battery is recharged as you walk.
#-------------------------------
[TOWNMAP]
Name = Town Map
NamePlural = Town Maps
Pocket = 8
Price = 0
FieldUse = Direct
Flags = KeyItem
Description = A very convenient map that can be viewed anytime. It even shows your present location.
#-------------------------------
[ESCAPEROPE]
Name = Escape Rope
NamePlural = Escape Ropes
Pocket = 8
Price = 0
FieldUse = Direct
Flags = KeyItem
Description = A long, durable rope. Use it to escape instantly from a cave or a dungeon.
#-------------------------------
[COINCASE]
Name = Coin Case
NamePlural = Coin Cases
Pocket = 8
Price = 0
FieldUse = Direct
Flags = KeyItem
Description = A case for holding coins obtained at the Game Corner. It holds up to 99,999 coins.
#-------------------------------
[POKEFLUTE]
Name = Poké Flute
NamePlural = Poké Flutes
Pocket = 8
Price = 0
FieldUse = OnPokemon
BattleUse = Direct
Flags = KeyItem
Description = A flute that is said to instantly awaken any Pokémon. It has a lovely tone.
#-------------------------------
[SOOTSACK]
Name = Soot Sack
NamePlural = Soot Sacks
Pocket = 8
Price = 0
Flags = KeyItem
Description = A sack used to gather and hold volcanic ash.
#-------------------------------
[SILPHSCOPE]
Name = Silph Scope
NamePlural = Silph Scopes
Pocket = 8
Price = 0
Flags = KeyItem
Description = A scope that makes unseeable Pokémon visible. It is made by Silph Co.
#-------------------------------
[DEVONSCOPE]
Name = Devon Scope
NamePlural = Devon Scopes
Pocket = 8
Price = 0
Flags = KeyItem
Description = A device by Devon that signals any unseeable Pokémon.
#-------------------------------
[SQUIRTBOTTLE]
Name = Squirt Bottle
NamePlural = Squirt Bottles
Pocket = 8
Price = 0
Flags = KeyItem
Description = A watering can shaped like a Squirtle. It helps promote healthy growth of Berries planted in soft soil.
#-------------------------------
[SPRAYDUCK]
Name = Sprayduck
NamePlural = Sprayducks
Pocket = 8
Price = 0
Flags = KeyItem
Description = A watering can shaped like a Psyduck. It helps promote healthy growth of Berries planted in soft soil.
#-------------------------------
[WAILMERPAIL]
Name = Wailmer Pail
NamePlural = Wailmer Pails
Pocket = 8
Price = 0
Flags = KeyItem
Description = A nifty watering pail. Use it to promote strong growth in Berries planted in soft soil.
#-------------------------------
[SPRINKLOTAD]
Name = Sprinklotad
NamePlural = Sprinklotads
Pocket = 8
Price = 0
Flags = KeyItem
Description = A watering can shaped like a Lotad. It helps promote the healthy growth of any Berries planted in soft soil.
#-------------------------------
[GRACIDEA]
Name = Gracidea
NamePlural = Gracideas
Pocket = 8
Price = 0
FieldUse = OnPokemon
Flags = KeyItem
Description = A flower sometimes bundled in bouquets to convey gratitude on special occasions like birthdays.
#-------------------------------
[REVEALGLASS]
Name = Reveal Glass
NamePlural = Reveal Glasses
Pocket = 8
Price = 0
FieldUse = OnPokemon
Flags = KeyItem
Description = A glass that reveals the truth. It is a mysterious glass that returns a Pokémon back to its original shape.
#-------------------------------
[PRISONBOTTLE]
Name = Prison Bottle
NamePlural = Prison Bottles
Pocket = 8
Price = 0
FieldUse = OnPokemon
Flags = KeyItem
Description = A bottle believed to have been used to seal away the power of a certain Pokémon long, long ago.
#-------------------------------
[ROTOMCATALOG]
Name = Rotom Catalog
NamePlural = Rotom Catalogs
Pocket = 8
Price = 0
FieldUse = OnPokemon
Flags = KeyItem
Description = A catalog of devices that Rotom like. Use the catalog to have Rotom hop in and out of the devices listed within.
#-------------------------------
[ZYGARDECUBE]
Name = Zygarde Cube
NamePlural = Zygarde Cubes
Pocket = 8
Price = 0
FieldUse = OnPokemon
Flags = KeyItem
Description = An item in which Zygarde Cores and Cells are gathered. You can also use it to change Zygarde's forms.
#-------------------------------
[DNASPLICERS]
Name = DNA Splicers
NamePlural = DNA Splicers
Pocket = 8
Price = 0
FieldUse = OnPokemon
Flags = KeyItem
Description = A splicer that fuses Kyurem and a certain Pokémon. They are said to have been one in the beginning.
#-------------------------------
[DNASPLICERSUSED]
Name = DNA Splicers
NamePlural = DNA Splicers
Pocket = 8
Price = 0
FieldUse = OnPokemon
Flags = KeyItem
Description = A splicer that separates Kyurem and a certain Pokémon when they have been fused.
#-------------------------------
[NSOLARIZER]
Name = N-Solarizer
NamePlural = N-Solarizers
Pocket = 8
Price = 0
FieldUse = OnPokemon
Flags = KeyItem
Description = A machine to fuse Necrozma, which needs light, and Solgaleo.
#-------------------------------
[NSOLARIZERUSED]
Name = N-Solarizer
NamePlural = N-Solarizers
Pocket = 8
Price = 0
FieldUse = OnPokemon
Flags = KeyItem
Description = A machine to separate Necrozma, which needed light, from Solgaleo.
#-------------------------------
[NLUNARIZER]
Name = N-Lunarizer
NamePlural = N-Lunarizers
Pocket = 8
Price = 0
FieldUse = OnPokemon
Flags = KeyItem
Description = A machine to fuse Necrozma, which needs light, and Lunala.
#-------------------------------
[NLUNARIZERUSED]
Name = N-Lunarizer
NamePlural = N-Lunarizers
Pocket = 8
Price = 0
FieldUse = OnPokemon
Flags = KeyItem
Description = A machine to separate Necrozma, which needed light, from Lunala.
#-------------------------------
[REINSOFUNITY]
Name = Reins of Unity
NamePlural = Reins of Unity
Pocket = 8
Price = 0
FieldUse = OnPokemon
Flags = KeyItem
Description = Reins that people presented to the king. They unite Calyrex with its beloved steed.
#-------------------------------
[REINSOFUNITYUSED]
Name = Reins of Unity
NamePlural = Reins of Unity
Pocket = 8
Price = 0
FieldUse = OnPokemon
Flags = KeyItem
Description = Reins that people presented to the king. They separate Calyrex and its beloved steed.
#-------------------------------
[OVALCHARM]
Name = Oval Charm
NamePlural = Oval Charms
Pocket = 8
Price = 0
Flags = KeyItem
Description = An oval charm said to increase the chance of Eggs being found at the Day Care.
#-------------------------------
[SHINYCHARM]
Name = Shiny Charm
NamePlural = Shiny Charms
Pocket = 8
Price = 0
Flags = KeyItem
Description = A shiny charm said to increase the chance of finding a Shiny Pokémon.
#-------------------------------
[CATCHINGCHARM]
Name = Catching Charm
NamePlural = Catching Charms
Pocket = 8
Price = 0
Flags = KeyItem
Description = A charm said to increase the chance of getting a critical catch. The charm doesn't shake much.
#-------------------------------
[EXPCHARM]
Name = Exp. Charm
NamePlural = Exp. Charms
Pocket = 8
Price = 0
Flags = KeyItem
Description = A charm that increases the Exp. Points that Pokémon can get. A machine-like object is inside it.
#-------------------------------
[MEGARING]
Name = Mega Ring
NamePlural = Mega Rings
Pocket = 8
Price = 0
Flags = KeyItem,MegaRing
Description = This ring contains an untold power that somehow enables Pokémon carrying Mega Stones to Mega Evolve.
#-------------------------------
[POKEMONBOXLINK]
Name = Pokémon Box Link
NamePlural = Pokémon Box Links
Pocket = 8
Price = 0
Flags = KeyItem
Description = A device that allows you to access the Pokémon storage system. There are some places where it won't work.
#-------------------------------
[AURORATICKET]
Name = Aurora Ticket
NamePlural = Aurora Tickets
Pocket = 8
Price = 0
Flags = KeyItem
Description = A ticket required to board the ship to Doxy Island. It glows beautifully.
#-------------------------------
[OLDSEAMAP]
Name = Old Sea Map
NamePlural = Old Sea Maps
Pocket = 8
Price = 0
Flags = KeyItem
Description = A faded sea chart that shows the way to a certain island.
#-------------------------------
[DCSPASS]
Name = DCS Lifetime Pass
NamePlural = DCS Lifetime Passes
Pocket = 8
Price = 0
Flags = KeyItem
Description = A lifetime pass for DCS Services. Permits flights from any location to Seren Island.
#-------------------------------
[PUMPKABOOLATTE]
Name = Pumpkaboo Latte
NamePlural = Pumpkaboo Lattes
Pocket = 2
Price = 500
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A seasonal coffee drink inspired by the Pumpkaboo that visit Heartwood Forest in the fall. Its nutrient-rich spices heal all the status conditions of a Pokémon, making them very happy.
#-------------------------------
[APPLINCIDER]
Name = Applin Cider
NamePlural = Applin Ciders
Pocket = 2
Price = 500
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A cozy cider made by Heartwood Gym. It heals all the status conditions of a Pokémon, making them very happy.
#-------------------------------
[DAZZLINGHONEYCAKE]
Name = Dazzling Honeycake
NamePlural = Dazzling Honeycakes
Pocket = 2
Price = 500
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = Pancakes covered in Combee honey. It heals all the status conditions of a Pokémon, making them very happy.
#-------------------------------
[CHARIZARDCHAI]
Name = Charizard Chai
NamePlural = Charizard Chai
Pocket = 2
Price = 250
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A tea made from roasted berries and spices. Its warming effect thaws a frozen Pokémon, making them very happy.
#-------------------------------
[SINISTCHAMATCHA]
Name = Sinistcha Matcha
NamePlural = Sinistcha Matchas
Pocket = 2
Price = 250
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A smooth green tea inspired by Sinistcha. Its calming effect ends confusion, making Pokémon very happy.
#-------------------------------
[THUNDERFRUITPUNCH]
Name = Thunder Fruit Punch
NamePlural = Thunder Fruit Punches
Pocket = 2
Price = 250
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A mix of berry juices. Its shockingly sour taste removes paralysis from a Pokémon, making them very happy.
#-------------------------------
[DOUBLESLAPESPRESSO]
Name = Double Slap Espresso
NamePlural = Double Slap Espressos
Pocket = 2
Price = 250
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A double serving of Alola’s strongest coffee. It awakens a Pokémon from sleep, making them very happy.
#-------------------------------
[ROSERADETEA]
Name = Roserade Tea
NamePlural = Roserade Teas
Pocket = 2
Price = 250
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A tea made from Roserade petals. It lifts the effect of poison from a Pokémon, making them very happy.
#-------------------------------
[MOOMOOMOCHA]
Name = Moomoo Mocha
NamePlural = Moomoo Mochas
Pocket = 2
Price = 250
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A chocolatey coffee drink. It restores a Pokémon’s HP by 50 points, making them very happy.
#-------------------------------
[SUNNYDAYSUNDAE]
Name = Sunny Day Sundae
NamePlural = Sunny Day Sundaes
Pocket = 2
Price = 250
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A frozen dessert made from yache berries. Its cooling effect heals burns, making Pokémon very happy.
#-------------------------------
[KOMALACOFFEE]
Name = Komala Coffee
NamePlural = Komala Coffees
Pocket = 2
Price = 200
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description =  A slightly bitter drink from the Alola Region. Its rich and invigorating taste makes a Pokémon very happy. 
#-------------------------------
[TAPUCOCOA]
Name = Tapu Cocoa
NamePlural = Tapu Cocoas
Pocket = 2
Price = 150
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A sweet chocolate drink imported from the Alola Region. Its creamy taste makes a Pokémon happier. 
#-------------------------------
[PINAPJUICE]
Name = Pinap Juice
NamePlural = Pinap Juices
Pocket = 2
Price = 100
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A sour juice made from Pinap berries. Its refreshing taste makes a Pokémon a little happier.
#-------------------------------
[BERRYJELLY]
Name = Berry Jelly
NamePlural = Berry Jellies
Pocket = 2
Price = 500
FieldUse = OnPokemon
BattleUse = OnPokemon
Flags = Fling_30
Description = A nutrient-rich jelly with berries. It heals all the status conditions of a Pokémon, making them very happy.
#-------------------------------
[LUMINOUSPEARL]
Name = Luminous Pearl
NamePlural = Luminous Pearls
Pocket = 1
Price = 2100
FieldUse = OnPokemon
Flags = EvolutionStone,Fling_30
Description = A peculiar stone that makes certain species of Pokémon evolve. It is warm to the touch.
#-------------------------------
[OMINOUSPEARL]
Name = Ominous Pearl
NamePlural = Ominous Pearls
Pocket = 1
Price = 2100
FieldUse = OnPokemon
Flags = EvolutionStone,Fling_30
Description = A peculiar stone that makes certain species of Pokémon evolve. It is cold to the touch.
#-------------------------------
[ICEFANG]
Name = Ice Fang
NamePlural = Ice Fangs
Pocket = 1
Price = 3000
FieldUse = OnPokemon
Flags = EvolutionStone,Fling_30
Description = A frozen tooth that makes certain species of Pokémon evolve.
#-------------------------------
[ROYALHONEY]
Name = Royal Honey
NamePlural = Royal Honey
Pocket = 1
Price = 200
Flags = Fling_30
Description = A rare honey made by an area’s strongest Vespiquen.
#-------------------------------
[PLATEFOSSIL]
Name = Plate Fossil
NamePlural = Plate Fossils
Pocket = 1
Price = 7000
Flags = Fossil,Fling_100
Description = A fossil carved from an ancient volcano. It has a gem-like quality.
#-------------------------------
[ORBFOSSIL]
Name = Orb Fossil
NamePlural = Orb Fossils
Pocket = 1
Price = 7000
Flags = Fossil,Fling_100
Description = A fossil carved from an old sea bed. It appears to be part of its head.