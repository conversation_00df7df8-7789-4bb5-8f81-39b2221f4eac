class PokemonSaveScreen
  alias __gen4_anim__pbSaveScreen pbSaveScreen unless method_defined?(:__gen4_anim__pbSaveScreen)
  def pbSaveScreen
    ret = false
    @scene.pbStartScreen

    # Use time-based animation instead of frame-based
    # In v21.1, animations are measured in seconds rather than frames
    anim_duration = 0.1  # 0.1 seconds per animation step

    spr = AnimatedSprite.create("Graphics/Pictures/saving_emote", 4, 8, Spriteset_Map.viewport)
    spr.visible = false
    spr.ox = spr.framewidth / 2
    spr.oy = spr.frameheight / 2
    spr.z = 99999
    spr.x = $game_player.screen_x
    old_dir = $game_player.direction

    # Get the spriteset from the current scene
    spriteset = nil
    if $scene.is_a?(Scene_Map) && $scene.respond_to?(:spriteset)
      spriteset = $scene.spriteset
    end

    if spriteset && spriteset.respond_to?(:addUserSprite)
      spriteset.addUserSprite(spr)
    end

    if pbConfirmMessage(_INTL('Would you like to save the game?'))
      if SaveData.exists? && $game_temp.begun_new_game
        pbMessage(_INTL('WARNING!'))
        pbMessage(_INTL('There is a different game file that is already saved.'))
        pbMessage(_INTL("If you save now, the other file's adventure, including items and Pokémon, will be entirely lost."))
        if !pbConfirmMessageSerious(_INTL('Are you sure you want to save now and overwrite the other save file?'))
          pbSEPlay('GUI save choice')
          @scene.pbEndScreen
          spr.dispose
          return false
        end
      end

      # Animation sequence
      begin
        $game_player.turn_down
        pbWait(anim_duration * 3)  # Wait for 0.3 seconds
        $game_player.straighten
        pbWait(anim_duration * 2)  # Wait for 0.2 seconds
        $game_player.set_movement_type(:saving)

        # Animate through the 4 patterns
        4.times do |pattern|
          $game_player.pattern = pattern
          pbWait(anim_duration)  # Wait for 0.1 seconds per frame
        end

        # Find the player sprite to get the correct height
        player_sprite = nil
        if $scene.respond_to?(:spritesetGlobal) && $scene.spritesetGlobal.respond_to?(:playersprite)
          player_sprite = $scene.spritesetGlobal.playersprite
        elsif spriteset && spriteset.respond_to?(:character_sprites)
          player_sprite = spriteset.character_sprites[$game_player.id]
        end

        if player_sprite && player_sprite.respond_to?(:bitmap) && player_sprite.bitmap
          spr.y = $game_player.screen_y - player_sprite.bitmap.height / 4
        else
          spr.y = $game_player.screen_y - 16  # Fallback value
        end

        $game_temp.begun_new_game = false
        spr.play
        spr.visible = true
        pbSEPlay('GUI save choice')

        # Use seconds for wait time instead of frames
        wait_time = $DEBUG ? 2.0 : 4.0  # Wait for 2 or 4 seconds
        pbMessage(_INTL("Saving a lot of data.... \nDo not close the game.\\wtnp[{1}]", wait_time * 10))  # Convert to frames for message

        spr.visible = false

        # Animate back through the patterns
        4.times do |pattern|
          $game_player.pattern = 3 - pattern
          pbWait(anim_duration)  # Wait for 0.1 seconds per frame
        end
      rescue => e
        Console.echo_error("Error in saving animation: #{e.message}")
      end

      pbUpdateVehicle
      $game_player.direction = old_dir
      if Game.save
        $game_player.direction = 2
        pbMessage(_INTL("\\se[]{1} saved the game.\\me[GUI save game]\\wtnp[30]", $player.name))
        ret = true
      else
        $game_player.direction = 2
        pbMessage(_INTL("\\se[]Save failed.\\wtnp[30]"))
        ret = false
      end
    else
      pbSEPlay('GUI save choice')
    end
    @scene.pbEndScreen
    $game_player.turn_generic(old_dir) if $game_player.respond_to?(:turn_generic)
    spr.dispose
    return ret
  end
end

class Game_Player
  alias __saving_anim__set_movement_type set_movement_type unless method_defined?(:__saving_anim__set_movement_type)
  def set_movement_type(*args)
    type = args[0]
    case type
    when :saving
      begin
        meta = GameData::PlayerMetadata.get($player&.character_ID || 1)
        if meta && meta.respond_to?(:saving_charset)
          new_charset = pbGetPlayerCharset(meta.saving_charset)
          @character_name = new_charset if new_charset
        end
      rescue => e
        Console.echo_error("Error in saving animation charset: #{e.message}")
      end
    else
      __saving_anim__set_movement_type(*args)
    end
  end
end
