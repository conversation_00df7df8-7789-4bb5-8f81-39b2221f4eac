﻿# See the documentation on the wiki to learn how to edit this file.
#-------------------------------
[MEGAHORN]
Name = Megahorn
Type = BUG
Category = Physical
Power = 120
Accuracy = 85
TotalPP = 10
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = Using its tough and impressive horn, the user rams into the target with no letup.
#-------------------------------
[ATTACKORDER]
Name = Attack Order
Type = BUG
Category = Physical
Power = 90
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = None
Flags = CanProtect,CanMirrorMove,HighCriticalHitRate
Description = The user calls out its underlings to pummel the target. Critical hits land more easily.
#-------------------------------
[BUGBUZZ]
Name = Bug Buzz
Type = BUG
Category = Special
Power = 90
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetSpDef1
Flags = CanProtect,Can<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,Sound
EffectChance = 10
Description = The user vibrates its wings to generate a damaging sound wave. It may also lower the target's Sp. Def stat.
#-------------------------------
[FIRSTIMPRESSION]
Name = First Impression
Type = BUG
Category = Physical
Power = 90
Accuracy = 100
TotalPP = 10
Target = NearOther
Priority = 2
FunctionCode = FailsIfNotUserFirstTurn
Flags = Contact,CanProtect,CanMirrorMove
Description = Although this move has great power, it only works the first turn the user is in battle.
#-------------------------------
[POLLENPUFF]
Name = Pollen Puff
Type = BUG
Category = Special
Power = 90
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = HealAllyOrDamageFoe
Flags = CanProtect,CanMirrorMove,Bomb
Description = Fires an exploding pollen puff at enemies, or a HP-restoring one at allies.
#-------------------------------
[LEECHLIFE]
Name = Leech Life
Type = BUG
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = HealUserByHalfOfDamageDone
Flags = Contact,CanProtect,CanMirrorMove
Description = The user drains the target's blood. The user's HP is restored by half the damage taken by the target.
#-------------------------------
[LUNGE]
Name = Lunge
Type = BUG
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = LowerTargetAttack1
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 100
Description = The user makes a lunge at the target, attacking with full force. This lowers the target's Attack stat.
#-------------------------------
[XSCISSOR]
Name = X-Scissor
Type = BUG
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The user slashes at the foe by crossing its scythes or claws as if they were a pair of scissors.
#-------------------------------
[SIGNALBEAM]
Name = Signal Beam
Type = BUG
Category = Special
Power = 75
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = ConfuseTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The user attacks with a sinister beam of light. It may also confuse the target.
#-------------------------------
[SKITTERSMACK]
Name = Skitter Smack
Type = BUG
Category = Physical
Power = 70
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetSpAtk1
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 100
Description = The user skitters behind the target to attack. This also lowers the target's Sp. Atk stat.
#-------------------------------
[UTURN]
Name = U-turn
Type = BUG
Category = Physical
Power = 70
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = SwitchOutUserDamagingMove
Flags = Contact,CanProtect,CanMirrorMove
Description = After making its attack, the user rushes back to switch places with a party Pokémon in waiting.
#-------------------------------
[STEAMROLLER]
Name = Steamroller
Type = BUG
Category = Physical
Power = 65
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = FlinchTarget
Flags = Contact,CanProtect,CanMirrorMove,TramplesMinimize
EffectChance = 30
Description = The user crushes its foes by rolling over them. This attack may make the target flinch.
#-------------------------------
[BUGBITE]
Name = Bug Bite
Type = BUG
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = UserConsumeTargetBerry
Flags = Contact,CanProtect,CanMirrorMove
Description = The user bites the target. If the target is holding a Berry, the user eats it and gains its effect.
#-------------------------------
[SILVERWIND]
Name = Silver Wind
Type = BUG
Category = Special
Power = 60
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = RaiseUserMainStats1
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The foe is attacked with powdery scales blown by wind. It may also raise all the user's stats.
#-------------------------------
[FELLSTINGER]
Name = Fell Stinger
Type = BUG
Category = Physical
Power = 50
Accuracy = 100
TotalPP = 25
Target = NearOther
FunctionCode = RaiseUserAttack3IfTargetFaints
Flags = Contact,CanProtect,CanMirrorMove
Description = When the user knocks out a target with this move, the user's Attack stat rises drastically.
#-------------------------------
[STRUGGLEBUG]
Name = Struggle Bug
Type = BUG
Category = Special
Power = 50
Accuracy = 100
TotalPP = 20
Target = AllNearFoes
FunctionCode = LowerTargetSpAtk1
Flags = CanProtect,CanMirrorMove
EffectChance = 100
Description = While resisting, the user attacks the opposing Pokémon. The targets' Sp. Atk stat is reduced.
#-------------------------------
[FURYCUTTER]
Name = Fury Cutter
Type = BUG
Category = Physical
Power = 40
Accuracy = 95
TotalPP = 20
Target = NearOther
FunctionCode = PowerHigherWithConsecutiveUse
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is slashed with scythes or claws. Its power increases if it hits in succession.
#-------------------------------
[PINMISSILE]
Name = Pin Missile
Type = BUG
Category = Physical
Power = 25
Accuracy = 95
TotalPP = 20
Target = NearOther
FunctionCode = HitTwoToFiveTimes
Flags = CanProtect,CanMirrorMove
Description = Sharp spikes are shot at the target in rapid succession. They hit two to five times in a row.
#-------------------------------
[TWINEEDLE]
Name = Twineedle
Type = BUG
Category = Physical
Power = 25
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = HitTwoTimesPoisonTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 20
Description = The foe is stabbed twice by a pair of stingers. It may also poison the target.
#-------------------------------
[INFESTATION]
Name = Infestation
Type = BUG
Category = Special
Power = 20
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = BindTarget
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is infested and unable to flee for four to five turns.
#-------------------------------
[DEFENDORDER]
Name = Defend Order
Type = BUG
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = RaiseUserDefSpDef1
Description = The user calls out its underlings to shield its body, raising its Defense and Sp. Def stats.
#-------------------------------
[HEALORDER]
Name = Heal Order
Type = BUG
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = HealUserHalfOfTotalHP
Description = The user calls out its underlings to heal it. The user regains up to half of its max HP.
#-------------------------------
[POWDER]
Name = Powder
Type = BUG
Category = Status
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = TargetNextFireMoveDamagesTarget
Flags = CanProtect,CanMirrorMove,Powder
Description = The target is covered in a powder that explodes and damages it if it uses a Fire-type move.
#-------------------------------
[QUIVERDANCE]
Name = Quiver Dance
Type = BUG
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = RaiseUserSpAtkSpDefSpd1
Flags = Dance
Description = The user performs a beautiful dance. It boosts the user's Sp. Atk, Sp. Def, and Speed stats.
#-------------------------------
[RAGEPOWDER]
Name = Rage Powder
Type = BUG
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
Priority = 2
FunctionCode = RedirectAllMovesToUser
Flags = Powder
Description = The user scatters irritating powder to draw attention to itself. Opponents aim only at the user.
#-------------------------------
[SPIDERWEB]
Name = Spider Web
Type = BUG
Category = Status
Accuracy = 0
TotalPP = 10
Target = NearOther
FunctionCode = TrapTargetInBattle
Flags = CanMirrorMove
Description = The user ensnares the target with thin, gooey silk so it can't flee from battle.
#-------------------------------
[STICKYWEB]
Name = Sticky Web
Type = BUG
Category = Status
Accuracy = 0
TotalPP = 20
Target = FoeSide
FunctionCode = AddStickyWebToFoeSide
Description = Weaves a sticky net around the opposing team, which lowers their Speed stats upon switching in.
#-------------------------------
[STRINGSHOT]
Name = String Shot
Type = BUG
Category = Status
Accuracy = 95
TotalPP = 40
Target = AllNearFoes
FunctionCode = LowerTargetSpeed2
Flags = CanProtect,CanMirrorMove
Description = The foe is bound with silk blown from the user's mouth. This silk reduces the target's Speed.
#-------------------------------
[TAILGLOW]
Name = Tail Glow
Type = BUG
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = RaiseUserSpAtk3
Description = The user stares at flashing lights to focus its mind, drastically raising its Sp. Atk stat.
#-------------------------------
[HYPERSPACEFURY]
Name = Hyperspace Fury
Type = DARK
Category = Physical
Power = 100
Accuracy = 0
TotalPP = 5
Target = NearOther
FunctionCode = HoopaRemoveProtectionsBypassSubstituteLowerUserDef1
Flags = CanMirrorMove,CannotMetronome
Description = Unleashes a barrage of multi-arm attacks, skipping protections. The user's Defense stat falls.
#-------------------------------
[FOULPLAY]
Name = Foul Play
Type = DARK
Category = Physical
Power = 95
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = UseTargetAttackInsteadOfUserAttack
Flags = Contact,CanProtect,CanMirrorMove
Description = The user turns the foe's power against it. It does more damage the higher the target's Attack stat.
#-------------------------------
[FIERYWRATH]
Name = Fiery Wrath
Type = DARK
Category = Special
Power = 90
Accuracy = 100
TotalPP = 10
Target = AllNearFoes
FunctionCode = FlinchTarget
Flags = CanProtect,CanMirrorMove,CannotMetronome
EffectChance = 20
Description = The user transforms its wrath into a fire-like aura to attack. This may also make foes flinch.
#-------------------------------
[DARKESTLARIAT]
Name = Darkest Lariat
Type = DARK
Category = Physical
Power = 85
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = IgnoreTargetDefSpDefEvaStatStages
Flags = Contact,CanProtect,CanMirrorMove
Description = The user swings both arms and hits the target. Ignores the target's stat changes.
#-------------------------------
[NIGHTDAZE]
Name = Night Daze
Type = DARK
Category = Special
Power = 85
Accuracy = 95
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetAccuracy1
Flags = CanProtect,CanMirrorMove
EffectChance = 40
Description = The user lets loose a pitch-black shock wave at its target. It may also lower the target's accuracy.
#-------------------------------
[CRUNCH]
Name = Crunch
Type = DARK
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = LowerTargetDefense1
Flags = Contact,CanProtect,CanMirrorMove,Biting
EffectChance = 20
Description = The user crunches up the target with sharp fangs. It may also lower the target's Defense stat.
#-------------------------------
[DARKPULSE]
Name = Dark Pulse
Type = DARK
Category = Special
Power = 80
Accuracy = 100
TotalPP = 15
Target = Other
FunctionCode = FlinchTarget
Flags = CanProtect,CanMirrorMove,Pulse
EffectChance = 20
Description = The user releases a horrible aura imbued with dark thoughts. It may also make the target flinch.
#-------------------------------
[FALSESURRENDER]
Name = False Surrender
Type = DARK
Category = Physical
Power = 80
Accuracy = 0
TotalPP = 10
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove,CannotMetronome
Description = The user pretends to bow its head, but then it stabs the target with its disheveled hair. Never misses.
#-------------------------------
[JAWLOCK]
Name = Jaw Lock
Type = DARK
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = TrapUserAndTargetInBattle
Flags = Contact,CanProtect,CanMirrorMove
Description = This move prevents the user and the target from switching out until either of them faints.
#-------------------------------
[THROATCHOP]
Name = Throat Chop
Type = DARK
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = DisableTargetSoundMoves
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 100
Description = The user attacks the target's throat. The target cannot use sound-based moves for two turns.
#-------------------------------
[WICKEDBLOW]
Name = Wicked Blow
Type = DARK
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = AlwaysCriticalHit
Flags = Contact,CanProtect,CanMirrorMove,CannotMetronome
Description = Strikes with a fierce blow through mastery of the Dark style. Always results in a critical hit.
#-------------------------------
[LASHOUT]
Name = Lash Out
Type = DARK
Category = Physical
Power = 75
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = DoublePowerIfUserStatsLoweredThisTurn
Flags = Contact,CanProtect,CanMirrorMove
Description = The user lashes out to vent its frustration. Power is doubled if its stats dropped this turn.
#-------------------------------
[NIGHTSLASH]
Name = Night Slash
Type = DARK
Category = Physical
Power = 70
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove,HighCriticalHitRate
Description = The user slashes the target the instant an opportunity arises. Critical hits land more easily.
#-------------------------------
[SUCKERPUNCH]
Name = Sucker Punch
Type = DARK
Category = Physical
Power = 70
Accuracy = 100
TotalPP = 5
Target = NearOther
Priority = 1
FunctionCode = FailsIfTargetActed
Flags = Contact,CanProtect,CanMirrorMove
Description = This move enables the user to attack first. It fails if the target is not readying an attack, however.
#-------------------------------
[KNOCKOFF]
Name = Knock Off
Type = DARK
Category = Physical
Power = 65
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = RemoveTargetItem
Flags = Contact,CanProtect,CanMirrorMove
Description = The user slaps down the target's held item, preventing that item from being used in the battle.
#-------------------------------
[ASSURANCE]
Name = Assurance
Type = DARK
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = DoublePowerIfTargetLostHPThisTurn
Flags = Contact,CanProtect,CanMirrorMove
Description = If the target has already taken some damage in the same turn, this attack's power is doubled.
#-------------------------------
[BITE]
Name = Bite
Type = DARK
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 25
Target = NearOther
FunctionCode = FlinchTarget
Flags = Contact,CanProtect,CanMirrorMove,Biting
EffectChance = 30
Description = The target is bitten with viciously sharp fangs. It may make the target flinch.
#-------------------------------
[BRUTALSWING]
Name = Brutal Swing
Type = DARK
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 20
Target = AllNearOthers
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The user swings its body around violently to inflict damage on everything in its vicinity.
#-------------------------------
[FEINTATTACK]
Name = Feint Attack
Type = DARK
Category = Physical
Power = 60
Accuracy = 0
TotalPP = 20
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The user draws up to the foe disarmingly, then throws a sucker punch. It hits without fail.
#-------------------------------
[THIEF]
Name = Thief
Type = DARK
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 25
Target = NearOther
FunctionCode = UserTakesTargetItem
Flags = Contact,CanProtect,CanMirrorMove
Description = The user attacks and steals the foe's held item simultaneously. It can't steal if the user holds an item.
#-------------------------------
[SNARL]
Name = Snarl
Type = DARK
Category = Special
Power = 55
Accuracy = 95
TotalPP = 15
Target = AllNearFoes
FunctionCode = LowerTargetSpAtk1
Flags = CanProtect,CanMirrorMove,Sound,CannotMetronome
EffectChance = 100
Description = The user yells as if it is ranting about something, making the target's Sp. Atk stat decrease.
#-------------------------------
[PAYBACK]
Name = Payback
Type = DARK
Category = Physical
Power = 50
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = DoublePowerIfTargetActed
Flags = Contact,CanProtect,CanMirrorMove
Description = If the user moves after the target, this attack's power will be doubled.
#-------------------------------
[PURSUIT]
Name = Pursuit
Type = DARK
Category = Physical
Power = 40
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = PursueSwitchingFoe
Flags = Contact,CanProtect,CanMirrorMove
Description = An attack move that inflicts double damage if used on a target that is switching out of battle.
#-------------------------------
[BEATUP]
Name = Beat Up
Type = DARK
Category = Physical
Power = 1
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = HitOncePerUserTeamMember
Flags = CanProtect,CanMirrorMove
Description = The user gets all the party Pokémon to attack the foe. The more party Pokémon, the more damage.
#-------------------------------
[FLING]
Name = Fling
Type = DARK
Category = Physical
Power = 1
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = ThrowUserItemAtTarget
Flags = CanProtect,CanMirrorMove
Description = The user flings its held item at the target to attack. Its power and effects depend on the item.
#-------------------------------
[POWERTRIP]
Name = Power Trip
Type = DARK
Category = Physical
Power = 1
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = PowerHigherWithUserPositiveStatStages
Flags = Contact,CanProtect,CanMirrorMove
Description = The user boasts of its strength. Power increases the more the user's stats are raised.
#-------------------------------
[PUNISHMENT]
Name = Punishment
Type = DARK
Category = Physical
Power = 1
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = PowerHigherWithTargetPositiveStatStages
Flags = Contact,CanProtect,CanMirrorMove
Description = This attack's power increases the more the target has powered up with stat changes.
#-------------------------------
[DARKVOID]
Name = Dark Void
Type = DARK
Category = Status
Accuracy = 50
TotalPP = 10
Target = AllNearFoes
FunctionCode = SleepTargetIfUserDarkrai
Flags = CanProtect,CanMirrorMove
Description = Opposing Pokémon are dragged into a world of total darkness that makes them sleep.
#-------------------------------
[EMBARGO]
Name = Embargo
Type = DARK
Category = Status
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = StartTargetCannotUseItem
Flags = CanProtect,CanMirrorMove
Description = It prevents the target from using its held item. Its Trainer is also prevented from using items on it.
#-------------------------------
[FAKETEARS]
Name = Fake Tears
Type = DARK
Category = Status
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = LowerTargetSpDef2
Flags = CanProtect,CanMirrorMove
Description = The user feigns crying to fluster the target, harshly lowering its Sp. Def stat.
#-------------------------------
[FLATTER]
Name = Flatter
Type = DARK
Category = Status
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = RaiseTargetSpAtk1ConfuseTarget
Flags = CanProtect,CanMirrorMove
Description = Flattery is used to confuse the target. However, it also raises the target's Sp. Atk stat.
#-------------------------------
[HONECLAWS]
Name = Hone Claws
Type = DARK
Category = Status
Accuracy = 0
TotalPP = 15
Target = User
FunctionCode = RaiseUserAtkAcc1
Description = The user sharpens its claws to boost its Attack stat and accuracy.
#-------------------------------
[MEMENTO]
Name = Memento
Type = DARK
Category = Status
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = UserFaintsLowerTargetAtkSpAtk2
Flags = CanProtect,CanMirrorMove
Description = The user faints when using this move. In return, it harshly lowers the target's Attack and Sp. Atk.
#-------------------------------
[NASTYPLOT]
Name = Nasty Plot
Type = DARK
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = RaiseUserSpAtk2
Description = The user stimulates its brain by thinking bad thoughts. It sharply raises the user's Sp. Atk.
#-------------------------------
[OBSTRUCT]
Name = Obstruct
Type = DARK
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
Priority = 4
FunctionCode = ProtectUserFromDamagingMovesObstruct
Description = The user protects itself from all attacks. Direct contact lowers the attacker's Defense.
#-------------------------------
[PARTINGSHOT]
Name = Parting Shot
Type = DARK
Category = Status
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = LowerTargetAtkSpAtk1SwitchOutUser
Flags = CanProtect,CanMirrorMove,Sound
Description = With a parting threat, the user lowers the target's Attack and Sp. Atk stats. Then it switches out.
#-------------------------------
[QUASH]
Name = Quash
Type = DARK
Category = Status
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = TargetActsLast
Flags = CanProtect,CanMirrorMove
Description = The user suppresses the target and makes its move go last.
#-------------------------------
[SNATCH]
Name = Snatch
Type = DARK
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
Priority = 4
FunctionCode = StealAndUseBeneficialStatusMove
Description = The user steals the effects of any healing or stat-changing move the foe attempts to use.
#-------------------------------
[SWITCHEROO]
Name = Switcheroo
Type = DARK
Category = Status
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = UserTargetSwapItems
Flags = CanProtect,CanMirrorMove
Description = The user trades held items with the target faster than the eye can follow.
#-------------------------------
[TAUNT]
Name = Taunt
Type = DARK
Category = Status
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = DisableTargetStatusMoves
Flags = CanProtect,CanMirrorMove
Description = The target is taunted into a rage that allows it to use only attack moves for three turns.
#-------------------------------
[TOPSYTURVY]
Name = Topsy-Turvy
Type = DARK
Category = Status
Accuracy = 0
TotalPP = 20
Target = NearOther
FunctionCode = InvertTargetStatStages
Flags = CanProtect,CanMirrorMove
Description = All stat changes affecting the target turn topsy-turvy and become the opposite of what they were.
#-------------------------------
[TORMENT]
Name = Torment
Type = DARK
Category = Status
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = DisableTargetUsingSameMoveConsecutively
Flags = CanProtect,CanMirrorMove
Description = The user torments and enrages the foe, making it incapable of using the same move twice in a row.
#-------------------------------
[ETERNABEAM]
Name = Eternabeam
Type = DRAGON
Category = Special
Power = 160
Accuracy = 90
TotalPP = 5
Target = NearOther
FunctionCode = AttackAndSkipNextTurn
Flags = CanProtect,CanMirrorMove,CannotMetronome
Description = This is Eternatus's most powerful attack in its original form. The user can't move on the next turn.
#-------------------------------
[DRAGONENERGY]
Name = Dragon Energy
Type = DRAGON
Category = Special
Power = 150
Accuracy = 100
TotalPP = 5
Target = AllNearFoes
FunctionCode = PowerHigherWithUserHP
Flags = CanProtect,CanMirrorMove,CannotMetronome
Description = Converts life-force into power to attack. The lower the user's HP, the lower the move's power.
#-------------------------------
[ROAROFTIME]
Name = Roar of Time
Type = DRAGON
Category = Special
Power = 150
Accuracy = 90
TotalPP = 5
Target = NearOther
FunctionCode = AttackAndSkipNextTurn
Flags = CanProtect,CanMirrorMove
Description = The user blasts the target with power that distorts even time. The user must rest on the next turn.
#-------------------------------
[DRACOMETEOR]
Name = Draco Meteor
Type = DRAGON
Category = Special
Power = 130
Accuracy = 90
TotalPP = 5
Target = NearOther
FunctionCode = LowerUserSpAtk2
Flags = CanProtect,CanMirrorMove
Description = Comets are summoned down from the sky. The attack's recoil harshly reduces the user's Sp. Atk stat.
#-------------------------------
[OUTRAGE]
Name = Outrage
Type = DRAGON
Category = Physical
Power = 120
Accuracy = 100
TotalPP = 10
Target = RandomNearFoe
FunctionCode = MultiTurnAttackConfuseUserAtEnd
Flags = Contact,CanProtect,CanMirrorMove
Description = The user rampages and attacks for two to three turns. It then becomes confused, however.
#-------------------------------
[CLANGINGSCALES]
Name = Clanging Scales
Type = DRAGON
Category = Special
Power = 110
Accuracy = 100
TotalPP = 5
Target = AllNearFoes
FunctionCode = LowerUserDefense1
Flags = CanProtect,CanMirrorMove,Sound
Description = The user rubs its scales and makes a huge noise. Also lowers the user's Defense stat.
#-------------------------------
[COREENFORCER]
Name = Core Enforcer
Type = DRAGON
Category = Special
Power = 100
Accuracy = 100
TotalPP = 10
Target = AllNearFoes
FunctionCode = NegateTargetAbilityIfTargetActed
Flags = CanProtect,CanMirrorMove
Description = If the target has already moved this turn, the effect of its Ability is negated.
#-------------------------------
[DRAGONRUSH]
Name = Dragon Rush
Type = DRAGON
Category = Physical
Power = 100
Accuracy = 75
TotalPP = 10
Target = NearOther
FunctionCode = FlinchTarget
Flags = Contact,CanProtect,CanMirrorMove,TramplesMinimize
EffectChance = 20
Description = The user tackles the foe while exhibiting overwhelming menace. It may also make the target flinch.
#-------------------------------
[DYNAMAXCANNON]
Name = Dynamax Cannon
Type = DRAGON
Category = Special
Power = 100
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = None
Flags = CanProtect,CannotMetronome
Description = The user unleashes a strong beam from its core.
#-------------------------------
[SPACIALREND]
Name = Spacial Rend
Type = DRAGON
Category = Special
Power = 100
Accuracy = 95
TotalPP = 5
Target = NearOther
FunctionCode = None
Flags = CanProtect,CanMirrorMove,HighCriticalHitRate
Description = The user tears the target along with the space around it. Critical hits land more easily.
#-------------------------------
[DRAGONHAMMER]
Name = Dragon Hammer
Type = DRAGON
Category = Physical
Power = 90
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The user uses its body like a hammer to attack the target and inflict damage.
#-------------------------------
[DRAGONPULSE]
Name = Dragon Pulse
Type = DRAGON
Category = Special
Power = 85
Accuracy = 100
TotalPP = 10
Target = Other
FunctionCode = None
Flags = CanProtect,CanMirrorMove,Pulse
Description = The target is attacked with a shock wave generated by the user's gaping mouth.
#-------------------------------
[DRAGONCLAW]
Name = Dragon Claw
Type = DRAGON
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The user slashes the target with huge, sharp claws.
#-------------------------------
[BREAKINGSWIPE]
Name = Breaking Swipe
Type = DRAGON
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 15
Target = AllNearFoes
FunctionCode = LowerTargetAttack1
Flags = Contact,CanProtect,CanMirrorMove,CannotMetronome
EffectChance = 100
Description = The user swings its tough tail wildly and attacks all foes. This also lowers their Attack stats.
#-------------------------------
[DRAGONBREATH]
Name = Dragon Breath
Type = DRAGON
Category = Special
Power = 60
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = ParalyzeTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 30
Description = The user exhales a mighty gust that inflicts damage. It may also leave the target with paralysis.
#-------------------------------
[DRAGONTAIL]
Name = Dragon Tail
Type = DRAGON
Category = Physical
Power = 60
Accuracy = 90
TotalPP = 10
Target = NearOther
Priority = -6
FunctionCode = SwitchOutTargetDamagingMove
Flags = Contact,CanProtect,CanMirrorMove
Description = The user knocks away the target and drags out another Pokémon in its party. In the wild, the battle ends.
#-------------------------------
[DRAGONDARTS]
Name = Dragon Darts
Type = DRAGON
Category = Physical
Power = 50
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = HitTwoTimesTargetThenTargetAlly
Flags = CanProtect,CanMirrorMove
Description = The user attacks twice using Dreepy. If there are two targets, this move hits each one once.
#-------------------------------
[DUALCHOP]
Name = Dual Chop
Type = DRAGON
Category = Physical
Power = 40
Accuracy = 90
TotalPP = 15
Target = NearOther
FunctionCode = HitTwoTimes
Flags = Contact,CanProtect,CanMirrorMove
Description = The user attacks its target by hitting it with brutal strikes. The target is hit twice in a row.
#-------------------------------
[TWISTER]
Name = Twister
Type = DRAGON
Category = Special
Power = 40
Accuracy = 100
TotalPP = 20
Target = AllNearFoes
FunctionCode = FlinchTargetDoublePowerIfTargetInSky
Flags = CanProtect,CanMirrorMove
EffectChance = 20
Description = The user whips up a vicious tornado to tear at the opposing team. It may also make targets flinch.
#-------------------------------
[SCALESHOT]
Name = Scale Shot
Type = DRAGON
Category = Physical
Power = 25
Accuracy = 90
TotalPP = 20
Target = NearOther
FunctionCode = HitTwoToFiveTimesRaiseUserSpd1LowerUserDef1
Flags = CanProtect,CanMirrorMove
Description = Attacks by shooting scales two to five times in a row. Boosts the user's Speed but lowers its Defense.
#-------------------------------
[DRAGONRAGE]
Name = Dragon Rage
Type = DRAGON
Category = Special
Power = 1
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = FixedDamage40
Flags = CanProtect,CanMirrorMove
Description = This attack hits the target with a shock wave of pure rage. This attack always inflicts 40 HP damage.
#-------------------------------
[CLANGOROUSSOUL]
Name = Clangorous Soul
Type = DRAGON
Category = Status
Accuracy = 0
TotalPP = 5
Target = User
FunctionCode = RaiseUserMainStats1LoseThirdOfTotalHP
Flags = Sound,CannotMetronome,Dance
Description = The user raises all its stats by using some of its HP.
#-------------------------------
[DRAGONDANCE]
Name = Dragon Dance
Type = DRAGON
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = RaiseUserAtkSpd1
Flags = Dance
Description = The user vigorously performs a mystic, powerful dance that boosts its Attack and Speed stats.
#-------------------------------
[BOLTSTRIKE]
Name = Bolt Strike
Type = ELECTRIC
Category = Physical
Power = 130
Accuracy = 85
TotalPP = 5
Target = NearOther
FunctionCode = ParalyzeTarget
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 20
Description = The user charges at its foe, surrounding itself with lightning. It may also leave the target paralyzed.
#-------------------------------
[VOLTTACKLE]
Name = Volt Tackle
Type = ELECTRIC
Category = Physical
Power = 120
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = RecoilThirdOfDamageDealtParalyzeTarget
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 10
Description = The user electrifies itself, then charges at the foe. It causes considerable damage to the user as well.
#-------------------------------
[ZAPCANNON]
Name = Zap Cannon
Type = ELECTRIC
Category = Special
Power = 120
Accuracy = 50
TotalPP = 5
Target = NearOther
FunctionCode = ParalyzeTarget
Flags = CanProtect,CanMirrorMove,Bomb
EffectChance = 100
Description = The user fires an electric blast like a cannon to inflict damage and cause paralysis.
#-------------------------------
[AURAWHEEL]
Name = Aura Wheel
Type = ELECTRIC
Category = Physical
Power = 110
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = TypeDependsOnUserMorpekoFormRaiseUserSpeed1
Flags = CanProtect,CanMirrorMove,CannotMetronome
EffectChance = 100
Description = Morpeko attacks and raises its Speed with energy stored in its cheeks. Type changes with the user's form.
#-------------------------------
[THUNDER]
Name = Thunder
Type = ELECTRIC
Category = Special
Power = 110
Accuracy = 70
TotalPP = 10
Target = NearOther
FunctionCode = ParalyzeTargetAlwaysHitsInRainHitsTargetInSky
Flags = CanProtect,CanMirrorMove
EffectChance = 30
Description = A wicked thunderbolt is dropped on the foe to inflict damage. It may also leave the target paralyzed.
#-------------------------------
[FUSIONBOLT]
Name = Fusion Bolt
Type = ELECTRIC
Category = Physical
Power = 100
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = DoublePowerAfterFusionFlare
Flags = CanProtect,CanMirrorMove
Description = The user throws down a giant thunderbolt. It does more damage if influenced by an enormous flame.
#-------------------------------
[PLASMAFISTS]
Name = Plasma Fists
Type = ELECTRIC
Category = Physical
Power = 100
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = NormalMovesBecomeElectric
Flags = Contact,CanProtect,CanMirrorMove,Punching,CannotMetronome
Description = The user attacks with electrically charged fists. Normal-type moves become Electric-type.
#-------------------------------
[THUNDERBOLT]
Name = Thunderbolt
Type = ELECTRIC
Category = Special
Power = 90
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = ParalyzeTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = A strong electric blast is loosed at the target. It may also leave the target with paralysis.
#-------------------------------
[WILDCHARGE]
Name = Wild Charge
Type = ELECTRIC
Category = Physical
Power = 90
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = RecoilQuarterOfDamageDealt
Flags = Contact,CanProtect,CanMirrorMove
Description = The user shrouds itself in electricity and smashes into its foe. It also damages the user a little.
#-------------------------------
[BOLTBEAK]
Name = Bolt Beak
Type = ELECTRIC
Category = Physical
Power = 85
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = DoublePowerIfTargetNotActed
Flags = Contact,CanProtect,CanMirrorMove
Description = Stabs the target with an electrified beak. Power is doubled if the user attacks first.
#-------------------------------
[DISCHARGE]
Name = Discharge
Type = ELECTRIC
Category = Special
Power = 80
Accuracy = 100
TotalPP = 15
Target = AllNearOthers
FunctionCode = ParalyzeTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 30
Description = A flare of electricity is loosed to strike the area around the user. It may also cause paralysis.
#-------------------------------
[OVERDRIVE]
Name = Overdrive
Type = ELECTRIC
Category = Special
Power = 80
Accuracy = 100
TotalPP = 10
Target = AllNearFoes
FunctionCode = None
Flags = CanProtect,CanMirrorMove,Sound,CannotMetronome
Description = The user attacks all foes by twanging a guitar or bass guitar, causing a huge echo and strong vibration.
#-------------------------------
[THUNDERCAGE]
Name = Thunder Cage
Type = ELECTRIC
Category = Special
Power = 80
Accuracy = 90
TotalPP = 15
Target = NearOther
FunctionCode = BindTarget
Flags = CanProtect,CanMirrorMove,CannotMetronome
Description = The user traps the target in a cage of sparking electricity for four to five turns.
#-------------------------------
[ZINGZAP]
Name = Zing Zap
Type = ELECTRIC
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = FlinchTarget
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 30
Description = A strong electric blast crashes down on the target. This may also make the target flinch.
#-------------------------------
[THUNDERPUNCH]
Name = Thunder Punch
Type = ELECTRIC
Category = Physical
Power = 75
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = ParalyzeTarget
Flags = Contact,CanProtect,CanMirrorMove,Punching
EffectChance = 10
Description = The target is punched with an electrified fist. It may also leave the target with paralysis.
#-------------------------------
[RISINGVOLTAGE]
Name = Rising Voltage
Type = ELECTRIC
Category = Special
Power = 70
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = DoublePowerInElectricTerrain
Flags = CanProtect,CanMirrorMove
Description = The user attacks with electric voltage rising from the ground. Power is doubled on Electric Terrain.
#-------------------------------
[VOLTSWITCH]
Name = Volt Switch
Type = ELECTRIC
Category = Special
Power = 70
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = SwitchOutUserDamagingMove
Flags = CanProtect,CanMirrorMove
Description = After making its attack, the user rushes back to switch places with a party Pokémon in waiting.
#-------------------------------
[PARABOLICCHARGE]
Name = Parabolic Charge
Type = ELECTRIC
Category = Special
Power = 65
Accuracy = 100
TotalPP = 20
Target = AllNearOthers
FunctionCode = HealUserByHalfOfDamageDone
Flags = CanProtect,CanMirrorMove
Description = The user attacks everything around it. The user's HP is restored by half the damage dealt.
#-------------------------------
[SPARK]
Name = Spark
Type = ELECTRIC
Category = Physical
Power = 65
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = ParalyzeTarget
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 30
Description = The user throws an electrically charged tackle at the target. It may also leave the target with paralysis.
#-------------------------------
[THUNDERFANG]
Name = Thunder Fang
Type = ELECTRIC
Category = Physical
Power = 65
Accuracy = 95
TotalPP = 15
Target = NearOther
FunctionCode = ParalyzeFlinchTarget
Flags = Contact,CanProtect,CanMirrorMove,Biting
EffectChance = 101
Description = The user bites with electrified fangs. It may also make the target flinch or leave it with paralysis.
#-------------------------------
[SHOCKWAVE]
Name = Shock Wave
Type = ELECTRIC
Category = Special
Power = 60
Accuracy = 0
TotalPP = 20
Target = NearOther
FunctionCode = None
Flags = CanProtect,CanMirrorMove
Description = The user strikes the target with a quick jolt of electricity. This attack cannot be evaded.
#-------------------------------
[ELECTROWEB]
Name = Electroweb
Type = ELECTRIC
Category = Special
Power = 55
Accuracy = 95
TotalPP = 15
Target = AllNearFoes
FunctionCode = LowerTargetSpeed1
Flags = CanProtect,CanMirrorMove
EffectChance = 100
Description = The user captures and attacks foes by using an electric net, which lowers their Speed stat.
#-------------------------------
[CHARGEBEAM]
Name = Charge Beam
Type = ELECTRIC
Category = Special
Power = 50
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = RaiseUserSpAtk1
Flags = CanProtect,CanMirrorMove
EffectChance = 70
Description = The user fires a concentrated bundle of electricity. It may also raise the user's Sp. Atk stat.
#-------------------------------
[THUNDERSHOCK]
Name = Thunder Shock
Type = ELECTRIC
Category = Special
Power = 40
Accuracy = 100
TotalPP = 30
Target = NearOther
FunctionCode = ParalyzeTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = A jolt of electricity is hurled at the foe to inflict damage. It may also leave the target with paralysis.
#-------------------------------
[NUZZLE]
Name = Nuzzle
Type = ELECTRIC
Category = Physical
Power = 20
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = ParalyzeTarget
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 100
Description = The user nuzzles its electrified cheeks against the target. This also leaves the target with paralysis.
#-------------------------------
[ELECTROBALL]
Name = Electro Ball
Type = ELECTRIC
Category = Special
Power = 1
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = PowerHigherWithUserFasterThanTarget
Flags = CanProtect,CanMirrorMove,Bomb
Description = The user hurls an electric orb at the foe. It does more damage the faster the user is.
#-------------------------------
[CHARGE]
Name = Charge
Type = ELECTRIC
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = RaiseUserSpDef1PowerUpElectricMove
Description = The user boosts the power of the Electric move it uses next. It also raises the user's Sp. Def stat.
#-------------------------------
[EERIEIMPULSE]
Name = Eerie Impulse
Type = ELECTRIC
Category = Status
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = LowerTargetSpAtk2
Flags = CanProtect,CanMirrorMove
Description = The user's body generates an eerie impulse. Harshly lowers the target's Sp. Atk stat.
#-------------------------------
[ELECTRICTERRAIN]
Name = Electric Terrain
Type = ELECTRIC
Category = Status
Accuracy = 0
TotalPP = 10
Target = BothSides
FunctionCode = StartElectricTerrain
Description = The user electrifies the ground for five turns. Pokémon on the ground no longer fall asleep.
#-------------------------------
[ELECTRIFY]
Name = Electrify
Type = ELECTRIC
Category = Status
Accuracy = 0
TotalPP = 20
Target = NearOther
FunctionCode = TargetMovesBecomeElectric
Flags = CanProtect,CanMirrorMove
Description = If the target uses a move after being electrified, that move becomes Electric-type.
#-------------------------------
[IONDELUGE]
Name = Ion Deluge
Type = ELECTRIC
Category = Status
Accuracy = 0
TotalPP = 25
Target = BothSides
Priority = 1
FunctionCode = NormalMovesBecomeElectric
Description = The user disperses electrically charged particles. Normal-type moves become Electric-type.
#-------------------------------
[MAGNETRISE]
Name = Magnet Rise
Type = ELECTRIC
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = StartUserAirborne
Description = The user levitates using electrically generated magnetism for five turns.
#-------------------------------
[MAGNETICFLUX]
Name = Magnetic Flux
Type = ELECTRIC
Category = Status
Accuracy = 0
TotalPP = 20
Target = UserAndAllies
FunctionCode = RaisePlusMinusUserAndAlliesDefSpDef1
Description = Manipulates magnetic fields to raise the Defense and Sp. Def stats of allies with Plus or Minus Abilities.
#-------------------------------
[THUNDERWAVE]
Name = Thunder Wave
Type = ELECTRIC
Category = Status
Accuracy = 90
TotalPP = 20
Target = NearOther
FunctionCode = ParalyzeTargetIfNotTypeImmune
Flags = CanProtect,CanMirrorMove
Description = A weak electric charge is launched at the target. It causes paralysis if it hits.
#-------------------------------
[LIGHTOFRUIN]
Name = Light of Ruin
Type = FAIRY
Category = Special
Power = 140
Accuracy = 90
TotalPP = 5
Target = NearOther
FunctionCode = RecoilHalfOfDamageDealt
Flags = CanProtect,CanMirrorMove,CannotMetronome
Description = Fires a powerful beam of light drawn from the Eternal Flower. It also damages the user a lot.
#-------------------------------
[FLEURCANNON]
Name = Fleur Cannon
Type = FAIRY
Category = Special
Power = 130
Accuracy = 90
TotalPP = 5
Target = NearOther
FunctionCode = LowerUserSpAtk2
Flags = CanProtect,CanMirrorMove,CannotMetronome
Description = The user unleashes a strong beam. The attack's recoil harshly lowers the user's Sp. Atk stat.
#-------------------------------
[MISTYEXPLOSION]
Name = Misty Explosion
Type = FAIRY
Category = Special
Power = 100
Accuracy = 100
TotalPP = 5
Target = AllNearOthers
FunctionCode = UserFaintsPowersUpInMistyTerrainExplosive
Flags = CanProtect,CanMirrorMove
Description = The user attacks everything around and faints upon using this move. Power increases on Misty Terrain.
#-------------------------------
[MOONBLAST]
Name = Moonblast
Type = FAIRY
Category = Special
Power = 95
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = LowerTargetSpAtk1
Flags = CanProtect,CanMirrorMove
EffectChance = 30
Description = The user attacks by borrowing the power of the moon. This may also lower the target's Sp. Atk stat.
#-------------------------------
[PLAYROUGH]
Name = Play Rough
Type = FAIRY
Category = Physical
Power = 90
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetAttack1
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 10
Description = The user plays rough with the target and attacks it. This may also lower the target's Attack stat.
#-------------------------------
[STRANGESTEAM]
Name = Strange Steam
Type = FAIRY
Category = Special
Power = 90
Accuracy = 95
TotalPP = 10
Target = NearOther
FunctionCode = ConfuseTarget
Flags = CanProtect,CanMirrorMove,CannotMetronome
EffectChance = 20
Description = The user attacks the target by emitting steam. This may also confuse the target.
#-------------------------------
[DAZZLINGGLEAM]
Name = Dazzling Gleam
Type = FAIRY
Category = Special
Power = 80
Accuracy = 100
TotalPP = 10
Target = AllNearFoes
FunctionCode = None
Flags = CanProtect,CanMirrorMove
Description = The user damages opposing Pokémon by emitting a powerful flash.
#-------------------------------
[SPIRITBREAK]
Name = Spirit Break
Type = FAIRY
Category = Physical
Power = 75
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = LowerTargetSpAtk1
Flags = Contact,CanProtect,CanMirrorMove,CannotMetronome
EffectChance = 100
Description = The user attacks the target with immense force. This also lowers the target's Sp. Atk stat.
#-------------------------------
[DRAININGKISS]
Name = Draining Kiss
Type = FAIRY
Category = Special
Power = 50
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = HealUserByThreeQuartersOfDamageDone
Flags = Contact,CanProtect,CanMirrorMove
Description = The user steals the target's HP with a kiss. The user's HP is restored by over half of the damage dealt.
#-------------------------------
[DISARMINGVOICE]
Name = Disarming Voice
Type = FAIRY
Category = Special
Power = 40
Accuracy = 0
TotalPP = 15
Target = AllNearFoes
FunctionCode = None
Flags = CanProtect,CanMirrorMove,Sound
Description = Letting out a charming cry, the user does emotional damage to foes. This attack never misses.
#-------------------------------
[FAIRYWIND]
Name = Fairy Wind
Type = FAIRY
Category = Special
Power = 40
Accuracy = 100
TotalPP = 30
Target = NearOther
FunctionCode = None
Flags = CanProtect,CanMirrorMove
Description = The user stirs up a fairy wind and strikes the target with it.
#-------------------------------
[NATURESMADNESS]
Name = Nature's Madness
Type = FAIRY
Category = Special
Power = 1
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = FixedDamageHalfTargetHP
Flags = CanProtect,CanMirrorMove,CannotMetronome
Description = The user hits the target with the force of nature. It halves the target's HP.
#-------------------------------
[AROMATICMIST]
Name = Aromatic Mist
Type = FAIRY
Category = Status
Accuracy = 0
TotalPP = 20
Target = NearAlly
FunctionCode = RaiseTargetSpDef1
Description = The user raises the Sp. Def stat of an ally Pokémon by using a mysterious aroma.
#-------------------------------
[BABYDOLLEYES]
Name = Baby-Doll Eyes
Type = FAIRY
Category = Status
Accuracy = 100
TotalPP = 30
Target = NearOther
Priority = 1
FunctionCode = LowerTargetAttack1
Flags = CanProtect,CanMirrorMove
Description = The user stares with its baby-doll eyes, which lowers the target's Attack stat. Always goes first.
#-------------------------------
[CHARM]
Name = Charm
Type = FAIRY
Category = Status
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = LowerTargetAttack2
Flags = CanProtect,CanMirrorMove
Description = The user charmingly gazes at the foe, making it less wary. The target's Attack is harshly lowered.
#-------------------------------
[CRAFTYSHIELD]
Name = Crafty Shield
Type = FAIRY
Category = Status
Accuracy = 0
TotalPP = 10
Target = UserSide
Priority = 3
FunctionCode = ProtectUserSideFromStatusMoves
Description = The user protects itself and its allies from status moves with a mysterious power.
#-------------------------------
[DECORATE]
Name = Decorate
Type = FAIRY
Category = Status
Accuracy = 0
TotalPP = 15
Target = NearOther
FunctionCode = RaiseTargetAtkSpAtk2
Flags = CannotMetronome
Description = The user sharply raises the target's Attack and Sp. Atk stats by decorating the target.
#-------------------------------
[FAIRYLOCK]
Name = Fairy Lock
Type = FAIRY
Category = Status
Accuracy = 0
TotalPP = 10
Target = BothSides
FunctionCode = TrapAllBattlersInBattleForOneTurn
Flags = CanMirrorMove
Description = By locking down the battlefield, the user keeps all Pokémon from fleeing during the next turn.
#-------------------------------
[FLORALHEALING]
Name = Floral Healing
Type = FAIRY
Category = Status
Accuracy = 0
TotalPP = 10
Target = NearOther
FunctionCode = HealTargetDependingOnGrassyTerrain
Flags = CanProtect
Description = The user restores the target's HP by up to half of its max HP. It restores more HP when the terrain is grass.
#-------------------------------
[FLOWERSHIELD]
Name = Flower Shield
Type = FAIRY
Category = Status
Accuracy = 0
TotalPP = 10
Target = AllBattlers
FunctionCode = RaiseGrassBattlersDef1
Description = The user raises the Defense stats of all Grass-type Pokémon in battle with a mysterious power.
#-------------------------------
[GEOMANCY]
Name = Geomancy
Type = FAIRY
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = TwoTurnAttackRaiseUserSpAtkSpDefSpd2
Description = The user absorbs energy and sharply raises its Sp. Atk, Sp. Def, and Speed stats on the next turn.
#-------------------------------
[MISTYTERRAIN]
Name = Misty Terrain
Type = FAIRY
Category = Status
Accuracy = 0
TotalPP = 10
Target = BothSides
FunctionCode = StartMistyTerrain
Description = The user covers the ground with mist for five turns. Grounded Pokémon can't gain status conditions.
#-------------------------------
[MOONLIGHT]
Name = Moonlight
Type = FAIRY
Category = Status
Accuracy = 0
TotalPP = 5
Target = User
FunctionCode = HealUserDependingOnWeather
Description = The user restores its own HP. The amount of HP regained varies with the weather.
#-------------------------------
[SWEETKISS]
Name = Sweet Kiss
Type = FAIRY
Category = Status
Accuracy = 75
TotalPP = 10
Target = NearOther
FunctionCode = ConfuseTarget
Flags = CanProtect,CanMirrorMove
Description = The user kisses the target with a sweet, angelic cuteness that causes confusion.
#-------------------------------
[FOCUSPUNCH]
Name = Focus Punch
Type = FIGHTING
Category = Physical
Power = 150
Accuracy = 100
TotalPP = 20
Target = NearOther
Priority = -3
FunctionCode = FailsIfUserDamagedThisTurn
Flags = Contact,CanProtect,Punching
Description = The user focuses its mind before launching a punch. It will fail if the user is hit before it is used.
#-------------------------------
[METEORASSAULT]
Name = Meteor Assault
Type = FIGHTING
Category = Physical
Power = 150
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = AttackAndSkipNextTurn
Flags = CanProtect,CanMirrorMove,CannotMetronome
Description = The user attacks wildly with its thick leek. The user can't move on the next turn.
#-------------------------------
[HIGHJUMPKICK]
Name = High Jump Kick
Type = FIGHTING
Category = Physical
Power = 130
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = CrashDamageIfFailsUnusableInGravity
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is attacked with a knee kick from a jump. If it misses, the user is hurt instead.
#-------------------------------
[CLOSECOMBAT]
Name = Close Combat
Type = FIGHTING
Category = Physical
Power = 120
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = LowerUserDefSpDef1
Flags = Contact,CanProtect,CanMirrorMove
Description = The user fights the foe up close without guarding itself. It also cuts the user's Defense and Sp. Def.
#-------------------------------
[FOCUSBLAST]
Name = Focus Blast
Type = FIGHTING
Category = Special
Power = 120
Accuracy = 70
TotalPP = 5
Target = NearOther
FunctionCode = LowerTargetSpDef1
Flags = CanProtect,CanMirrorMove,Bomb
EffectChance = 10
Description = The user heightens its mental focus and unleashes its power. It may also lower the target's Sp. Def.
#-------------------------------
[SUPERPOWER]
Name = Superpower
Type = FIGHTING
Category = Physical
Power = 120
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = LowerUserAtkDef1
Flags = Contact,CanProtect,CanMirrorMove
Description = The user attacks the target with great power. However, it also lowers the user's Attack and Defense.
#-------------------------------
[CROSSCHOP]
Name = Cross Chop
Type = FIGHTING
Category = Physical
Power = 100
Accuracy = 80
TotalPP = 5
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove,HighCriticalHitRate
Description = The user delivers a double chop with its forearms crossed. Critical hits land more easily.
#-------------------------------
[DYNAMICPUNCH]
Name = Dynamic Punch
Type = FIGHTING
Category = Physical
Power = 100
Accuracy = 50
TotalPP = 5
Target = NearOther
FunctionCode = ConfuseTarget
Flags = Contact,CanProtect,CanMirrorMove,Punching
EffectChance = 100
Description = The user punches the target with full, concentrated power. It confuses the target if it hits.
#-------------------------------
[FLYINGPRESS]
Name = Flying Press
Type = FIGHTING
Category = Physical
Power = 100
Accuracy = 95
TotalPP = 10
Target = Other
FunctionCode = EffectivenessIncludesFlyingType
Flags = Contact,CanProtect,CanMirrorMove,TramplesMinimize
Description = The user dives down onto the target from the sky. This move is Fighting and Flying type simultaneously.
#-------------------------------
[HAMMERARM]
Name = Hammer Arm
Type = FIGHTING
Category = Physical
Power = 100
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = LowerUserSpeed1
Flags = Contact,CanProtect,CanMirrorMove,Punching
Description = The user swings and hits with its strong and heavy fist. It lowers the user's Speed, however.
#-------------------------------
[JUMPKICK]
Name = Jump Kick
Type = FIGHTING
Category = Physical
Power = 100
Accuracy = 95
TotalPP = 10
Target = NearOther
FunctionCode = CrashDamageIfFailsUnusableInGravity
Flags = Contact,CanProtect,CanMirrorMove
Description = The user jumps up high, then strikes with a kick. If the kick misses, the user hurts itself.
#-------------------------------
[SACREDSWORD]
Name = Sacred Sword
Type = FIGHTING
Category = Physical
Power = 90
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = IgnoreTargetDefSpDefEvaStatStages
Flags = Contact,CanProtect,CanMirrorMove
Description = The user attacks by slicing with its long horns. The target's stat changes don't affect the damage.
#-------------------------------
[THUNDEROUSKICK]
Name = Thunderous Kick
Type = FIGHTING
Category = Physical
Power = 90
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetDefense1
Flags = Contact,CanProtect,CanMirrorMove,CannotMetronome
EffectChance = 100
Description = Overwhelms the target with lightning-like movement, then kicks. Lowers the target's Defense stat.
#-------------------------------
[SECRETSWORD]
Name = Secret Sword
Type = FIGHTING
Category = Special
Power = 85
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = UseTargetDefenseInsteadOfTargetSpDef
Flags = CanProtect,CanMirrorMove,CannotMetronome
Description = The user cuts with its long horn. The odd power contained in it does physical damage to the foe.
#-------------------------------
[SKYUPPERCUT]
Name = Sky Uppercut
Type = FIGHTING
Category = Physical
Power = 85
Accuracy = 90
TotalPP = 15
Target = NearOther
FunctionCode = HitsTargetInSky
Flags = Contact,CanProtect,CanMirrorMove,Punching
Description = The user attacks the target with an uppercut thrown skyward with force.
#-------------------------------
[AURASPHERE]
Name = Aura Sphere
Type = FIGHTING
Category = Special
Power = 80
Accuracy = 0
TotalPP = 20
Target = Other
FunctionCode = None
Flags = CanProtect,CanMirrorMove,Pulse,Bomb
Description = The user looses a blast of aura power from deep within its body. This move is certain to hit.
#-------------------------------
[BODYPRESS]
Name = Body Press
Type = FIGHTING
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = UseUserDefenseInsteadOfUserAttack
Flags = Contact,CanProtect,CanMirrorMove,CannotMetronome
Description = The user attacks by slamming its body into the target. Power increases the higher the user's Defense is.
#-------------------------------
[SUBMISSION]
Name = Submission
Type = FIGHTING
Category = Physical
Power = 80
Accuracy = 80
TotalPP = 20
Target = NearOther
FunctionCode = RecoilQuarterOfDamageDealt
Flags = Contact,CanProtect,CanMirrorMove
Description = The user grabs the target and recklessly dives for the ground. It also hurts the user slightly.
#-------------------------------
[BRICKBREAK]
Name = Brick Break
Type = FIGHTING
Category = Physical
Power = 75
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = RemoveScreens
Flags = Contact,CanProtect,CanMirrorMove
Description = The user attacks with a swift chop. It can also break any barrier such as Light Screen and Reflect.
#-------------------------------
[DRAINPUNCH]
Name = Drain Punch
Type = FIGHTING
Category = Physical
Power = 75
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = HealUserByHalfOfDamageDone
Flags = Contact,CanProtect,CanMirrorMove,Punching
Description = An energy-draining punch. The user's HP is restored by half the damage taken by the target.
#-------------------------------
[VITALTHROW]
Name = Vital Throw
Type = FIGHTING
Category = Physical
Power = 70
Accuracy = 0
TotalPP = 10
Target = NearOther
Priority = -1
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The user attacks last. In return, this throw move is guaranteed not to miss.
#-------------------------------
[WAKEUPSLAP]
Name = Wake-Up Slap
Type = FIGHTING
Category = Physical
Power = 70
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = DoublePowerIfTargetAsleepCureTarget
Flags = Contact,CanProtect,CanMirrorMove
Description = This attack inflicts big damage on a sleeping target. It also wakes the target up, however.
#-------------------------------
[LOWSWEEP]
Name = Low Sweep
Type = FIGHTING
Category = Physical
Power = 65
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = LowerTargetSpeed1
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 100
Description = The user attacks the target's legs swiftly, reducing the target's Speed stat.
#-------------------------------
[CIRCLETHROW]
Name = Circle Throw
Type = FIGHTING
Category = Physical
Power = 60
Accuracy = 90
TotalPP = 10
Target = NearOther
Priority = -6
FunctionCode = SwitchOutTargetDamagingMove
Flags = Contact,CanProtect,CanMirrorMove
Description = The user throws the target and drags out another Pokémon in its party. In the wild, the battle ends.
#-------------------------------
[FORCEPALM]
Name = Force Palm
Type = FIGHTING
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = ParalyzeTarget
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 30
Description = The target is attacked with a shock wave. It may also leave the target with paralysis.
#-------------------------------
[REVENGE]
Name = Revenge
Type = FIGHTING
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 10
Target = NearOther
Priority = -4
FunctionCode = DoublePowerIfUserLostHPThisTurn
Flags = Contact,CanProtect,CanMirrorMove
Description = An attack move that inflicts double the damage if the user has been hurt by the foe in the same turn.
#-------------------------------
[ROLLINGKICK]
Name = Rolling Kick
Type = FIGHTING
Category = Physical
Power = 60
Accuracy = 85
TotalPP = 15
Target = NearOther
FunctionCode = FlinchTarget
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 30
Description = The user lashes out with a quick, spinning kick. It may also make the target flinch.
#-------------------------------
[STORMTHROW]
Name = Storm Throw
Type = FIGHTING
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = AlwaysCriticalHit
Flags = Contact,CanProtect,CanMirrorMove
Description = The user strikes the target with a fierce blow. This attack always results in a critical hit.
#-------------------------------
[KARATECHOP]
Name = Karate Chop
Type = FIGHTING
Category = Physical
Power = 50
Accuracy = 100
TotalPP = 25
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove,HighCriticalHitRate
Description = The target is attacked with a sharp chop. Critical hits land more easily.
#-------------------------------
[MACHPUNCH]
Name = Mach Punch
Type = FIGHTING
Category = Physical
Power = 40
Accuracy = 100
TotalPP = 30
Target = NearOther
Priority = 1
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove,Punching
Description = The user throws a punch at blinding speed. It is certain to strike first.
#-------------------------------
[POWERUPPUNCH]
Name = Power-Up Punch
Type = FIGHTING
Category = Physical
Power = 40
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = RaiseUserAttack1
Flags = Contact,CanProtect,CanMirrorMove,Punching
EffectChance = 100
Description = Striking opponents repeatedly makes the user's fists harder, raising the user's Attack stat.
#-------------------------------
[ROCKSMASH]
Name = Rock Smash
Type = FIGHTING
Category = Physical
Power = 40
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = LowerTargetDefense1
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 50
Description = The user attacks with a punch that can shatter a rock. It may also lower the foe's Defense stat.
#-------------------------------
[VACUUMWAVE]
Name = Vacuum Wave
Type = FIGHTING
Category = Special
Power = 40
Accuracy = 100
TotalPP = 30
Target = NearOther
Priority = 1
FunctionCode = None
Flags = CanProtect,CanMirrorMove
Description = The user whirls its fists to send a wave of pure vacuum at the target. This move always goes first.
#-------------------------------
[DOUBLEKICK]
Name = Double Kick
Type = FIGHTING
Category = Physical
Power = 30
Accuracy = 100
TotalPP = 30
Target = NearOther
FunctionCode = HitTwoTimes
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is quickly kicked twice in succession using both feet.
#-------------------------------
[ARMTHRUST]
Name = Arm Thrust
Type = FIGHTING
Category = Physical
Power = 15
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = HitTwoToFiveTimes
Flags = Contact,CanProtect,CanMirrorMove
Description = The user looses a flurry of open-palmed arm thrusts that hit two to five times in a row.
#-------------------------------
[TRIPLEKICK]
Name = Triple Kick
Type = FIGHTING
Category = Physical
Power = 10
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = HitThreeTimesPowersUpWithEachHit
Flags = Contact,CanProtect,CanMirrorMove
Description = A consecutive three-kick attack that becomes more powerful with each successive hit.
#-------------------------------
[COUNTER]
Name = Counter
Type = FIGHTING
Category = Physical
Power = 1
Accuracy = 100
TotalPP = 20
Target = None
Priority = -5
FunctionCode = CounterPhysicalDamage
Flags = Contact,CanProtect
Description = A retaliation move that counters any physical attack, inflicting double the damage taken.
#-------------------------------
[FINALGAMBIT]
Name = Final Gambit
Type = FIGHTING
Category = Special
Power = 1
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = UserFaintsFixedDamageUserHP
Flags = CanProtect
Description = The user risks all to attack the foe. The user faints but does damage equal to its HP.
#-------------------------------
[LOWKICK]
Name = Low Kick
Type = FIGHTING
Category = Physical
Power = 1
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = PowerHigherWithTargetWeight
Flags = Contact,CanProtect,CanMirrorMove
Description = A powerful low kick that makes the foe fall over. It inflicts greater damage on heavier foes.
#-------------------------------
[REVERSAL]
Name = Reversal
Type = FIGHTING
Category = Physical
Power = 1
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = PowerLowerWithUserHP
Flags = Contact,CanProtect,CanMirrorMove
Description = An all-out attack that becomes more powerful the less HP the user has.
#-------------------------------
[SEISMICTOSS]
Name = Seismic Toss
Type = FIGHTING
Category = Physical
Power = 1
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = FixedDamageUserLevel
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is thrown using the power of gravity. It inflicts damage equal to the user's level.
#-------------------------------
[BULKUP]
Name = Bulk Up
Type = FIGHTING
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = RaiseUserAtkDef1
Description = The user tenses its muscles to bulk up its body, boosting both its Attack and Defense stats.
#-------------------------------
[COACHING]
Name = Coaching
Type = FIGHTING
Category = Status
Accuracy = 0
TotalPP = 10
Target = AllAllies
FunctionCode = RaiseAlliesAtkDef1
Flags = CanMirrorMove
Description = The user properly coaches its ally Pokémon, boosting their Attack and Defense stats.
#-------------------------------
[DETECT]
Name = Detect
Type = FIGHTING
Category = Status
Accuracy = 0
TotalPP = 5
Target = User
Priority = 4
FunctionCode = ProtectUser
Description = It enables the user to evade all attacks. Its chance of failing rises if it is used in succession.
#-------------------------------
[MATBLOCK]
Name = Mat Block
Type = FIGHTING
Category = Status
Accuracy = 0
TotalPP = 10
Target = UserSide
FunctionCode = ProtectUserSideFromDamagingMovesIfUserFirstTurn
Description = Using a pulled-up mat as a shield, the user protects itself and its allies from damaging moves.
#-------------------------------
[NORETREAT]
Name = No Retreat
Type = FIGHTING
Category = Status
Accuracy = 0
TotalPP = 5
Target = User
FunctionCode = RaiseUserMainStats1TrapUserInBattle
Description = This move raises all the user's stats but prevents the user from switching out or fleeing.
#-------------------------------
[OCTOLOCK]
Name = Octolock
Type = FIGHTING
Category = Status
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = TrapTargetInBattleLowerTargetDefSpDef1EachTurn
Flags = CanProtect,CanMirrorMove
Description = Locks the target in and prevents it from fleeing. Also lowers the target's Defense and Sp. Def each turn.
#-------------------------------
[QUICKGUARD]
Name = Quick Guard
Type = FIGHTING
Category = Status
Accuracy = 0
TotalPP = 15
Target = UserSide
Priority = 3
FunctionCode = ProtectUserSideFromPriorityMoves
Description = The user protects itself and its allies from priority moves. If may fail if used in succession.
#-------------------------------
[VCREATE]
Name = V-create
Type = FIRE
Category = Physical
Power = 180
Accuracy = 95
TotalPP = 5
Target = NearOther
FunctionCode = LowerUserDefSpDefSpd1
Flags = Contact,CanProtect,CanMirrorMove,CannotMetronome
Description = With a fiery forehead, the user hurls itself at the foe. It lowers the user's Defense, Sp. Def, and Speed.
#-------------------------------
[BLASTBURN]
Name = Blast Burn
Type = FIRE
Category = Special
Power = 150
Accuracy = 90
TotalPP = 5
Target = NearOther
FunctionCode = AttackAndSkipNextTurn
Flags = CanProtect,CanMirrorMove
Description = The target is razed by a fiery explosion. The user must rest on the next turn, however.
#-------------------------------
[ERUPTION]
Name = Eruption
Type = FIRE
Category = Special
Power = 150
Accuracy = 100
TotalPP = 5
Target = AllNearFoes
FunctionCode = PowerHigherWithUserHP
Flags = CanProtect,CanMirrorMove
Description = The user attacks in an explosive fury. The lower the user's HP, the less powerful this attack becomes.
#-------------------------------
[MINDBLOWN]
Name = Mind Blown
Type = FIRE
Category = Special
Power = 150
Accuracy = 100
TotalPP = 5
Target = AllNearOthers
FunctionCode = UserLosesHalfOfTotalHPExplosive
Flags = CanProtect,CanMirrorMove,CannotMetronome
Description = The user attacks everything by causing its own head to explode. This also damages the user.
#-------------------------------
[SHELLTRAP]
Name = Shell Trap
Type = FIRE
Category = Special
Power = 150
Accuracy = 100
TotalPP = 5
Target = AllNearFoes
Priority = -3
FunctionCode = UsedAfterUserTakesPhysicalDamage
Flags = CanProtect
Description = The user sets a shell trap. If it is hit by a physical move, the trap explodes and hurt the attacker.
#-------------------------------
[BLUEFLARE]
Name = Blue Flare
Type = FIRE
Category = Special
Power = 130
Accuracy = 85
TotalPP = 5
Target = NearOther
FunctionCode = BurnTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 20
Description = The user attacks by engulfing the foe in a beautiful, yet intense, blue flame. It may also burn the foe.
#-------------------------------
[BURNUP]
Name = Burn Up
Type = FIRE
Category = Special
Power = 130
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = UserLosesFireType
Flags = CanProtect,CanMirrorMove,ThawsUser
Description = To inflict massive damage, the user burns itself out. The user will no longer be Fire type.
#-------------------------------
[OVERHEAT]
Name = Overheat
Type = FIRE
Category = Special
Power = 130
Accuracy = 90
TotalPP = 5
Target = NearOther
FunctionCode = LowerUserSpAtk2
Flags = CanProtect,CanMirrorMove
Description = The user attacks the target at full power. The attack's recoil sharply reduces the user's Sp. Atk stat.
#-------------------------------
[FLAREBLITZ]
Name = Flare Blitz
Type = FIRE
Category = Physical
Power = 120
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = RecoilThirdOfDamageDealtBurnTarget
Flags = Contact,CanProtect,CanMirrorMove,ThawsUser
EffectChance = 10
Description = The user cloaks itself in fire and charges at the foe. The user also takes damage and may burn the target.
#-------------------------------
[PYROBALL]
Name = Pyro Ball
Type = FIRE
Category = Physical
Power = 120
Accuracy = 90
TotalPP = 5
Target = NearOther
FunctionCode = BurnTarget
Flags = CanProtect,CanMirrorMove,ThawsUser,Bomb,CannotMetronome
EffectChance = 10
Description = Attacks by igniting a small stone and launching it as a fiery ball. May also burn the target.
#-------------------------------
[FIREBLAST]
Name = Fire Blast
Type = FIRE
Category = Special
Power = 110
Accuracy = 85
TotalPP = 5
Target = NearOther
FunctionCode = BurnTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The foe is attacked with an intense blast of all-consuming fire. It may also leave the target with a burn.
#-------------------------------
[FUSIONFLARE]
Name = Fusion Flare
Type = FIRE
Category = Special
Power = 100
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = DoublePowerAfterFusionBolt
Flags = CanProtect,CanMirrorMove,ThawsUser
Description = The user brings down a giant flame. It does more damage if influenced by an enormous thunderbolt.
#-------------------------------
[INFERNO]
Name = Inferno
Type = FIRE
Category = Special
Power = 100
Accuracy = 50
TotalPP = 5
Target = NearOther
FunctionCode = BurnTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 100
Description = The user attacks by engulfing the target in an intense fire. It leaves the target with a burn.
#-------------------------------
[MAGMASTORM]
Name = Magma Storm
Type = FIRE
Category = Special
Power = 100
Accuracy = 75
TotalPP = 5
Target = NearOther
FunctionCode = BindTarget
Flags = CanProtect,CanMirrorMove
Description = The target becomes trapped within a maelstrom of fire that rages for four to five turns.
#-------------------------------
[SACREDFIRE]
Name = Sacred Fire
Type = FIRE
Category = Physical
Power = 100
Accuracy = 95
TotalPP = 5
Target = NearOther
FunctionCode = BurnTarget
Flags = CanProtect,CanMirrorMove,ThawsUser
EffectChance = 50
Description = The target is razed with a mystical fire of great intensity. It may also leave the target with a burn.
#-------------------------------
[SEARINGSHOT]
Name = Searing Shot
Type = FIRE
Category = Special
Power = 100
Accuracy = 100
TotalPP = 5
Target = AllNearOthers
FunctionCode = BurnTarget
Flags = CanProtect,CanMirrorMove,Bomb
EffectChance = 30
Description = An inferno of scarlet flames torches everything around the user. It may leave the foe with a burn.
#-------------------------------
[HEATWAVE]
Name = Heat Wave
Type = FIRE
Category = Special
Power = 95
Accuracy = 90
TotalPP = 10
Target = AllNearFoes
FunctionCode = BurnTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The user attacks by exhaling hot breath on the opposing team. It may also leave targets with a burn.
#-------------------------------
[FLAMETHROWER]
Name = Flamethrower
Type = FIRE
Category = Special
Power = 90
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = BurnTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The target is scorched with an intense blast of fire. It may also leave the target with a burn.
#-------------------------------
[BLAZEKICK]
Name = Blaze Kick
Type = FIRE
Category = Physical
Power = 85
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = BurnTarget
Flags = Contact,CanProtect,CanMirrorMove,HighCriticalHitRate
EffectChance = 10
Description = The user launches a kick with a high critical-hit ratio. It may also leave the target with a burn.
#-------------------------------
[FIERYDANCE]
Name = Fiery Dance
Type = FIRE
Category = Special
Power = 80
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = RaiseUserSpAtk1
Flags = CanProtect,CanMirrorMove,Dance
EffectChance = 50
Description = Cloaked in flames, the user dances and flaps its wings. It may also raise the user's Sp. Atk stat.
#-------------------------------
[FIRELASH]
Name = Fire Lash
Type = FIRE
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = LowerTargetDefense1
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 100
Description = The user strikes the target with a burning lash. This also lowers the target's Defense stat.
#-------------------------------
[FIREPLEDGE]
Name = Fire Pledge
Type = FIRE
Category = Special
Power = 80
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = FirePledge
Flags = CanProtect,CanMirrorMove
Description = A column of fire hits opposing Pokémon. When used with its Grass equivalent, it makes a sea of fire.
#-------------------------------
[LAVAPLUME]
Name = Lava Plume
Type = FIRE
Category = Special
Power = 80
Accuracy = 100
TotalPP = 15
Target = AllNearOthers
FunctionCode = BurnTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 30
Description = An inferno of scarlet flames torches everything around the user. It may leave targets with a burn.
#-------------------------------
[FIREPUNCH]
Name = Fire Punch
Type = FIRE
Category = Physical
Power = 75
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = BurnTarget
Flags = Contact,CanProtect,CanMirrorMove,Punching
EffectChance = 10
Description = The target is punched with a fiery fist. It may leave the target with a burn.
#-------------------------------
[MYSTICALFIRE]
Name = Mystical Fire
Type = FIRE
Category = Special
Power = 75
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetSpAtk1
Flags = CanProtect,CanMirrorMove
EffectChance = 100
Description = The user attacks by breathing a special, hot fire. This also lowers the target's Sp. Atk stat.
#-------------------------------
[BURNINGJEALOUSY]
Name = Burning Jealousy
Type = FIRE
Category = Special
Power = 70
Accuracy = 100
TotalPP = 5
Target = AllNearFoes
FunctionCode = BurnTargetIfTargetStatsRaisedThisTurn
Flags = CanProtect,CanMirrorMove
EffectChance = 100
Description = The user attacks with energy from jealousy. This burns all foes that had their stats boosted this turn.
#-------------------------------
[FLAMEBURST]
Name = Flame Burst
Type = FIRE
Category = Special
Power = 70
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = DamageTargetAlly
Flags = CanProtect,CanMirrorMove
Description = The user attacks the foe with a bursting flame. It also damages Pokémon next to the target.
#-------------------------------
[FIREFANG]
Name = Fire Fang
Type = FIRE
Category = Physical
Power = 65
Accuracy = 95
TotalPP = 15
Target = NearOther
FunctionCode = BurnFlinchTarget
Flags = Contact,CanProtect,CanMirrorMove,Biting
EffectChance = 101
Description = The user bites with flame-cloaked fangs. It may also make the target flinch or leave it burned.
#-------------------------------
[FLAMEWHEEL]
Name = Flame Wheel
Type = FIRE
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 25
Target = NearOther
FunctionCode = BurnTarget
Flags = Contact,CanProtect,CanMirrorMove,ThawsUser
EffectChance = 10
Description = The user cloaks itself in fire and charges at the target. It may also leave the target with a burn.
#-------------------------------
[INCINERATE]
Name = Incinerate
Type = FIRE
Category = Special
Power = 60
Accuracy = 100
TotalPP = 15
Target = AllNearFoes
FunctionCode = DestroyTargetBerryOrGem
Flags = CanProtect,CanMirrorMove
Description = The user attacks the foe with fire. The target's held Berry becomes burnt up and unusable.
#-------------------------------
[FLAMECHARGE]
Name = Flame Charge
Type = FIRE
Category = Physical
Power = 50
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = RaiseUserSpeed1
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 100
Description = The user cloaks itself in flame and attacks. Building up more power, it raises the user's Speed stat.
#-------------------------------
[EMBER]
Name = Ember
Type = FIRE
Category = Special
Power = 40
Accuracy = 100
TotalPP = 25
Target = NearOther
FunctionCode = BurnTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The target is attacked with small flames. It may also leave the target with a burn.
#-------------------------------
[FIRESPIN]
Name = Fire Spin
Type = FIRE
Category = Special
Power = 35
Accuracy = 85
TotalPP = 15
Target = NearOther
FunctionCode = BindTarget
Flags = CanProtect,CanMirrorMove
Description = The target becomes trapped within a fierce vortex of fire that rages for four to five turns.
#-------------------------------
[HEATCRASH]
Name = Heat Crash
Type = FIRE
Category = Physical
Power = 1
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = PowerHigherWithUserHeavierThanTarget
Flags = Contact,CanProtect,CanMirrorMove,TramplesMinimize
Description = The user slams the foe with its flaming body. The heavier the user is, the greater the damage.
#-------------------------------
[SUNNYDAY]
Name = Sunny Day
Type = FIRE
Category = Status
Accuracy = 0
TotalPP = 5
Target = BothSides
FunctionCode = StartSunWeather
Description = The user intensifies the sun for five turns, powering up Fire-type moves.
#-------------------------------
[WILLOWISP]
Name = Will-O-Wisp
Type = FIRE
Category = Status
Accuracy = 85
TotalPP = 15
Target = NearOther
FunctionCode = BurnTarget
Flags = CanProtect,CanMirrorMove
Description = The user shoots a sinister, bluish-white flame at the target to inflict a burn.
#-------------------------------
[SKYATTACK]
Name = Sky Attack
Type = FLYING
Category = Physical
Power = 140
Accuracy = 90
TotalPP = 5
Target = Other
FunctionCode = TwoTurnAttackFlinchTarget
Flags = CanProtect,CanMirrorMove,HighCriticalHitRate
EffectChance = 30
Description = A second-turn attack move where critical hits land more easily. It may also make the target flinch.
#-------------------------------
[BRAVEBIRD]
Name = Brave Bird
Type = FLYING
Category = Physical
Power = 120
Accuracy = 100
TotalPP = 15
Target = Other
FunctionCode = RecoilThirdOfDamageDealt
Flags = Contact,CanProtect,CanMirrorMove
Description = The user tucks in its wings and charges from a low altitude. The user also takes serious damage.
#-------------------------------
[DRAGONASCENT]
Name = Dragon Ascent
Type = FLYING
Category = Physical
Power = 120
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = LowerUserDefSpDef1
Flags = Contact,CanProtect,CanMirrorMove,CannotMetronome
Description = The user soars upward and drops at high speeds. Its Defense and Sp. Def stats are lowered.
#-------------------------------
[HURRICANE]
Name = Hurricane
Type = FLYING
Category = Special
Power = 110
Accuracy = 70
TotalPP = 10
Target = Other
FunctionCode = ConfuseTargetAlwaysHitsInRainHitsTargetInSky
Flags = CanProtect,CanMirrorMove
EffectChance = 30
Description = The user wraps its foe in a fierce wind that flies up into the sky. It may also confuse the foe.
#-------------------------------
[AEROBLAST]
Name = Aeroblast
Type = FLYING
Category = Special
Power = 100
Accuracy = 95
TotalPP = 5
Target = Other
FunctionCode = None
Flags = CanProtect,CanMirrorMove,HighCriticalHitRate
Description = A vortex of air is shot at the target to inflict damage. Critical hits land more easily.
#-------------------------------
[BEAKBLAST]
Name = Beak Blast
Type = FLYING
Category = Physical
Power = 100
Accuracy = 100
TotalPP = 15
Target = NearOther
Priority = -3
FunctionCode = BurnAttackerBeforeUserActs
Flags = CanProtect,Bomb
Description = The user heats up its beak before attacking. Making contact in this time results in a burn.
#-------------------------------
[FLY]
Name = Fly
Type = FLYING
Category = Physical
Power = 90
Accuracy = 95
TotalPP = 15
Target = Other
FunctionCode = TwoTurnAttackInvulnerableInSky
Flags = Contact,CanProtect,CanMirrorMove
Description = The user soars, then strikes on the second turn. It can also be used for flying to any familiar town.
#-------------------------------
[BOUNCE]
Name = Bounce
Type = FLYING
Category = Physical
Power = 85
Accuracy = 85
TotalPP = 5
Target = Other
FunctionCode = TwoTurnAttackInvulnerableInSkyParalyzeTarget
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 30
Description = The user bounces up high, then drops on the foe on the second turn. It may also paralyze the foe.
#-------------------------------
[DRILLPECK]
Name = Drill Peck
Type = FLYING
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 20
Target = Other
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = A corkscrewing attack with the sharp beak acting as a drill.
#-------------------------------
[OBLIVIONWING]
Name = Oblivion Wing
Type = FLYING
Category = Special
Power = 80
Accuracy = 100
TotalPP = 10
Target = Other
FunctionCode = HealUserByThreeQuartersOfDamageDone
Flags = CanProtect,CanMirrorMove
Description = The user absorbs its target's HP. The user's HP is restored by over half of the damage dealt.
#-------------------------------
[AIRSLASH]
Name = Air Slash
Type = FLYING
Category = Special
Power = 75
Accuracy = 95
TotalPP = 15
Target = Other
FunctionCode = FlinchTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 30
Description = The user attacks with a blade of air that slices even the sky. It may also make the target flinch.
#-------------------------------
[CHATTER]
Name = Chatter
Type = FLYING
Category = Special
Power = 65
Accuracy = 100
TotalPP = 20
Target = Other
FunctionCode = ConfuseTarget
Flags = CanProtect,CanMirrorMove,Sound
EffectChance = 100
Description = The user attacks using a sound wave based on words it has learned. It may also confuse the target.
#-------------------------------
[AERIALACE]
Name = Aerial Ace
Type = FLYING
Category = Physical
Power = 60
Accuracy = 0
TotalPP = 20
Target = Other
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The user confounds the foe with speed, then slashes. The attack lands without fail.
#-------------------------------
[AIRCUTTER]
Name = Air Cutter
Type = FLYING
Category = Special
Power = 60
Accuracy = 95
TotalPP = 25
Target = AllNearFoes
FunctionCode = None
Flags = CanProtect,CanMirrorMove,HighCriticalHitRate
Description = The user launches razor-like wind to slash the opposing team. Critical hits land more easily.
#-------------------------------
[PLUCK]
Name = Pluck
Type = FLYING
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 20
Target = Other
FunctionCode = UserConsumeTargetBerry
Flags = Contact,CanProtect,CanMirrorMove
Description = The user pecks the target. If the target is holding a Berry, the user eats it and gains its effect.
#-------------------------------
[SKYDROP]
Name = Sky Drop
Type = FLYING
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 10
Target = Other
FunctionCode = TwoTurnAttackInvulnerableInSkyTargetCannotAct
Flags = Contact,CanProtect,CanMirrorMove
Description = The user takes the foe into the sky, then drops it on the next turn. The foe cannot attack while airborne.
#-------------------------------
[WINGATTACK]
Name = Wing Attack
Type = FLYING
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 35
Target = Other
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is struck with large, imposing wings spread wide to inflict damage.
#-------------------------------
[ACROBATICS]
Name = Acrobatics
Type = FLYING
Category = Physical
Power = 55
Accuracy = 100
TotalPP = 15
Target = Other
FunctionCode = DoublePowerIfUserHasNoItem
Flags = Contact,CanProtect,CanMirrorMove
Description = The user nimbly strikes the foe. This attack does more damage if the user is not holding an item.
#-------------------------------
[DUALWINGBEAT]
Name = Dual Wingbeat
Type = FLYING
Category = Physical
Power = 40
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = HitTwoTimes
Flags = Contact,CanProtect,CanMirrorMove
Description = The user slams the target with its wings. The target is hit twice in a row.
#-------------------------------
[GUST]
Name = Gust
Type = FLYING
Category = Special
Power = 40
Accuracy = 100
TotalPP = 35
Target = Other
FunctionCode = DoublePowerIfTargetInSky
Flags = CanProtect,CanMirrorMove
Description = A gust of wind is whipped up by wings and launched at the target to inflict damage.
#-------------------------------
[PECK]
Name = Peck
Type = FLYING
Category = Physical
Power = 35
Accuracy = 100
TotalPP = 35
Target = Other
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is jabbed with a sharply pointed beak or horn.
#-------------------------------
[DEFOG]
Name = Defog
Type = FLYING
Category = Status
Accuracy = 0
TotalPP = 15
Target = NearOther
FunctionCode = LowerTargetEvasion1RemoveSideEffects
Flags = CanProtect,CanMirrorMove
Description = A strong wind blows away the foe's obstacles such as Light Screen. It also lowers their evasion.
#-------------------------------
[FEATHERDANCE]
Name = Feather Dance
Type = FLYING
Category = Status
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = LowerTargetAttack2
Flags = CanProtect,CanMirrorMove,Dance
Description = The user covers the target's body with a mass of down that harshly lowers its Attack stat.
#-------------------------------
[MIRRORMOVE]
Name = Mirror Move
Type = FLYING
Category = Status
Accuracy = 0
TotalPP = 20
Target = NearOther
FunctionCode = UseLastMoveUsedByTarget
Description = The user counters the target by mimicking the target's last move.
#-------------------------------
[ROOST]
Name = Roost
Type = FLYING
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = HealUserHalfOfTotalHPLoseFlyingTypeThisTurn
Description = The user lands and rests its body. It restores the user's HP by up to half of its max HP.
#-------------------------------
[TAILWIND]
Name = Tailwind
Type = FLYING
Category = Status
Accuracy = 0
TotalPP = 15
Target = UserSide
FunctionCode = StartUserSideDoubleSpeed
Description = The user whips up a turbulent whirlwind that ups the Speed of all party Pokémon for four turns.
#-------------------------------
[ASTRALBARRAGE]
Name = Astral Barrage
Type = GHOST
Category = Special
Power = 120
Accuracy = 100
TotalPP = 5
Target = AllNearFoes
FunctionCode = None
Flags = CanProtect,CanMirrorMove,CannotMetronome
Description = The user attacks by sending a frightful amount of small ghosts at opposing Pokémon.
#-------------------------------
[SHADOWFORCE]
Name = Shadow Force
Type = GHOST
Category = Physical
Power = 120
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = TwoTurnAttackInvulnerableRemoveProtections
Flags = Contact,CanMirrorMove
Description = The user disappears, then strikes the foe on the second turn. It hits even if the foe protects itself.
#-------------------------------
[POLTERGEIST]
Name = Poltergeist
Type = GHOST
Category = Physical
Power = 110
Accuracy = 90
TotalPP = 5
Target = NearOther
FunctionCode = FailsIfTargetHasNoItem
Flags = CanProtect,CanMirrorMove
Description = Attacks the target by controlling its item. The move fails if the target doesn't have an item.
#-------------------------------
[MOONGEISTBEAM]
Name = Moongeist Beam
Type = GHOST
Category = Special
Power = 100
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = IgnoreTargetAbility
Flags = CanProtect,CanMirrorMove,CannotMetronome
Description = The user emits a sinister ray. This move can be used on the target regardless of its Abilities.
#-------------------------------
[PHANTOMFORCE]
Name = Phantom Force
Type = GHOST
Category = Physical
Power = 90
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = TwoTurnAttackInvulnerableRemoveProtections
Flags = Contact,CanMirrorMove
Description = The user vanishes somewhere, then strikes on the next turn. Hits through protections.
#-------------------------------
[SPECTRALTHIEF]
Name = Spectral Thief
Type = GHOST
Category = Physical
Power = 90
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = UserStealTargetPositiveStatStages
Flags = Contact,CanProtect,CanMirrorMove,CannotMetronome
Description = The user hides in the target's shadow, steals the target's stat boosts, and then attacks.
#-------------------------------
[SHADOWBONE]
Name = Shadow Bone
Type = GHOST
Category = Physical
Power = 85
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetDefense1
Flags = CanProtect,CanMirrorMove
EffectChance = 20
Description = The user beats the target with a bone containing a spirit. May lower the target's Defense stat.
#-------------------------------
[SHADOWBALL]
Name = Shadow Ball
Type = GHOST
Category = Special
Power = 80
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = LowerTargetSpDef1
Flags = CanProtect,CanMirrorMove,Bomb
EffectChance = 20
Description = The user hurls a shadowy blob at the target. It may also lower the target's Sp. Def stat.
#-------------------------------
[SPIRITSHACKLE]
Name = Spirit Shackle
Type = GHOST
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = TrapTargetInBattle
Flags = CanProtect,CanMirrorMove
EffectChance = 100
Description = The user attacks while also stitching the target's shadow to the ground to prevent it fleeing.
#-------------------------------
[SHADOWCLAW]
Name = Shadow Claw
Type = GHOST
Category = Physical
Power = 70
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove,HighCriticalHitRate
Description = The user slashes with a sharp claw made from shadows. Critical hits land more easily.
#-------------------------------
[HEX]
Name = Hex
Type = GHOST
Category = Special
Power = 65
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = DoublePowerIfTargetStatusProblem
Flags = CanProtect,CanMirrorMove
Description = This relentless attack does massive damage to a target affected by status problems.
#-------------------------------
[OMINOUSWIND]
Name = Ominous Wind
Type = GHOST
Category = Special
Power = 60
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = RaiseUserMainStats1
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The user blasts the target with a gust of repulsive wind. It may also raise all the user's stats at once.
#-------------------------------
[SHADOWPUNCH]
Name = Shadow Punch
Type = GHOST
Category = Physical
Power = 60
Accuracy = 0
TotalPP = 20
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove,Punching
Description = The user throws a punch from the shadows. The punch lands without fail.
#-------------------------------
[SHADOWSNEAK]
Name = Shadow Sneak
Type = GHOST
Category = Physical
Power = 40
Accuracy = 100
TotalPP = 30
Target = NearOther
Priority = 1
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The user extends its shadow and attacks the target from behind. This move always goes first.
#-------------------------------
[ASTONISH]
Name = Astonish
Type = GHOST
Category = Physical
Power = 30
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = FlinchTarget
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 30
Description = The user attacks the target while shouting in a startling fashion. It may also make the target flinch.
#-------------------------------
[LICK]
Name = Lick
Type = GHOST
Category = Physical
Power = 30
Accuracy = 100
TotalPP = 30
Target = NearOther
FunctionCode = ParalyzeTarget
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 30
Description = The target is licked with a long tongue, causing damage. It may also leave the target with paralysis.
#-------------------------------
[NIGHTSHADE]
Name = Night Shade
Type = GHOST
Category = Special
Power = 1
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = FixedDamageUserLevel
Flags = CanProtect,CanMirrorMove
Description = The user makes the foe see a frightening mirage. It inflicts damage matching the user's level.
#-------------------------------
[CONFUSERAY]
Name = Confuse Ray
Type = GHOST
Category = Status
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = ConfuseTarget
Flags = CanProtect,CanMirrorMove
Description = The target is exposed to a sinister ray that triggers confusion.
#-------------------------------
[CURSE]
Name = Curse
Type = GHOST
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = CurseTargetOrLowerUserSpd1RaiseUserAtkDef1
Description = A move that works differently for the Ghost type than for all the other types.
#-------------------------------
[DESTINYBOND]
Name = Destiny Bond
Type = GHOST
Category = Status
Accuracy = 0
TotalPP = 5
Target = User
FunctionCode = AttackerFaintsIfUserFaints
Description = When this move is used, if the user faints, the foe that landed the knockout hit also faints.
#-------------------------------
[GRUDGE]
Name = Grudge
Type = GHOST
Category = Status
Accuracy = 0
TotalPP = 5
Target = User
FunctionCode = SetAttackerMovePPTo0IfUserFaints
Description = If the user faints, the user's grudge fully depletes the PP of the foe's move that knocked it out.
#-------------------------------
[NIGHTMARE]
Name = Nightmare
Type = GHOST
Category = Status
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = StartDamageTargetEachTurnIfTargetAsleep
Flags = CanProtect,CanMirrorMove
Description = A sleeping target sees a nightmare that inflicts some damage every turn.
#-------------------------------
[SPITE]
Name = Spite
Type = GHOST
Category = Status
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = LowerPPOfTargetLastMoveBy4
Flags = CanProtect,CanMirrorMove
Description = The user unleashes its grudge on the move last used by the target by cutting 4 PP from it.
#-------------------------------
[TRICKORTREAT]
Name = Trick-or-Treat
Type = GHOST
Category = Status
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = AddGhostTypeToTarget
Flags = CanProtect,CanMirrorMove
Description = The user takes the target trick-or-treating. This adds Ghost type to the target's type.
#-------------------------------
[FRENZYPLANT]
Name = Frenzy Plant
Type = GRASS
Category = Special
Power = 150
Accuracy = 90
TotalPP = 5
Target = NearOther
FunctionCode = AttackAndSkipNextTurn
Flags = CanProtect,CanMirrorMove
Description = The user slams the target with an enormous tree. The user can't move on the next turn.
#-------------------------------
[LEAFSTORM]
Name = Leaf Storm
Type = GRASS
Category = Special
Power = 130
Accuracy = 90
TotalPP = 5
Target = NearOther
FunctionCode = LowerUserSpAtk2
Flags = CanProtect,CanMirrorMove
Description = A storm of sharp is whipped up. The attack's recoil harshly reduces the user's Sp. Atk stat.
#-------------------------------
[SOLARBLADE]
Name = Solar Blade
Type = GRASS
Category = Physical
Power = 125
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = TwoTurnAttackOneTurnInSun
Flags = Contact,CanProtect,CanMirrorMove
Description = The user gathers light energy into a blade, attacking the target on the next turn.
#-------------------------------
[PETALDANCE]
Name = Petal Dance
Type = GRASS
Category = Special
Power = 120
Accuracy = 100
TotalPP = 10
Target = RandomNearFoe
FunctionCode = MultiTurnAttackConfuseUserAtEnd
Flags = Contact,CanProtect,CanMirrorMove,Dance
Description = The user attacks by scattering petals for two to three turns. The user then becomes confused.
#-------------------------------
[POWERWHIP]
Name = Power Whip
Type = GRASS
Category = Physical
Power = 120
Accuracy = 85
TotalPP = 10
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The user violently whirls its vines or tentacles to harshly lash the target.
#-------------------------------
[SEEDFLARE]
Name = Seed Flare
Type = GRASS
Category = Special
Power = 120
Accuracy = 85
TotalPP = 5
Target = NearOther
FunctionCode = LowerTargetSpDef2
Flags = CanProtect,CanMirrorMove
EffectChance = 40
Description = The user generates a shock wave from within its body. It may harshly lower the target's Sp. Def.
#-------------------------------
[SOLARBEAM]
Name = Solar Beam
Type = GRASS
Category = Special
Power = 120
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = TwoTurnAttackOneTurnInSun
Flags = CanProtect,CanMirrorMove
Description = A two-turn attack. The user gathers light, then blasts a bundled beam on the second turn.
#-------------------------------
[WOODHAMMER]
Name = Wood Hammer
Type = GRASS
Category = Physical
Power = 120
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = RecoilThirdOfDamageDealt
Flags = Contact,CanProtect,CanMirrorMove
Description = The user slams its rugged body into the target to attack. The user also sustains serious damage.
#-------------------------------
[ENERGYBALL]
Name = Energy Ball
Type = GRASS
Category = Special
Power = 90
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetSpDef1
Flags = CanProtect,CanMirrorMove,Bomb
EffectChance = 10
Description = The user draws power from nature and fires it at the target. It may also lower the target's Sp. Def.
#-------------------------------
[LEAFBLADE]
Name = Leaf Blade
Type = GRASS
Category = Physical
Power = 90
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove,HighCriticalHitRate
Description = The user handles a sharp leaf like a sword and attacks by slashing. It has a high critical-hit ratio.
#-------------------------------
[PETALBLIZZARD]
Name = Petal Blizzard
Type = GRASS
Category = Physical
Power = 90
Accuracy = 100
TotalPP = 15
Target = AllNearOthers
FunctionCode = None
Flags = CanProtect,CanMirrorMove
Description = The user stirs up a violent petal blizzard and attacks everything around it.
#-------------------------------
[APPLEACID]
Name = Apple Acid
Type = GRASS
Category = Special
Power = 80
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetSpDef1
Flags = CanProtect,CanMirrorMove,CannotMetronome
EffectChance = 100
Description = Attacks with an acidic liquid created from tart apples. This also lowers the target's Sp. Def.
#-------------------------------
[DRUMBEATING]
Name = Drum Beating
Type = GRASS
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetSpeed1
Flags = CanProtect,CanMirrorMove,CannotMetronome
EffectChance = 100
Description = The user plays its drum, controlling roots to attack. This also lowers the target's Speed stat.
#-------------------------------
[GRASSPLEDGE]
Name = Grass Pledge
Type = GRASS
Category = Special
Power = 80
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = GrassPledge
Flags = CanProtect,CanMirrorMove
Description = A column of grass hits the foes. When used with its water equivalent, it creates a vast swamp.
#-------------------------------
[GRAVAPPLE]
Name = Grav Apple
Type = GRASS
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetDefense1PowersUpInGravity
Flags = CanProtect,CanMirrorMove,CannotMetronome
EffectChance = 100
Description = The user inflicts damage by dropping an apple from high above. This also lowers the target's Defense.
#-------------------------------
[SEEDBOMB]
Name = Seed Bomb
Type = GRASS
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = None
Flags = CanProtect,CanMirrorMove,Bomb
Description = The user slams a barrage of hard-shelled seeds down on the target from above.
#-------------------------------
[GIGADRAIN]
Name = Giga Drain
Type = GRASS
Category = Special
Power = 75
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = HealUserByHalfOfDamageDone
Flags = CanProtect,CanMirrorMove
Description = A nutrient-draining attack. The user's HP is restored by half the damage taken by the target.
#-------------------------------
[HORNLEECH]
Name = Horn Leech
Type = GRASS
Category = Physical
Power = 75
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = HealUserByHalfOfDamageDone
Flags = Contact,CanProtect,CanMirrorMove
Description = The user drains the foe's energy with its horns. The user's HP is restored by half the damage inflicted.
#-------------------------------
[GRASSYGLIDE]
Name = Grassy Glide
Type = GRASS
Category = Physical
Power = 70
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = HigherPriorityInGrassyTerrain
Flags = Contact,CanProtect,CanMirrorMove
Description = Gliding on the ground, the user attacks the target. This move always goes first on Grassy Terrain.
#-------------------------------
[TROPKICK]
Name = Trop Kick
Type = GRASS
Category = Physical
Power = 70
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = LowerTargetAttack1
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 100
Description = The user lands an intense tropical kick on the target. This also lowers the target's Attack stat.
#-------------------------------
[LEAFTORNADO]
Name = Leaf Tornado
Type = GRASS
Category = Special
Power = 65
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetAccuracy1
Flags = CanProtect,CanMirrorMove
EffectChance = 50
Description = The user attacks its foe by encircling it in sharp leaves. This attack may also lower the foe's accuracy.
#-------------------------------
[MAGICALLEAF]
Name = Magical Leaf
Type = GRASS
Category = Special
Power = 60
Accuracy = 0
TotalPP = 20
Target = NearOther
FunctionCode = None
Flags = CanProtect,CanMirrorMove
Description = The user scatters curious leaves that chase the target. This attack will not miss.
#-------------------------------
[NEEDLEARM]
Name = Needle Arm
Type = GRASS
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = FlinchTarget
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 30
Description = The user attacks by wildly swinging its thorny arms. It may also make the target flinch.
#-------------------------------
[RAZORLEAF]
Name = Razor Leaf
Type = GRASS
Category = Physical
Power = 55
Accuracy = 95
TotalPP = 25
Target = AllNearFoes
FunctionCode = None
Flags = CanProtect,CanMirrorMove,HighCriticalHitRate
Description = Sharp-edged leaves are launched to slash at the opposing team. Critical hits land more easily.
#-------------------------------
[VINEWHIP]
Name = Vine Whip
Type = GRASS
Category = Physical
Power = 45
Accuracy = 100
TotalPP = 25
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is struck with slender, whiplike vines to inflict damage.
#-------------------------------
[BRANCHPOKE]
Name = Branch Poke
Type = GRASS
Category = Physical
Power = 40
Accuracy = 100
TotalPP = 40
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove,CannotMetronome
Description = The user attacks the target by poking it with a sharply pointed branch.
#-------------------------------
[LEAFAGE]
Name = Leafage
Type = GRASS
Category = Physical
Power = 40
Accuracy = 100
TotalPP = 40
Target = NearOther
FunctionCode = None
Flags = CanProtect,CanMirrorMove
Description = The user attacks by pelting the target with leaves.
#-------------------------------
[MEGADRAIN]
Name = Mega Drain
Type = GRASS
Category = Special
Power = 40
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = HealUserByHalfOfDamageDone
Flags = CanProtect,CanMirrorMove
Description = A nutrient-draining attack. The user's HP is restored by half the damage taken by the target.
#-------------------------------
[SNAPTRAP]
Name = Snap Trap
Type = GRASS
Category = Physical
Power = 35
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = BindTarget
Flags = Contact,CanProtect,CanMirrorMove,CannotMetronome
Description = The user snares the target in a snap trap for four to five turns.
#-------------------------------
[BULLETSEED]
Name = Bullet Seed
Type = GRASS
Category = Physical
Power = 25
Accuracy = 100
TotalPP = 30
Target = NearOther
FunctionCode = HitTwoToFiveTimes
Flags = CanProtect,CanMirrorMove,Bomb
Description = The user forcefully shoots seeds at the target. Two to five seeds are shot in rapid succession.
#-------------------------------
[ABSORB]
Name = Absorb
Type = GRASS
Category = Special
Power = 20
Accuracy = 100
TotalPP = 25
Target = NearOther
FunctionCode = HealUserByHalfOfDamageDone
Flags = CanProtect,CanMirrorMove
Description = A nutrient-draining attack. The user's HP is restored by half the damage taken by the target.
#-------------------------------
[GRASSKNOT]
Name = Grass Knot
Type = GRASS
Category = Special
Power = 1
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = PowerHigherWithTargetWeight
Flags = Contact,CanProtect,CanMirrorMove
Description = The user snares the target with grass and trips it. The heavier the target, the greater the damage.
#-------------------------------
[AROMATHERAPY]
Name = Aromatherapy
Type = GRASS
Category = Status
Accuracy = 0
TotalPP = 5
Target = UserAndAllies
FunctionCode = CureUserPartyStatus
Description = The user releases a soothing scent that heals all status problems affecting the user's party.
#-------------------------------
[COTTONGUARD]
Name = Cotton Guard
Type = GRASS
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = RaiseUserDefense3
Description = The user protects itself by wrapping its body in soft cotton, drastically raising its Defense stat.
#-------------------------------
[COTTONSPORE]
Name = Cotton Spore
Type = GRASS
Category = Status
Accuracy = 100
TotalPP = 40
Target = AllNearFoes
FunctionCode = LowerTargetSpeed2
Flags = CanProtect,CanMirrorMove,Powder
Description = The user releases cotton-like spores that cling to the foe, harshly reducing its Speed stat.
#-------------------------------
[FORESTSCURSE]
Name = Forest's Curse
Type = GRASS
Category = Status
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = AddGrassTypeToTarget
Flags = CanProtect,CanMirrorMove
Description = The user puts a forest curse on the target. The target is now Grass type as well.
#-------------------------------
[GRASSWHISTLE]
Name = Grass Whistle
Type = GRASS
Category = Status
Accuracy = 55
TotalPP = 15
Target = NearOther
FunctionCode = SleepTarget
Flags = CanProtect,CanMirrorMove,Sound
Description = The user plays a pleasant melody that lulls the target into a deep sleep.
#-------------------------------
[GRASSYTERRAIN]
Name = Grassy Terrain
Type = GRASS
Category = Status
Accuracy = 0
TotalPP = 10
Target = BothSides
FunctionCode = StartGrassyTerrain
Description = The user turns the ground to grass for five turns. Grounded Pokémon restore a little HP every turn.
#-------------------------------
[INGRAIN]
Name = Ingrain
Type = GRASS
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = StartHealUserEachTurnTrapUserInBattle
Description = The user lays roots that restore its HP on every turn. Because it is rooted, it can't switch out.
#-------------------------------
[JUNGLEHEALING]
Name = Jungle Healing
Type = GRASS
Category = Status
Accuracy = 0
TotalPP = 10
Target = UserAndAllies
FunctionCode = HealUserAndAlliesQuarterOfTotalHPCureStatus
Flags = CannotMetronome
Description = The user becomes one with the jungle, and restores HP and cures status conditions of itself and allies.
#-------------------------------
[LEECHSEED]
Name = Leech Seed
Type = GRASS
Category = Status
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = StartLeechSeedTarget
Flags = CanProtect,CanMirrorMove
Description = A seed is planted on the target. It steals some HP from the target every turn.
#-------------------------------
[SLEEPPOWDER]
Name = Sleep Powder
Type = GRASS
Category = Status
Accuracy = 75
TotalPP = 15
Target = NearOther
FunctionCode = SleepTarget
Flags = CanProtect,CanMirrorMove,Powder
Description = The user scatters a big cloud of sleep-inducing dust around the target.
#-------------------------------
[SPIKYSHIELD]
Name = Spiky Shield
Type = GRASS
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
Priority = 4
FunctionCode = ProtectUserFromTargetingMovesSpikyShield
Description = Protects the user from attacks. Also damages attackers that make contact with the user.
#-------------------------------
[SPORE]
Name = Spore
Type = GRASS
Category = Status
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = SleepTarget
Flags = CanProtect,CanMirrorMove,Powder
Description = The user scatters bursts of spores that induce sleep.
#-------------------------------
[STRENGTHSAP]
Name = Strength Sap
Type = GRASS
Category = Status
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = HealUserByTargetAttackLowerTargetAttack1
Flags = CanProtect,CanMirrorMove
Description = The user restores its HP by the target's Attack stat amount. Then lowers the target's Attack stat.
#-------------------------------
[STUNSPORE]
Name = Stun Spore
Type = GRASS
Category = Status
Accuracy = 75
TotalPP = 30
Target = NearOther
FunctionCode = ParalyzeTarget
Flags = CanProtect,CanMirrorMove,Powder
Description = The user scatters a cloud of paralyzing powder. It may leave the target with paralysis.
#-------------------------------
[SYNTHESIS]
Name = Synthesis
Type = GRASS
Category = Status
Accuracy = 0
TotalPP = 5
Target = User
FunctionCode = HealUserDependingOnWeather
Description = The user restores its own HP. The amount of HP regained varies with the weather.
#-------------------------------
[WORRYSEED]
Name = Worry Seed
Type = GRASS
Category = Status
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = SetTargetAbilityToInsomnia
Flags = CanProtect,CanMirrorMove
Description = A seed that causes worry is planted on the foe. It prevents sleep by making its Ability Insomnia.
#-------------------------------
[PRECIPICEBLADES]
Name = Precipice Blades
Type = GROUND
Category = Physical
Power = 120
Accuracy = 85
TotalPP = 10
Target = AllNearFoes
FunctionCode = None
Flags = CanProtect,CanMirrorMove,CannotMetronome
Description = The user attacks its foes by manifesting the power of the land in fearsome blades of stone.
#-------------------------------
[EARTHQUAKE]
Name = Earthquake
Type = GROUND
Category = Physical
Power = 100
Accuracy = 100
TotalPP = 10
Target = AllNearOthers
FunctionCode = DoublePowerIfTargetUnderground
Flags = CanProtect,CanMirrorMove
Description = The user sets off an earthquake that strikes every Pokémon around it.
#-------------------------------
[HIGHHORSEPOWER]
Name = High Horsepower
Type = GROUND
Category = Physical
Power = 95
Accuracy = 95
TotalPP = 10
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The user fiercely attacks the target using its entire body.
#-------------------------------
[EARTHPOWER]
Name = Earth Power
Type = GROUND
Category = Special
Power = 90
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetSpDef1
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The user makes the ground under the foe erupt with power. It may also lower the target's Sp. Def.
#-------------------------------
[LANDSWRATH]
Name = Land's Wrath
Type = GROUND
Category = Physical
Power = 90
Accuracy = 100
TotalPP = 10
Target = AllNearFoes
FunctionCode = None
Flags = CanProtect,CanMirrorMove
Description = The user gathers the energy of the land and focuses that power on foes to damage them.
#-------------------------------
[THOUSANDARROWS]
Name = Thousand Arrows
Type = GROUND
Category = Physical
Power = 90
Accuracy = 100
TotalPP = 10
Target = AllNearFoes
FunctionCode = HitsTargetInSkyGroundsTarget
Flags = CanProtect,CanMirrorMove,CannotMetronome
Description = This move also hits Pokémon that are in the air. Those Pokémon are knocked down to the ground.
#-------------------------------
[THOUSANDWAVES]
Name = Thousand Waves
Type = GROUND
Category = Physical
Power = 90
Accuracy = 100
TotalPP = 10
Target = AllNearFoes
FunctionCode = TrapTargetInBattleMainEffect
Flags = CanProtect,CanMirrorMove,CannotMetronome
Description = The user attacks with a wave that crawls along the ground. Those it hits can't flee from battle.
#-------------------------------
[DIG]
Name = Dig
Type = GROUND
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = TwoTurnAttackInvulnerableUnderground
Flags = Contact,CanProtect,CanMirrorMove
Description = The user burrows, then attacks on the second turn. It can also be used to exit dungeons.
#-------------------------------
[DRILLRUN]
Name = Drill Run
Type = GROUND
Category = Physical
Power = 80
Accuracy = 95
TotalPP = 10
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove,HighCriticalHitRate
Description = The user crashes into its target while rotating its body like a drill. Critical hits land more easily.
#-------------------------------
[STOMPINGTANTRUM]
Name = Stomping Tantrum
Type = GROUND
Category = Physical
Power = 75
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = DoublePowerIfUserLastMoveFailed
Flags = Contact,CanProtect,CanMirrorMove
Description = The user attacks driven by frustration. Power increases if the user's previous move failed.
#-------------------------------
[SCORCHINGSANDS]
Name = Scorching Sands
Type = GROUND
Category = Special
Power = 70
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = BurnTarget
Flags = CanProtect,CanMirrorMove,ThawsUser
EffectChance = 30
Description = The user throws scorching sand at the target to attack. This may also burn the target.
#-------------------------------
[BONECLUB]
Name = Bone Club
Type = GROUND
Category = Physical
Power = 65
Accuracy = 85
TotalPP = 20
Target = NearOther
FunctionCode = FlinchTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The user clubs the target with a bone. It may also make the target flinch.
#-------------------------------
[MUDBOMB]
Name = Mud Bomb
Type = GROUND
Category = Special
Power = 65
Accuracy = 85
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetAccuracy1
Flags = CanProtect,CanMirrorMove,Bomb
EffectChance = 30
Description = The user launches a hard-packed mud ball to attack. It may also lower the target's accuracy.
#-------------------------------
[BULLDOZE]
Name = Bulldoze
Type = GROUND
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 20
Target = AllNearOthers
FunctionCode = LowerTargetSpeed1WeakerInGrassyTerrain
Flags = CanProtect,CanMirrorMove
EffectChance = 100
Description = The user strikes everything around it by stomping on the ground. It reduces hit Pokémon's Speed.
#-------------------------------
[MUDSHOT]
Name = Mud Shot
Type = GROUND
Category = Special
Power = 55
Accuracy = 95
TotalPP = 15
Target = NearOther
FunctionCode = LowerTargetSpeed1
Flags = CanProtect,CanMirrorMove
EffectChance = 100
Description = The user attacks by hurling a blob of mud at the target. It also reduces the target's Speed.
#-------------------------------
[BONEMERANG]
Name = Bonemerang
Type = GROUND
Category = Physical
Power = 50
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = HitTwoTimes
Flags = CanProtect,CanMirrorMove
Description = The user throws the bone it holds. The bone loops to hit the target twice, coming and going.
#-------------------------------
[SANDTOMB]
Name = Sand Tomb
Type = GROUND
Category = Physical
Power = 35
Accuracy = 85
TotalPP = 15
Target = NearOther
FunctionCode = BindTarget
Flags = CanProtect,CanMirrorMove
Description = The user traps the target inside a harshly raging sandstorm for four to five turns.
#-------------------------------
[BONERUSH]
Name = Bone Rush
Type = GROUND
Category = Physical
Power = 25
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = HitTwoToFiveTimes
Flags = CanProtect,CanMirrorMove
Description = The user strikes the target with a hard bone two to five times in a row.
#-------------------------------
[MUDSLAP]
Name = Mud-Slap
Type = GROUND
Category = Special
Power = 20
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetAccuracy1
Flags = CanProtect,CanMirrorMove
EffectChance = 100
Description = The user hurls mud in the target's face to inflict damage and lower its accuracy.
#-------------------------------
[FISSURE]
Name = Fissure
Type = GROUND
Category = Physical
Power = 1
Accuracy = 30
TotalPP = 5
Target = NearOther
FunctionCode = OHKOHitsUndergroundTarget
Flags = CanProtect,CanMirrorMove
Description = The user opens up a fissure in the ground and drops the foe in. The target instantly faints if it hits.
#-------------------------------
[MAGNITUDE]
Name = Magnitude
Type = GROUND
Category = Physical
Power = 1
Accuracy = 100
TotalPP = 30
Target = AllNearOthers
FunctionCode = RandomPowerDoublePowerIfTargetUnderground
Flags = CanProtect,CanMirrorMove
Description = The user looses a ground-shaking quake affecting everyone around the user. Its power varies.
#-------------------------------
[MUDSPORT]
Name = Mud Sport
Type = GROUND
Category = Status
Accuracy = 0
TotalPP = 15
Target = BothSides
FunctionCode = StartWeakenElectricMoves
Description = The user covers itself with mud. It weakens Electric-type moves while the user is in the battle.
#-------------------------------
[ROTOTILLER]
Name = Rototiller
Type = GROUND
Category = Status
Accuracy = 0
TotalPP = 10
Target = AllBattlers
FunctionCode = RaiseGroundedGrassBattlersAtkSpAtk1
Description = The user tills the soil to encourage plant growth. This raises the Attack and Sp. Atk of Grass types.
#-------------------------------
[SANDATTACK]
Name = Sand Attack
Type = GROUND
Category = Status
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = LowerTargetAccuracy1
Flags = CanProtect,CanMirrorMove
Description = Sand is hurled in the target's face, reducing its accuracy.
#-------------------------------
[SHOREUP]
Name = Shore Up
Type = GROUND
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = HealUserDependingOnSandstorm
Description = The user regains up to half of its max HP. It restores more HP in a sandstorm.
#-------------------------------
[SPIKES]
Name = Spikes
Type = GROUND
Category = Status
Accuracy = 0
TotalPP = 20
Target = FoeSide
FunctionCode = AddSpikesToFoeSide
Description = The user lays a trap of spikes at the foe's feet. The trap hurts foes that switch into battle.
#-------------------------------
[FREEZESHOCK]
Name = Freeze Shock
Type = ICE
Category = Physical
Power = 140
Accuracy = 90
TotalPP = 5
Target = NearOther
FunctionCode = TwoTurnAttackParalyzeTarget
Flags = CanProtect,CanMirrorMove,CannotMetronome
EffectChance = 30
Description = On the second turn, the user hits the foe with electrically charged ice. It may also paralyze the foe.
#-------------------------------
[ICEBURN]
Name = Ice Burn
Type = ICE
Category = Special
Power = 140
Accuracy = 90
TotalPP = 5
Target = NearOther
FunctionCode = TwoTurnAttackBurnTarget
Flags = CanProtect,CanMirrorMove,CannotMetronome
EffectChance = 30
Description = On the second turn, an ultracold, freezing wind surrounds the foe. This may leave it with a burn.
#-------------------------------
[GLACIALLANCE]
Name = Glacial Lance
Type = ICE
Category = Physical
Power = 130
Accuracy = 100
TotalPP = 5
Target = AllNearFoes
FunctionCode = None
Flags = CanProtect,CanMirrorMove,CannotMetronome
Description = The user attacks by hurling a blizzard-cloaked icicle lance at opposing Pokémon.
#-------------------------------
[BLIZZARD]
Name = Blizzard
Type = ICE
Category = Special
Power = 110
Accuracy = 70
TotalPP = 5
Target = AllNearFoes
FunctionCode = FreezeTargetAlwaysHitsInHail
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = A howling blizzard is summoned to strike the opposing team. It may also freeze them solid.
#-------------------------------
[ICEHAMMER]
Name = Ice Hammer
Type = ICE
Category = Physical
Power = 100
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = LowerUserSpeed1
Flags = Contact,CanProtect,CanMirrorMove,Punching
Description = The user swings and hits with its strong, heavy fist. It lowers the user's Speed, however.
#-------------------------------
[ICEBEAM]
Name = Ice Beam
Type = ICE
Category = Special
Power = 90
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = FreezeTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The target is struck with an icy-cold beam of energy. It may also freeze the target solid.
#-------------------------------
[ICICLECRASH]
Name = Icicle Crash
Type = ICE
Category = Physical
Power = 85
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = FlinchTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 30
Description = The user attacks by harshly dropping an icicle onto the foe. It may also make the target flinch.
#-------------------------------
[ICEPUNCH]
Name = Ice Punch
Type = ICE
Category = Physical
Power = 75
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = FreezeTarget
Flags = Contact,CanProtect,CanMirrorMove,Punching
EffectChance = 10
Description = The target is punched with an icy fist. It may also leave the target frozen.
#-------------------------------
[FREEZEDRY]
Name = Freeze-Dry
Type = ICE
Category = Special
Power = 70
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = FreezeTargetSuperEffectiveAgainstWater
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The user rapidly cools the target. This may freeze the target. Is super-effective on Water types.
#-------------------------------
[AURORABEAM]
Name = Aurora Beam
Type = ICE
Category = Special
Power = 65
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = LowerTargetAttack1
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The target is hit with a rainbow-colored beam. This may also lower the target's Attack stat.
#-------------------------------
[GLACIATE]
Name = Glaciate
Type = ICE
Category = Special
Power = 65
Accuracy = 95
TotalPP = 10
Target = AllNearFoes
FunctionCode = LowerTargetSpeed1
Flags = CanProtect,CanMirrorMove
EffectChance = 100
Description = The user attacks by blowing freezing cold air at the foe. This attack reduces the targets' Speed stat.
#-------------------------------
[ICEFANG]
Name = Ice Fang
Type = ICE
Category = Physical
Power = 65
Accuracy = 95
TotalPP = 15
Target = NearOther
FunctionCode = FreezeFlinchTarget
Flags = Contact,CanProtect,CanMirrorMove,Biting
EffectChance = 101
Description = The user bites with cold-infused fangs. It may also make the target flinch or leave it frozen.
#-------------------------------
[AVALANCHE]
Name = Avalanche
Type = ICE
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 10
Target = NearOther
Priority = -4
FunctionCode = DoublePowerIfUserLostHPThisTurn
Flags = Contact,CanProtect,CanMirrorMove
Description = An attack move that inflicts double the damage if the user has been hurt by the foe in the same turn.
#-------------------------------
[FROSTBREATH]
Name = Frost Breath
Type = ICE
Category = Special
Power = 60
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = AlwaysCriticalHit
Flags = CanProtect,CanMirrorMove
Description = The user blows a cold breath on the target. This attack always results in a critical hit.
#-------------------------------
[ICYWIND]
Name = Icy Wind
Type = ICE
Category = Special
Power = 55
Accuracy = 95
TotalPP = 15
Target = AllNearFoes
FunctionCode = LowerTargetSpeed1
Flags = CanProtect,CanMirrorMove
EffectChance = 100
Description = The user attacks with a gust of chilled air. It also lowers the targets' Speed stat.
#-------------------------------
[ICESHARD]
Name = Ice Shard
Type = ICE
Category = Physical
Power = 40
Accuracy = 100
TotalPP = 30
Target = NearOther
Priority = 1
FunctionCode = None
Flags = CanProtect,CanMirrorMove
Description = The user flash freezes chunks of ice and hurls them at the target. This move always goes first.
#-------------------------------
[POWDERSNOW]
Name = Powder Snow
Type = ICE
Category = Special
Power = 40
Accuracy = 100
TotalPP = 25
Target = AllNearFoes
FunctionCode = FreezeTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The user attacks with a chilling gust of powdery snow. It may also freeze the targets.
#-------------------------------
[ICEBALL]
Name = Ice Ball
Type = ICE
Category = Physical
Power = 30
Accuracy = 90
TotalPP = 20
Target = NearOther
FunctionCode = MultiTurnAttackPowersUpEachTurn
Flags = Contact,CanProtect,CanMirrorMove,Bomb
Description = The user continually rolls into the target over five turns. It becomes stronger each time it hits.
#-------------------------------
[ICICLESPEAR]
Name = Icicle Spear
Type = ICE
Category = Physical
Power = 25
Accuracy = 100
TotalPP = 30
Target = NearOther
FunctionCode = HitTwoToFiveTimes
Flags = CanProtect,CanMirrorMove
Description = The user launches sharp icicles at the target. It strikes two to five times in a row.
#-------------------------------
[TRIPLEAXEL]
Name = Triple Axel
Type = ICE
Category = Physical
Power = 20
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = HitThreeTimesPowersUpWithEachHit
Flags = Contact,CanProtect,CanMirrorMove
Description = A consecutive three-kick attack that becomes more powerful with each successful hit.
#-------------------------------
[SHEERCOLD]
Name = Sheer Cold
Type = ICE
Category = Special
Power = 1
Accuracy = 30
TotalPP = 5
Target = NearOther
FunctionCode = OHKOIce
Flags = CanProtect,CanMirrorMove
Description = The foe is attacked with a blast of absolute-zero cold. The target instantly faints if it hits.
#-------------------------------
[AURORAVEIL]
Name = Aurora Veil
Type = ICE
Category = Status
Accuracy = 0
TotalPP = 20
Target = UserSide
FunctionCode = StartWeakenDamageAgainstUserSideIfHail
Description = This move reduces damage from attacks for five turns. This can be used only in a hailstorm.
#-------------------------------
[HAIL]
Name = Hail
Type = ICE
Category = Status
Accuracy = 0
TotalPP = 10
Target = BothSides
FunctionCode = StartHailWeather
Description = The user summons a hail storm lasting five turns. It damages all Pokémon except the Ice type.
#-------------------------------
[HAZE]
Name = Haze
Type = ICE
Category = Status
Accuracy = 0
TotalPP = 30
Target = BothSides
FunctionCode = ResetAllBattlersStatStages
Description = The user creates a haze that eliminates every stat change among all the Pokémon engaged in battle.
#-------------------------------
[MIST]
Name = Mist
Type = ICE
Category = Status
Accuracy = 0
TotalPP = 30
Target = UserSide
FunctionCode = StartUserSideImmunityToStatStageLowering
Description = The user cloaks its body with a white mist that prevents any of its stats from being cut for five turns.
#-------------------------------
[EXPLOSION]
Name = Explosion
Type = NORMAL
Category = Physical
Power = 250
Accuracy = 100
TotalPP = 5
Target = AllNearOthers
FunctionCode = UserFaintsExplosive
Flags = CanProtect,CanMirrorMove
Description = The user explodes to inflict damage on those around it. The user faints upon using this move.
#-------------------------------
[SELFDESTRUCT]
Name = Self-Destruct
Type = NORMAL
Category = Physical
Power = 200
Accuracy = 100
TotalPP = 5
Target = AllNearOthers
FunctionCode = UserFaintsExplosive
Flags = CanProtect,CanMirrorMove
Description = The user blows up to inflict damage on all Pokémon in battle. The user faints upon using this move.
#-------------------------------
[GIGAIMPACT]
Name = Giga Impact
Type = NORMAL
Category = Physical
Power = 150
Accuracy = 90
TotalPP = 5
Target = NearOther
FunctionCode = AttackAndSkipNextTurn
Flags = Contact,CanProtect,CanMirrorMove
Description = The user charges at the target using every bit of its power. The user must rest on the next turn.
#-------------------------------
[HYPERBEAM]
Name = Hyper Beam
Type = NORMAL
Category = Special
Power = 150
Accuracy = 90
TotalPP = 5
Target = NearOther
FunctionCode = AttackAndSkipNextTurn
Flags = CanProtect,CanMirrorMove
Description = The foe is attacked with a powerful beam. The user must rest on the next turn to regain its energy.
#-------------------------------
[BOOMBURST]
Name = Boomburst
Type = NORMAL
Category = Special
Power = 140
Accuracy = 100
TotalPP = 10
Target = AllNearOthers
FunctionCode = None
Flags = CanProtect,CanMirrorMove,Sound
Description = The user attacks everything around it with the destructive power of a terrible, explosive sound.
#-------------------------------
[LASTRESORT]
Name = Last Resort
Type = NORMAL
Category = Physical
Power = 140
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = FailsIfUserHasUnusedMove
Flags = Contact,CanProtect,CanMirrorMove
Description = This move can be used only after the user has used all the other moves it knows in the battle.
#-------------------------------
[SKULLBASH]
Name = Skull Bash
Type = NORMAL
Category = Physical
Power = 130
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = TwoTurnAttackChargeRaiseUserDefense1
Flags = Contact,CanProtect,CanMirrorMove
Description = The user tucks in its head to raise its Defense in the first turn, then rams the foe on the next turn.
#-------------------------------
[DOUBLEEDGE]
Name = Double-Edge
Type = NORMAL
Category = Physical
Power = 120
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = RecoilThirdOfDamageDealt
Flags = Contact,CanProtect,CanMirrorMove
Description = A reckless, life-risking tackle. It also damages the user by a fairly large amount, however.
#-------------------------------
[HEADCHARGE]
Name = Head Charge
Type = NORMAL
Category = Physical
Power = 120
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = RecoilQuarterOfDamageDealt
Flags = Contact,CanProtect,CanMirrorMove
Description = The user charges its head into the foe, using its powerful guard hair. The user also takes damage.
#-------------------------------
[MEGAKICK]
Name = Mega Kick
Type = NORMAL
Category = Physical
Power = 120
Accuracy = 75
TotalPP = 5
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is attacked by a kick launched with muscle-packed power.
#-------------------------------
[MULTIATTACK]
Name = Multi-Attack
Type = NORMAL
Category = Physical
Power = 120
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = TypeDependsOnUserMemory
Flags = Contact,CanProtect,CanMirrorMove
Description = Cloaking itself in high energy, the user slams into the target. This move's type depends on the held memory.
#-------------------------------
[TECHNOBLAST]
Name = Techno Blast
Type = NORMAL
Category = Special
Power = 120
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = TypeDependsOnUserDrive
Flags = CanProtect,CanMirrorMove,CannotMetronome
Description = The user fires a beam of light at its target. The type changes depending on the Drive the user holds.
#-------------------------------
[THRASH]
Name = Thrash
Type = NORMAL
Category = Physical
Power = 120
Accuracy = 100
TotalPP = 10
Target = RandomNearFoe
FunctionCode = MultiTurnAttackConfuseUserAtEnd
Flags = Contact,CanProtect,CanMirrorMove
Description = The user rampages and attacks for two to three turns. It then becomes confused, however.
#-------------------------------
[EGGBOMB]
Name = Egg Bomb
Type = NORMAL
Category = Physical
Power = 100
Accuracy = 75
TotalPP = 10
Target = NearOther
FunctionCode = None
Flags = CanProtect,CanMirrorMove,Bomb
Description = A large egg is hurled at the target with maximum force to inflict damage.
#-------------------------------
[JUDGMENT]
Name = Judgment
Type = NORMAL
Category = Special
Power = 100
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = TypeDependsOnUserPlate
Flags = CanProtect,CanMirrorMove
Description = The user releases countless shots of light. Its type varies with the kind of Plate the user is holding.
#-------------------------------
[HYPERVOICE]
Name = Hyper Voice
Type = NORMAL
Category = Special
Power = 90
Accuracy = 100
TotalPP = 10
Target = AllNearFoes
FunctionCode = None
Flags = CanProtect,CanMirrorMove,Sound
Description = The user lets loose a horribly echoing shout with the power to inflict damage.
#-------------------------------
[REVELATIONDANCE]
Name = Revelation Dance
Type = NORMAL
Category = Special
Power = 90
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = TypeIsUserFirstType
Flags = CanProtect,CanMirrorMove,Dance
Description = The user attacks the target by dancing very hard. The user's type determines the type of this move.
#-------------------------------
[ROCKCLIMB]
Name = Rock Climb
Type = NORMAL
Category = Physical
Power = 90
Accuracy = 85
TotalPP = 20
Target = NearOther
FunctionCode = ConfuseTarget
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 20
Description = The user attacks the target by smashing into it with incredible force. It may also confuse the target.
#-------------------------------
[TAKEDOWN]
Name = Take Down
Type = NORMAL
Category = Physical
Power = 90
Accuracy = 85
TotalPP = 20
Target = NearOther
FunctionCode = RecoilQuarterOfDamageDealt
Flags = Contact,CanProtect,CanMirrorMove
Description = A reckless, full-body charge attack for slamming into the foe. It also damages the user a little.
#-------------------------------
[UPROAR]
Name = Uproar
Type = NORMAL
Category = Special
Power = 90
Accuracy = 100
TotalPP = 10
Target = RandomNearFoe
FunctionCode = MultiTurnAttackPreventSleeping
Flags = CanProtect,CanMirrorMove,Sound
Description = The user attacks in an uproar for three turns. Over that time, no one can fall asleep.
#-------------------------------
[BODYSLAM]
Name = Body Slam
Type = NORMAL
Category = Physical
Power = 85
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = ParalyzeTarget
Flags = Contact,CanProtect,CanMirrorMove,TramplesMinimize
EffectChance = 30
Description = The user drops onto the target with its full body weight. It may leave the target with paralysis.
#-------------------------------
[EXTREMESPEED]
Name = Extreme Speed
Type = NORMAL
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 5
Target = NearOther
Priority = 2
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The user charges the target at blinding speed. This attack always goes before any other move.
#-------------------------------
[HYPERFANG]
Name = Hyper Fang
Type = NORMAL
Category = Physical
Power = 80
Accuracy = 90
TotalPP = 15
Target = NearOther
FunctionCode = FlinchTarget
Flags = Contact,CanProtect,CanMirrorMove,Biting
EffectChance = 10
Description = The user bites hard on the target with its sharp front fangs. It may also make the target flinch.
#-------------------------------
[MEGAPUNCH]
Name = Mega Punch
Type = NORMAL
Category = Physical
Power = 80
Accuracy = 85
TotalPP = 20
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove,Punching
Description = The target is slugged by a punch thrown with muscle-packed power.
#-------------------------------
[RAZORWIND]
Name = Razor Wind
Type = NORMAL
Category = Special
Power = 80
Accuracy = 100
TotalPP = 10
Target = AllNearFoes
FunctionCode = TwoTurnAttack
Flags = CanProtect,CanMirrorMove,HighCriticalHitRate
Description = A two-turn attack. Blades of wind hit the foe on the second turn. Critical hits land more easily.
#-------------------------------
[SLAM]
Name = Slam
Type = NORMAL
Category = Physical
Power = 80
Accuracy = 75
TotalPP = 20
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is slammed with a long tail, vines, etc., to inflict damage.
#-------------------------------
[STRENGTH]
Name = Strength
Type = NORMAL
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is slugged with a punch thrown at maximum power. It can also be used to move heavy boulders.
#-------------------------------
[TRIATTACK]
Name = Tri Attack
Type = NORMAL
Category = Special
Power = 80
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = ParalyzeBurnOrFreezeTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 20
Description = The user strikes with a simultaneous three-beam attack. May also paralyze, burn, or freeze the target.
#-------------------------------
[CRUSHCLAW]
Name = Crush Claw
Type = NORMAL
Category = Physical
Power = 75
Accuracy = 95
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetDefense1
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 50
Description = The user slashes the target with hard and sharp claws. It may also lower the target's Defense.
#-------------------------------
[RELICSONG]
Name = Relic Song
Type = NORMAL
Category = Special
Power = 75
Accuracy = 100
TotalPP = 10
Target = AllNearFoes
FunctionCode = SleepTargetChangeUserMeloettaForm
Flags = CanProtect,CanMirrorMove,Sound,CannotMetronome
EffectChance = 10
Description = An ancient song appeals to the hearts of those listening. It may also induce sleep.
#-------------------------------
[CHIPAWAY]
Name = Chip Away
Type = NORMAL
Category = Physical
Power = 70
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = IgnoreTargetDefSpDefEvaStatStages
Flags = Contact,CanProtect,CanMirrorMove
Description = Seeking an opening, the user strikes continually. The foe's stat changes don't affect the damage.
#-------------------------------
[DIZZYPUNCH]
Name = Dizzy Punch
Type = NORMAL
Category = Physical
Power = 70
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = ConfuseTarget
Flags = Contact,CanProtect,CanMirrorMove,Punching
EffectChance = 20
Description = The target is hit with rhythmically launched punches that may also leave it confused.
#-------------------------------
[FACADE]
Name = Facade
Type = NORMAL
Category = Physical
Power = 70
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = DoublePowerIfUserPoisonedBurnedParalyzed
Flags = Contact,CanProtect,CanMirrorMove
Description = An attack move that doubles its power if the user is poisoned, burned, or has paralysis.
#-------------------------------
[HEADBUTT]
Name = Headbutt
Type = NORMAL
Category = Physical
Power = 70
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = FlinchTarget
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 30
Description = The user sticks out its head and attacks by charging into the foe. It may also make the target flinch.
#-------------------------------
[RETALIATE]
Name = Retaliate
Type = NORMAL
Category = Physical
Power = 70
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = DoublePowerIfAllyFaintedLastTurn
Flags = Contact,CanProtect,CanMirrorMove
Description = Gets revenge for a fainted ally. If an ally fainted in the last turn, this attack's damage increases.
#-------------------------------
[SECRETPOWER]
Name = Secret Power
Type = NORMAL
Category = Physical
Power = 70
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = EffectDependsOnEnvironment
Flags = CanProtect,CanMirrorMove
EffectChance = 30
Description = The user attacks with a secret power. Its added effects vary depending on the user's environment.
#-------------------------------
[SLASH]
Name = Slash
Type = NORMAL
Category = Physical
Power = 70
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove,HighCriticalHitRate
Description = The target is attacked with a slash of claws or blades. Critical hits land more easily.
#-------------------------------
[SMELLINGSALTS]
Name = Smelling Salts
Type = NORMAL
Category = Physical
Power = 70
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = DoublePowerIfTargetParalyzedCureTarget
Flags = Contact,CanProtect,CanMirrorMove
Description = This attack inflicts double damage on a paralyzed foe. It also cures the target's paralysis, however.
#-------------------------------
[HORNATTACK]
Name = Horn Attack
Type = NORMAL
Category = Physical
Power = 65
Accuracy = 100
TotalPP = 25
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is jabbed with a sharply pointed horn to inflict damage.
#-------------------------------
[STOMP]
Name = Stomp
Type = NORMAL
Category = Physical
Power = 65
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = FlinchTarget
Flags = Contact,CanProtect,CanMirrorMove,TramplesMinimize
EffectChance = 30
Description = The target is stomped with a big foot. It may also make the target flinch.
#-------------------------------
[COVET]
Name = Covet
Type = NORMAL
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 25
Target = NearOther
FunctionCode = UserTakesTargetItem
Flags = Contact,CanProtect,CanMirrorMove
Description = The user endearingly approaches the target, then steals the target's held item.
#-------------------------------
[HIDDENPOWER]
Name = Hidden Power
Type = NORMAL
Category = Special
Power = 60
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = TypeDependsOnUserIVs
Flags = CanProtect,CanMirrorMove
Description = A unique attack that varies in type and intensity depending on the Pokémon using it.
#-------------------------------
[ROUND]
Name = Round
Type = NORMAL
Category = Special
Power = 60
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = UsedAfterAllyRoundWithDoublePower
Flags = CanProtect,CanMirrorMove,Sound
Description = The user attacks with a song. Others can join in the Round and make the attack do greater damage.
#-------------------------------
[SWIFT]
Name = Swift
Type = NORMAL
Category = Special
Power = 60
Accuracy = 0
TotalPP = 20
Target = AllNearFoes
FunctionCode = None
Flags = CanProtect,CanMirrorMove
Description = Star-shaped rays are shot at the opposing team. This attack never misses.
#-------------------------------
[VISEGRIP]
Name = Vise Grip
Type = NORMAL
Category = Physical
Power = 55
Accuracy = 100
TotalPP = 30
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is gripped and squeezed from both sides to inflict damage.
#-------------------------------
[CUT]
Name = Cut
Type = NORMAL
Category = Physical
Power = 50
Accuracy = 95
TotalPP = 30
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is cut with a scythe or a claw. It can also be used to cut down thin trees.
#-------------------------------
[RAPIDSPIN]
Name = Rapid Spin
Type = NORMAL
Category = Physical
Power = 50
Accuracy = 100
TotalPP = 40
Target = NearOther
FunctionCode = RemoveUserBindingAndEntryHazards
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 100
Description = A spin attack that raises the user's Speed and eliminates the effects of Bind, Spikes, etc.
#-------------------------------
[SNORE]
Name = Snore
Type = NORMAL
Category = Special
Power = 50
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = FlinchTargetFailsIfUserNotAsleep
Flags = CanProtect,CanMirrorMove,Sound
EffectChance = 30
Description = An attack that can be used only if the user is asleep. The harsh noise may also make the target flinch.
#-------------------------------
[TERRAINPULSE]
Name = Terrain Pulse
Type = NORMAL
Category = Special
Power = 50
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = TypeAndPowerDependOnTerrain
Flags = CanProtect,CanMirrorMove,Pulse
Description = Utilizes the power of the terrain to attack. This move's type and power vary with the terrain.
#-------------------------------
[WEATHERBALL]
Name = Weather Ball
Type = NORMAL
Category = Special
Power = 50
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = TypeAndPowerDependOnWeather
Flags = CanProtect,CanMirrorMove,Bomb
Description = An attack move that varies in power and type depending on the weather.
#-------------------------------
[ECHOEDVOICE]
Name = Echoed Voice
Type = NORMAL
Category = Special
Power = 40
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = PowerHigherWithConsecutiveUseOnUserSide
Flags = CanProtect,CanMirrorMove,Sound
Description = The user attacks the foe with an echoing voice. If this move is used every turn, it does greater damage.
#-------------------------------
[FAKEOUT]
Name = Fake Out
Type = NORMAL
Category = Physical
Power = 40
Accuracy = 100
TotalPP = 10
Target = NearOther
Priority = 3
FunctionCode = FlinchTargetFailsIfNotUserFirstTurn
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 100
Description = An attack that hits first and makes the target flinch. It only works the first turn the user is in battle.
#-------------------------------
[FALSESWIPE]
Name = False Swipe
Type = NORMAL
Category = Physical
Power = 40
Accuracy = 100
TotalPP = 40
Target = NearOther
FunctionCode = CannotMakeTargetFaint
Flags = Contact,CanProtect,CanMirrorMove
Description = A restrained attack that prevents the target from fainting. The target is left with at least 1 HP.
#-------------------------------
[HOLDBACK]
Name = Hold Back
Type = NORMAL
Category = Physical
Power = 40
Accuracy = 100
TotalPP = 40
Target = NearOther
FunctionCode = CannotMakeTargetFaint
Flags = Contact,CanProtect,CanMirrorMove
Description = The user holds back when it attacks, and the target is left with at least 1 HP.
#-------------------------------
[PAYDAY]
Name = Pay Day
Type = NORMAL
Category = Physical
Power = 40
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = AddMoneyGainedFromBattle
Flags = CanProtect,CanMirrorMove
Description = Numerous coins are hurled at the target to inflict damage. Money is earned after battle.
#-------------------------------
[POUND]
Name = Pound
Type = NORMAL
Category = Physical
Power = 40
Accuracy = 100
TotalPP = 35
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is physically pounded with a long tail or a foreleg, etc.
#-------------------------------
[QUICKATTACK]
Name = Quick Attack
Type = NORMAL
Category = Physical
Power = 40
Accuracy = 100
TotalPP = 30
Target = NearOther
Priority = 1
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The user lunges at the target at a speed that makes it almost invisible. It is sure to strike first.
#-------------------------------
[SCRATCH]
Name = Scratch
Type = NORMAL
Category = Physical
Power = 40
Accuracy = 100
TotalPP = 35
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = Hard, pointed, and sharp claws rake the target to inflict damage.
#-------------------------------
[TACKLE]
Name = Tackle
Type = NORMAL
Category = Physical
Power = 40
Accuracy = 100
TotalPP = 35
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = A physical attack in which the user charges and slams into the target with its whole body.
#-------------------------------
[DOUBLEHIT]
Name = Double Hit
Type = NORMAL
Category = Physical
Power = 35
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = HitTwoTimes
Flags = Contact,CanProtect,CanMirrorMove
Description = The user slams the target with a long tail, vines, or tentacle. The target is hit twice in a row.
#-------------------------------
[FEINT]
Name = Feint
Type = NORMAL
Category = Physical
Power = 30
Accuracy = 100
TotalPP = 10
Target = NearOther
Priority = 2
FunctionCode = RemoveProtections
Flags = CanMirrorMove
Description = An attack that hits a target using Protect or Detect. It also lifts the effects of those moves.
#-------------------------------
[TAILSLAP]
Name = Tail Slap
Type = NORMAL
Category = Physical
Power = 25
Accuracy = 85
TotalPP = 10
Target = NearOther
FunctionCode = HitTwoToFiveTimes
Flags = Contact,CanProtect,CanMirrorMove
Description = The user attacks by striking the target with its hard tail. It hits the Pokémon two to five times in a row.
#-------------------------------
[RAGE]
Name = Rage
Type = NORMAL
Category = Physical
Power = 20
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = StartRaiseUserAtk1WhenDamaged
Flags = Contact,CanProtect,CanMirrorMove
Description = As long as this move is in use, the user's Attack rises each time the user is hit in battle.
#-------------------------------
[SPIKECANNON]
Name = Spike Cannon
Type = NORMAL
Category = Physical
Power = 20
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = HitTwoToFiveTimes
Flags = CanProtect,CanMirrorMove
Description = Sharp spikes are shot at the target in rapid succession. They hit two to five times in a row.
#-------------------------------
[COMETPUNCH]
Name = Comet Punch
Type = NORMAL
Category = Physical
Power = 18
Accuracy = 85
TotalPP = 15
Target = NearOther
FunctionCode = HitTwoToFiveTimes
Flags = Contact,CanProtect,CanMirrorMove,Punching
Description = The target is hit with a flurry of punches that strike two to five times in a row.
#-------------------------------
[FURYSWIPES]
Name = Fury Swipes
Type = NORMAL
Category = Physical
Power = 18
Accuracy = 80
TotalPP = 15
Target = NearOther
FunctionCode = HitTwoToFiveTimes
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is raked with sharp claws or scythes for two to five times in quick succession.
#-------------------------------
[BARRAGE]
Name = Barrage
Type = NORMAL
Category = Physical
Power = 15
Accuracy = 85
TotalPP = 20
Target = NearOther
FunctionCode = HitTwoToFiveTimes
Flags = CanProtect,CanMirrorMove,Bomb
Description = Round objects are hurled at the target to strike two to five times in a row.
#-------------------------------
[BIND]
Name = Bind
Type = NORMAL
Category = Physical
Power = 15
Accuracy = 85
TotalPP = 20
Target = NearOther
FunctionCode = BindTarget
Flags = Contact,CanProtect,CanMirrorMove
Description = Things such as long bodies or tentacles are used to bind and squeeze the foe for four to five turns.
#-------------------------------
[DOUBLESLAP]
Name = Double Slap
Type = NORMAL
Category = Physical
Power = 15
Accuracy = 85
TotalPP = 10
Target = NearOther
FunctionCode = HitTwoToFiveTimes
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is slapped repeatedly, back and forth, two to five times in a row.
#-------------------------------
[FURYATTACK]
Name = Fury Attack
Type = NORMAL
Category = Physical
Power = 15
Accuracy = 85
TotalPP = 20
Target = NearOther
FunctionCode = HitTwoToFiveTimes
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is jabbed repeatedly with a horn or beak two to five times in a row.
#-------------------------------
[WRAP]
Name = Wrap
Type = NORMAL
Category = Physical
Power = 15
Accuracy = 90
TotalPP = 20
Target = NearOther
FunctionCode = BindTarget
Flags = Contact,CanProtect,CanMirrorMove
Description = A long body or vines are used to wrap and squeeze the target for four to five turns.
#-------------------------------
[CONSTRICT]
Name = Constrict
Type = NORMAL
Category = Physical
Power = 10
Accuracy = 100
TotalPP = 35
Target = NearOther
FunctionCode = LowerTargetSpeed1
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 10
Description = The foe is attacked with long, creeping tentacles or vines. It may also lower the target's Speed.
#-------------------------------
[BIDE]
Name = Bide
Type = NORMAL
Category = Physical
Power = 1
Accuracy = 0
TotalPP = 10
Target = None
Priority = 1
FunctionCode = MultiTurnAttackBideThenReturnDoubleDamage
Flags = Contact,CanProtect
Description = The user endures attacks for two turns, then strikes back to cause double the damage taken.
#-------------------------------
[CRUSHGRIP]
Name = Crush Grip
Type = NORMAL
Category = Physical
Power = 1
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = PowerHigherWithTargetHP
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is crushed with great force. The attack is more powerful the more HP the target has left.
#-------------------------------
[ENDEAVOR]
Name = Endeavor
Type = NORMAL
Category = Physical
Power = 1
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = LowerTargetHPToUserHP
Flags = Contact,CanProtect,CanMirrorMove
Description = An attack move that cuts down the target's HP to equal the user's HP.
#-------------------------------
[FLAIL]
Name = Flail
Type = NORMAL
Category = Physical
Power = 1
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = PowerLowerWithUserHP
Flags = Contact,CanProtect,CanMirrorMove
Description = The user flails about aimlessly to attack. It becomes more powerful the less HP the user has.
#-------------------------------
[FRUSTRATION]
Name = Frustration
Type = NORMAL
Category = Physical
Power = 1
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = PowerLowerWithUserHappiness
Flags = Contact,CanProtect,CanMirrorMove
Description = A full-power attack that grows more powerful the less the user likes its Trainer.
#-------------------------------
[GUILLOTINE]
Name = Guillotine
Type = NORMAL
Category = Physical
Power = 1
Accuracy = 30
TotalPP = 5
Target = NearOther
FunctionCode = OHKO
Flags = Contact,CanProtect,CanMirrorMove
Description = A vicious, tearing attack with big pincers. The target will faint instantly if this attack hits.
#-------------------------------
[HORNDRILL]
Name = Horn Drill
Type = NORMAL
Category = Physical
Power = 1
Accuracy = 30
TotalPP = 5
Target = NearOther
FunctionCode = OHKO
Flags = Contact,CanProtect,CanMirrorMove
Description = The user stabs the foe with a horn that rotates like a drill. If it hits, the target faints instantly.
#-------------------------------
[NATURALGIFT]
Name = Natural Gift
Type = NORMAL
Category = Physical
Power = 1
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = TypeAndPowerDependOnUserBerry
Flags = CanProtect,CanMirrorMove
Description = The user draws power to attack by using its held Berry. The Berry determines its type and power.
#-------------------------------
[PRESENT]
Name = Present
Type = NORMAL
Category = Physical
Power = 1
Accuracy = 90
TotalPP = 15
Target = NearOther
FunctionCode = RandomlyDamageOrHealTarget
Flags = CanProtect,CanMirrorMove
Description = The user attacks by giving the target a gift with a hidden trap. It restores HP sometimes, however.
#-------------------------------
[RETURN]
Name = Return
Type = NORMAL
Category = Physical
Power = 1
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = PowerHigherWithUserHappiness
Flags = Contact,CanProtect,CanMirrorMove
Description = A full-power attack that grows more powerful the more the user likes its Trainer.
#-------------------------------
[SONICBOOM]
Name = Sonic Boom
Type = NORMAL
Category = Special
Power = 1
Accuracy = 90
TotalPP = 20
Target = NearOther
FunctionCode = FixedDamage20
Flags = CanProtect,CanMirrorMove
Description = The target is hit with a destructive shock wave that always inflicts 20 HP damage.
#-------------------------------
[SPITUP]
Name = Spit Up
Type = NORMAL
Category = Special
Power = 1
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = PowerDependsOnUserStockpile
Flags = CanProtect
Description = The power stored using the move Stockpile is released all at once in an attack.
#-------------------------------
[SUPERFANG]
Name = Super Fang
Type = NORMAL
Category = Physical
Power = 1
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = FixedDamageHalfTargetHP
Flags = Contact,CanProtect,CanMirrorMove
Description = The user chomps hard on the target with its sharp front fangs. It cuts the target's HP to half.
#-------------------------------
[TRUMPCARD]
Name = Trump Card
Type = NORMAL
Category = Special
Power = 1
Accuracy = 0
TotalPP = 5
Target = NearOther
FunctionCode = PowerHigherWithLessPP
Flags = Contact,CanProtect,CanMirrorMove
Description = The fewer PP this move has, the greater its attack power.
#-------------------------------
[WRINGOUT]
Name = Wring Out
Type = NORMAL
Category = Special
Power = 1
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = PowerHigherWithTargetHP
Flags = Contact,CanProtect,CanMirrorMove
Description = The user powerfully wrings the foe. The more HP the foe has, the greater this attack's power.
#-------------------------------
[ACUPRESSURE]
Name = Acupressure
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 30
Target = UserOrNearAlly
FunctionCode = RaiseTargetRandomStat2
Description = The user applies pressure to stress points, sharply boosting one of its stats.
#-------------------------------
[AFTERYOU]
Name = After You
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 15
Target = NearOther
FunctionCode = TargetActsNext
Description = The user helps the target and makes it use its move right after the user.
#-------------------------------
[ASSIST]
Name = Assist
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = UseRandomMoveFromUserParty
Description = The user hurriedly and randomly uses a move among those known by other Pokémon in the party.
#-------------------------------
[ATTRACT]
Name = Attract
Type = NORMAL
Category = Status
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = AttractTarget
Flags = CanProtect,CanMirrorMove
Description = If it is the opposite gender of the user, the target becomes infatuated and less likely to attack.
#-------------------------------
[BATONPASS]
Name = Baton Pass
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 40
Target = User
FunctionCode = SwitchOutUserPassOnEffects
Description = The user switches places with a party Pokémon in waiting, passing along any stat changes.
#-------------------------------
[BELLYDRUM]
Name = Belly Drum
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = MaxUserAttackLoseHalfOfTotalHP
Description = The user maximizes its Attack stat in exchange for HP equal to half its max HP.
#-------------------------------
[BESTOW]
Name = Bestow
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 15
Target = NearOther
FunctionCode = TargetTakesUserItem
Flags = CanMirrorMove
Description = The user passes its held item to the target when the target isn't holding an item.
#-------------------------------
[BLOCK]
Name = Block
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 5
Target = NearOther
FunctionCode = TrapTargetInBattle
Flags = CanMirrorMove
Description = The user blocks the target's way with arms spread wide to prevent escape.
#-------------------------------
[CAMOUFLAGE]
Name = Camouflage
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = SetUserTypesBasedOnEnvironment
Description = The user's type is changed depending on its environment, such as at water's edge, in grass, or in a cave.
#-------------------------------
[CAPTIVATE]
Name = Captivate
Type = NORMAL
Category = Status
Accuracy = 100
TotalPP = 20
Target = AllNearFoes
FunctionCode = LowerTargetSpAtk2IfCanAttract
Flags = CanProtect,CanMirrorMove
Description = If it is the opposite gender of the user, the target is charmed into harshly lowering its Sp. Atk stat.
#-------------------------------
[CELEBRATE]
Name = Celebrate
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 40
Target = User
FunctionCode = DoesNothingCongratulations
Description = The Pokémon congratulates you on your special day!
#-------------------------------
[CONFIDE]
Name = Confide
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 20
Target = NearOther
FunctionCode = LowerTargetSpAtk1
Flags = CanMirrorMove,Sound
Description = The user tells the target a secret. The target loses focus and its Sp. Atk stat is lowered.
#-------------------------------
[CONVERSION]
Name = Conversion
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 30
Target = User
FunctionCode = SetUserTypesToUserMoveType
Description = The user changes its type to become the same type as one of its moves.
#-------------------------------
[CONVERSION2]
Name = Conversion 2
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 30
Target = NearOther
FunctionCode = SetUserTypesToResistLastAttack
Description = The user changes its type to make itself resistant to the type of the attack the opponent used last.
#-------------------------------
[COPYCAT]
Name = Copycat
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = UseLastMoveUsed
Description = The user mimics the move used immediately before it. The move fails if no other move has been used yet.
#-------------------------------
[COURTCHANGE]
Name = Court Change
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = BothSides
FunctionCode = SwapSideEffects
Flags = CanMirrorMove
Description = With its mysterious power, the user swaps the effects on either side of the field.
#-------------------------------
[DEFENSECURL]
Name = Defense Curl
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 40
Target = User
FunctionCode = RaiseUserDefense1CurlUpUser
Description = The user curls up to conceal weak spots and raise its Defense stat.
#-------------------------------
[DISABLE]
Name = Disable
Type = NORMAL
Category = Status
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = DisableTargetLastMoveUsed
Flags = CanProtect,CanMirrorMove
Description = For four turns, this move prevents the target from using the move it last used.
#-------------------------------
[DOUBLETEAM]
Name = Double Team
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 15
Target = User
FunctionCode = RaiseUserEvasion1
Description = By moving rapidly, the user makes illusory copies of itself to raise its evasiveness.
#-------------------------------
[ENCORE]
Name = Encore
Type = NORMAL
Category = Status
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = DisableTargetUsingDifferentMove
Flags = CanProtect,CanMirrorMove
Description = The user compels the target to keep using only the move it last used for three turns.
#-------------------------------
[ENDURE]
Name = Endure
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
Priority = 4
FunctionCode = UserEnduresFaintingThisTurn
Description = The user endures any attack with at least 1 HP. Its chance of failing rises if it is used in succession.
#-------------------------------
[ENTRAINMENT]
Name = Entrainment
Type = NORMAL
Category = Status
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = SetTargetAbilityToUserAbility
Flags = CanProtect,CanMirrorMove
Description = The user dances to compel the target to mimic it, making the target's Ability the same as the user's.
#-------------------------------
[FLASH]
Name = Flash
Type = NORMAL
Category = Status
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = LowerTargetAccuracy1
Flags = CanProtect,CanMirrorMove
Description = The user flashes a light that cuts the target's accuracy. It can also be used to illuminate caves.
#-------------------------------
[FOCUSENERGY]
Name = Focus Energy
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 30
Target = User
FunctionCode = RaiseUserCriticalHitRate2
Description = The user takes a deep breath and focuses so that critical hits land more easily.
#-------------------------------
[FOLLOWME]
Name = Follow Me
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
Priority = 2
FunctionCode = RedirectAllMovesToUser
Description = The user draws attention to itself, making all targets take aim only at the user.
#-------------------------------
[FORESIGHT]
Name = Foresight
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 40
Target = NearOther
FunctionCode = StartNegateTargetEvasionStatStageAndGhostImmunity
Flags = CanProtect,CanMirrorMove
Description = Enables the user to hit a Ghost type with any kind of move. It also enables the user to hit an evasive foe.
#-------------------------------
[GLARE]
Name = Glare
Type = NORMAL
Category = Status
Accuracy = 100
TotalPP = 30
Target = NearOther
FunctionCode = ParalyzeTarget
Flags = CanProtect,CanMirrorMove
Description = The user intimidates the target with the pattern on its belly to cause paralysis.
#-------------------------------
[GROWL]
Name = Growl
Type = NORMAL
Category = Status
Accuracy = 100
TotalPP = 40
Target = AllNearFoes
FunctionCode = LowerTargetAttack1
Flags = CanProtect,CanMirrorMove,Sound
Description = The user growls in an endearing way, making the foe less wary. The foe's Attack stat is lowered.
#-------------------------------
[GROWTH]
Name = Growth
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = RaiseUserAtkSpAtk1Or2InSun
Description = The user's body grows all at once, raising the Atk and Sp. Atk stats.
#-------------------------------
[HAPPYHOUR]
Name = Happy Hour
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 30
Target = UserSide
FunctionCode = DoubleMoneyGainedFromBattle
Description = Using Happy Hour doubles the amount of prize money received after battle.
#-------------------------------
[HARDEN]
Name = Harden
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 30
Target = User
FunctionCode = RaiseUserDefense1
Description = The user stiffens all the muscles in its body to raise its Defense stat.
#-------------------------------
[HEALBELL]
Name = Heal Bell
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 5
Target = UserAndAllies
FunctionCode = CureUserPartyStatus
Flags = Sound
Description = The user makes a soothing bell chime to heal the status problems of all the party Pokémon.
#-------------------------------
[HELPINGHAND]
Name = Helping Hand
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 20
Target = NearAlly
Priority = 5
FunctionCode = PowerUpAllyMove
Description = The user assists an ally by boosting the power of its attack.
#-------------------------------
[HOLDHANDS]
Name = Hold Hands
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 40
Target = NearAlly
FunctionCode = DoesNothingFailsIfNoAlly
Description = The user and an ally hold hands. This makes them very happy.
#-------------------------------
[HOWL]
Name = Howl
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 40
Target = UserAndAllies
FunctionCode = RaiseTargetAttack1
Flags = Sound
Description = The user howls loudly to raise the spirit of itself and allies, boosting their Attack stats.
#-------------------------------
[LASERFOCUS]
Name = Laser Focus
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 30
Target = User
FunctionCode = EnsureNextCriticalHit
Description = The user focuses intensely. The attack on the next turn always results in a critical hit.
#-------------------------------
[LEER]
Name = Leer
Type = NORMAL
Category = Status
Accuracy = 100
TotalPP = 30
Target = AllNearFoes
FunctionCode = LowerTargetDefense1
Flags = CanProtect,CanMirrorMove
Description = The user gains an intimidating leer with sharp eyes. The target's Defense stat is reduced.
#-------------------------------
[LOCKON]
Name = Lock-On
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 5
Target = NearOther
FunctionCode = EnsureNextMoveAlwaysHits
Flags = CanProtect,CanMirrorMove
Description = The user takes sure aim at the target. It ensures the next attack does not fail to hit the target.
#-------------------------------
[LOVELYKISS]
Name = Lovely Kiss
Type = NORMAL
Category = Status
Accuracy = 75
TotalPP = 10
Target = NearOther
FunctionCode = SleepTarget
Flags = CanProtect,CanMirrorMove
Description = With a scary face, the user tries to force a kiss on the target. If it suceeds, the target falls asleep.
#-------------------------------
[LUCKYCHANT]
Name = Lucky Chant
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 30
Target = UserSide
FunctionCode = StartPreventCriticalHitsAgainstUserSide
Description = The user chants an incantation toward the sky, preventing the foe from landing critical hits.
#-------------------------------
[MEFIRST]
Name = Me First
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 20
Target = NearFoe
FunctionCode = UseMoveTargetIsAboutToUse
Flags = CanProtect
Description = The user tries to cut ahead of the foe to steal and use the foe's intended move with greater power.
#-------------------------------
[MEANLOOK]
Name = Mean Look
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 5
Target = NearOther
FunctionCode = TrapTargetInBattle
Flags = CanMirrorMove
Description = The user pins the target with a dark, arresting look. The target becomes unable to flee.
#-------------------------------
[METRONOME]
Name = Metronome
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = UseRandomMove
Description = The user waggles a finger and stimulates its brain into randomly using nearly any move.
#-------------------------------
[MILKDRINK]
Name = Milk Drink
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = HealUserHalfOfTotalHP
Description = The user restores its own HP by up to half of its maximum HP. May also be used in the field to heal HP.
#-------------------------------
[MIMIC]
Name = Mimic
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = NearOther
FunctionCode = ReplaceMoveThisBattleWithTargetLastMoveUsed
Flags = CanProtect
Description = The user copies the move last used by the foe. The move can be used until the user is switched out.
#-------------------------------
[MINDREADER]
Name = Mind Reader
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 5
Target = NearOther
FunctionCode = EnsureNextMoveAlwaysHits
Flags = CanProtect,CanMirrorMove
Description = The user senses the foe's movements with its mind to ensure its next attack does not miss the foe.
#-------------------------------
[MINIMIZE]
Name = Minimize
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = RaiseUserEvasion2MinimizeUser
Description = The user compresses its body to make itself look smaller, which sharply raises its evasiveness.
#-------------------------------
[MORNINGSUN]
Name = Morning Sun
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 5
Target = User
FunctionCode = HealUserDependingOnWeather
Description = The user restores its own HP. The amount of HP regained varies with the weather.
#-------------------------------
[NATUREPOWER]
Name = Nature Power
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 20
Target = NearOther
FunctionCode = UseMoveDependingOnEnvironment
Description = An attack that makes use of nature's power. Its effects vary depending on the user's environment.
#-------------------------------
[NOBLEROAR]
Name = Noble Roar
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 30
Target = NearOther
FunctionCode = LowerTargetAtkSpAtk1
Flags = CanProtect,CanMirrorMove,Sound
Description = Letting out a noble roar, the user intimidates the target and lowers its Attack and Sp. Atk.
#-------------------------------
[ODORSLEUTH]
Name = Odor Sleuth
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 40
Target = NearOther
FunctionCode = StartNegateTargetEvasionStatStageAndGhostImmunity
Flags = CanProtect,CanMirrorMove
Description = Enables the user to hit a Ghost type with any type of move. It also enables the user to hit an evasive foe.
#-------------------------------
[PAINSPLIT]
Name = Pain Split
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 20
Target = NearOther
FunctionCode = UserTargetAverageHP
Flags = CanProtect,CanMirrorMove
Description = The user adds its HP to the target's HP, then equally shares the combined HP with the target.
#-------------------------------
[PERISHSONG]
Name = Perish Song
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 5
Target = AllBattlers
FunctionCode = StartPerishCountsForAllBattlers
Flags = Sound
Description = Any Pokémon that hears this song faints in three turns, unless it switches out of battle.
#-------------------------------
[PLAYNICE]
Name = Play Nice
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 20
Target = NearOther
FunctionCode = LowerTargetAttack1BypassSubstitute
Flags = CanMirrorMove
Description = The user and target become friends. The target loses its will to fight, lowering its Attack stat.
#-------------------------------
[PROTECT]
Name = Protect
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
Priority = 4
FunctionCode = ProtectUser
Description = It enables the user to evade all attacks. Its chance of failing rises if it is used in succession.
#-------------------------------
[PSYCHUP]
Name = Psych Up
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = NearOther
FunctionCode = UserCopyTargetStatStages
Description = The user hypnotizes itself into copying any stat change made by the target.
#-------------------------------
[RECOVER]
Name = Recover
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = HealUserHalfOfTotalHP
Description = Restoring its own cells, the user restores its own HP by half of its max HP.
#-------------------------------
[RECYCLE]
Name = Recycle
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = RestoreUserConsumedItem
Description = The user recycles a held item that has been used in battle so it can be used again.
#-------------------------------
[REFLECTTYPE]
Name = Reflect Type
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 15
Target = NearOther
FunctionCode = SetUserTypesToTargetTypes
Flags = CanProtect
Description = The user reflects the target's type, making it the same type as the target.
#-------------------------------
[REFRESH]
Name = Refresh
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = CureUserBurnPoisonParalysis
Description = The user rests to cure itself of a poisoning, burn, or paralysis.
#-------------------------------
[ROAR]
Name = Roar
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 20
Target = NearOther
Priority = -6
FunctionCode = SwitchOutTargetStatusMove
Flags = CanMirrorMove,Sound
Description = The target is scared off and replaced by another Pokémon in its party. In the wild, the battle ends.
#-------------------------------
[SAFEGUARD]
Name = Safeguard
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 25
Target = UserSide
FunctionCode = StartUserSideImmunityToInflictedStatus
Description = The user creates a protective field that prevents status problems for five turns.
#-------------------------------
[SCARYFACE]
Name = Scary Face
Type = NORMAL
Category = Status
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetSpeed2
Flags = CanProtect,CanMirrorMove
Description = The user frightens the target with a scary face to harshly reduce its Speed stat.
#-------------------------------
[SCREECH]
Name = Screech
Type = NORMAL
Category = Status
Accuracy = 85
TotalPP = 40
Target = NearOther
FunctionCode = LowerTargetDefense2
Flags = CanProtect,CanMirrorMove,Sound
Description = An earsplitting screech harshly reduces the target's Defense stat.
#-------------------------------
[SHARPEN]
Name = Sharpen
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 30
Target = User
FunctionCode = RaiseUserAttack1
Description = The user reduces its polygon count to make itself more jagged, raising the Attack stat.
#-------------------------------
[SHELLSMASH]
Name = Shell Smash
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 15
Target = User
FunctionCode = LowerUserDefSpDef1RaiseUserAtkSpAtkSpd2
Description = The user breaks its shell, lowering its defenses but sharply raising attacking and Speed stats.
#-------------------------------
[SIMPLEBEAM]
Name = Simple Beam
Type = NORMAL
Category = Status
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = SetTargetAbilityToSimple
Flags = CanProtect,CanMirrorMove
Description = The user's mysterious psychic wave changes the target's Ability to Simple.
#-------------------------------
[SING]
Name = Sing
Type = NORMAL
Category = Status
Accuracy = 55
TotalPP = 15
Target = NearOther
FunctionCode = SleepTarget
Flags = CanProtect,CanMirrorMove,Sound
Description = A soothing lullaby is sung in a calming voice that puts the target into a deep slumber.
#-------------------------------
[SKETCH]
Name = Sketch
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 1
Target = NearOther
FunctionCode = ReplaceMoveWithTargetLastMoveUsed
Description = It enables the user to permanently learn the move last used by the foe. Once used, Sketch disappears.
#-------------------------------
[SLACKOFF]
Name = Slack Off
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = HealUserHalfOfTotalHP
Description = The user slacks off, restoring its own HP by up to half of its maximum HP.
#-------------------------------
[SLEEPTALK]
Name = Sleep Talk
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = UseRandomUserMoveIfAsleep
Description = While it is asleep, the user randomly uses one of the moves it knows.
#-------------------------------
[SMOKESCREEN]
Name = Smokescreen
Type = NORMAL
Category = Status
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = LowerTargetAccuracy1
Flags = CanProtect,CanMirrorMove
Description = The user releases an obscuring cloud of smoke or ink. It reduces the target's accuracy.
#-------------------------------
[SOFTBOILED]
Name = Soft-Boiled
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = HealUserHalfOfTotalHP
Description = The user restores its own HP by up to half of its maximum HP. May also be used in the field to heal HP.
#-------------------------------
[SPLASH]
Name = Splash
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 40
Target = User
FunctionCode = DoesNothingUnusableInGravity
Description = The user just flops and splashes around to no effect at all...
#-------------------------------
[SPOTLIGHT]
Name = Spotlight
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 15
Target = NearOther
Priority = 3
FunctionCode = RedirectAllMovesToTarget
Flags = CanProtect
Description = The user shines a spotlight on the target so that only it will be attacked during the turn.
#-------------------------------
[STOCKPILE]
Name = Stockpile
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = UserAddStockpileRaiseDefSpDef1
Description = The user charges up power and raises both its Defense and Sp. Def. The move can be used three times.
#-------------------------------
[STUFFCHEEKS]
Name = Stuff Cheeks
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = UserConsumeBerryRaiseDefense2
Description = The user eats its held Berry, then sharply raises its Defense stat.
#-------------------------------
[SUBSTITUTE]
Name = Substitute
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = UserMakeSubstitute
Description = The user makes a copy of itself using some of its HP. The copy serves as the user's decoy.
#-------------------------------
[SUPERSONIC]
Name = Supersonic
Type = NORMAL
Category = Status
Accuracy = 55
TotalPP = 20
Target = NearOther
FunctionCode = ConfuseTarget
Flags = CanProtect,CanMirrorMove,Sound
Description = The user generates odd sound waves from its body. It may confuse the target.
#-------------------------------
[SWAGGER]
Name = Swagger
Type = NORMAL
Category = Status
Accuracy = 85
TotalPP = 15
Target = NearOther
FunctionCode = RaiseTargetAttack2ConfuseTarget
Flags = CanProtect,CanMirrorMove
Description = The user enrages and confuses the target. However, it also sharply raises the target's Attack stat.
#-------------------------------
[SWALLOW]
Name = Swallow
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = HealUserDependingOnUserStockpile
Description = The power stored using the move Stockpile is absorbed by the user to heal its HP.
#-------------------------------
[SWEETSCENT]
Name = Sweet Scent
Type = NORMAL
Category = Status
Accuracy = 100
TotalPP = 20
Target = AllNearFoes
FunctionCode = LowerTargetEvasion2
Flags = CanProtect,CanMirrorMove
Description = A sweet scent that lowers the foe's evasiveness. It also lures wild Pokémon if used in grass, etc.
#-------------------------------
[SWORDSDANCE]
Name = Swords Dance
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = RaiseUserAttack2
Flags = Dance
Description = A frenetic dance to uplift the fighting spirit. It sharply raises the user's Attack stat.
#-------------------------------
[TAILWHIP]
Name = Tail Whip
Type = NORMAL
Category = Status
Accuracy = 100
TotalPP = 30
Target = AllNearFoes
FunctionCode = LowerTargetDefense1
Flags = CanProtect,CanMirrorMove
Description = The user wags its tail cutely, making opposing Pokémon less wary and lowering their Defense stat.
#-------------------------------
[TEARFULLOOK]
Name = Tearful Look
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 20
Target = NearOther
FunctionCode = LowerTargetAtkSpAtk1
Flags = CanMirrorMove
Description = Gets teary eyed to make the target lose its will to fight. Lowers the target's Attack and Sp. Atk.
#-------------------------------
[TEATIME]
Name = Teatime
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = AllBattlers
FunctionCode = AllBattlersConsumeBerry
Description = The user has teatime with all the Pokémon in the battle. Each Pokémon eats its held Berry.
#-------------------------------
[TEETERDANCE]
Name = Teeter Dance
Type = NORMAL
Category = Status
Accuracy = 100
TotalPP = 20
Target = AllNearOthers
FunctionCode = ConfuseTarget
Flags = CanProtect,CanMirrorMove,Dance
Description = The user performs a wobbly dance that confuses the Pokémon around it.
#-------------------------------
[TICKLE]
Name = Tickle
Type = NORMAL
Category = Status
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = LowerTargetAtkDef1
Flags = CanProtect,CanMirrorMove
Description = The user tickles the target into laughing, reducing its Attack and Defense stats.
#-------------------------------
[TRANSFORM]
Name = Transform
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = NearOther
FunctionCode = TransformUserIntoTarget
Description = The user transforms into a copy of the target right down to having the same move set.
#-------------------------------
[WHIRLWIND]
Name = Whirlwind
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 20
Target = NearOther
Priority = -6
FunctionCode = SwitchOutTargetStatusMove
Flags = CanMirrorMove
Description = The foe is blown away, to be replaced by another Pokémon in its party. In the wild, the battle ends.
#-------------------------------
[WISH]
Name = Wish
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = HealUserPositionNextTurn
Description = One turn after this move is used, the target's HP is restored by half the user's maximum HP.
#-------------------------------
[WORKUP]
Name = Work Up
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 30
Target = User
FunctionCode = RaiseUserAtkSpAtk1
Description = The user is roused, and its Attack and Sp. Atk stats increase.
#-------------------------------
[YAWN]
Name = Yawn
Type = NORMAL
Category = Status
Accuracy = 0
TotalPP = 10
Target = NearOther
FunctionCode = SleepTargetNextTurn
Flags = CanProtect,CanMirrorMove
Description = The user lets loose a huge yawn that lulls the target into falling asleep on the next turn.
#-------------------------------
[BELCH]
Name = Belch
Type = POISON
Category = Special
Power = 120
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = FailsIfUserNotConsumedBerry
Flags = CanProtect
Description = The user lets out a damaging belch at the target. The user must eat a held Berry to use this move.
#-------------------------------
[GUNKSHOT]
Name = Gunk Shot
Type = POISON
Category = Physical
Power = 120
Accuracy = 80
TotalPP = 5
Target = NearOther
FunctionCode = PoisonTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 30
Description = The user shoots filthy garbage at the target to attack. It may also poison the target.
#-------------------------------
[SLUDGEWAVE]
Name = Sludge Wave
Type = POISON
Category = Special
Power = 95
Accuracy = 100
TotalPP = 10
Target = AllNearOthers
FunctionCode = PoisonTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = It swamps the area around the user with a giant sludge wave. It may also poison those hit.
#-------------------------------
[SHELLSIDEARM]
Name = Shell Side Arm
Type = POISON
Category = Special
Power = 90
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = CategoryDependsOnHigherDamagePoisonTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 20
Description = Inflicts physical or special damage, whichever will be more effective. May also poison the target.
#-------------------------------
[SLUDGEBOMB]
Name = Sludge Bomb
Type = POISON
Category = Special
Power = 90
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = PoisonTarget
Flags = CanProtect,CanMirrorMove,Bomb
EffectChance = 30
Description = Unsanitary sludge is hurled at the target. It may also poison the target.
#-------------------------------
[POISONJAB]
Name = Poison Jab
Type = POISON
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = PoisonTarget
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 30
Description = The target is stabbed with a tentacle or arm seeped with poison. It may also poison the target.
#-------------------------------
[CROSSPOISON]
Name = Cross Poison
Type = POISON
Category = Physical
Power = 70
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = PoisonTarget
Flags = Contact,CanProtect,CanMirrorMove,HighCriticalHitRate
EffectChance = 10
Description = A slashing attack with a poisonous blade that may also poison the foe. Critical hits land more easily.
#-------------------------------
[SLUDGE]
Name = Sludge
Type = POISON
Category = Special
Power = 65
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = PoisonTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 30
Description = Unsanitary sludge is hurled at the target. It may also poison the target.
#-------------------------------
[VENOSHOCK]
Name = Venoshock
Type = POISON
Category = Special
Power = 65
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = DoublePowerIfTargetPoisoned
Flags = CanProtect,CanMirrorMove
Description = The user drenches the foe in a special poisonous liquid. Its power doubles if the target is poisoned.
#-------------------------------
[CLEARSMOG]
Name = Clear Smog
Type = POISON
Category = Special
Power = 50
Accuracy = 0
TotalPP = 15
Target = NearOther
FunctionCode = ResetTargetStatStages
Flags = CanProtect,CanMirrorMove
Description = The user attacks by throwing a clump of special mud. All status changes are returned to normal.
#-------------------------------
[POISONFANG]
Name = Poison Fang
Type = POISON
Category = Physical
Power = 50
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = BadPoisonTarget
Flags = Contact,CanProtect,CanMirrorMove,Biting
EffectChance = 50
Description = The user bites the target with toxic fangs. It may also leave the target badly poisoned.
#-------------------------------
[POISONTAIL]
Name = Poison Tail
Type = POISON
Category = Physical
Power = 50
Accuracy = 100
TotalPP = 25
Target = NearOther
FunctionCode = PoisonTarget
Flags = Contact,CanProtect,CanMirrorMove,HighCriticalHitRate
EffectChance = 10
Description = The user hits the target with its tail. It may also poison the target. Critical hits land more easily.
#-------------------------------
[ACID]
Name = Acid
Type = POISON
Category = Special
Power = 40
Accuracy = 100
TotalPP = 30
Target = AllNearFoes
FunctionCode = LowerTargetSpDef1
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The foe is attacked with a spray of harsh acid. It may also lower the target's Sp. Def stat.
#-------------------------------
[ACIDSPRAY]
Name = Acid Spray
Type = POISON
Category = Special
Power = 40
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = LowerTargetSpDef2
Flags = CanProtect,CanMirrorMove,Bomb
EffectChance = 100
Description = The user spits fluid that works to melt the target. This harshly reduces the target's Sp. Def stat.
#-------------------------------
[SMOG]
Name = Smog
Type = POISON
Category = Special
Power = 30
Accuracy = 70
TotalPP = 20
Target = NearOther
FunctionCode = PoisonTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 40
Description = The target is attacked with a discharge of filthy gases. It may also poison the target.
#-------------------------------
[POISONSTING]
Name = Poison Sting
Type = POISON
Category = Physical
Power = 15
Accuracy = 100
TotalPP = 35
Target = NearOther
FunctionCode = PoisonTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 30
Description = The user stabs the target with a poisonous stinger. This may also poison the target.
#-------------------------------
[ACIDARMOR]
Name = Acid Armor
Type = POISON
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = RaiseUserDefense2
Description = The user alters its cellular structure to liquefy itself, sharply raising its Defense stat.
#-------------------------------
[BANEFULBUNKER]
Name = Baneful Bunker
Type = POISON
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
Priority = 4
FunctionCode = ProtectUserBanefulBunker
Description = Protects the user from attacks. Also poisons any attacker that makes contact with the user.
#-------------------------------
[COIL]
Name = Coil
Type = POISON
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = RaiseUserAtkDefAcc1
Description = The user coils up and concentrates. This raises its Attack and Defense stats as well as its accuracy.
#-------------------------------
[CORROSIVEGAS]
Name = Corrosive Gas
Type = POISON
Category = Status
Accuracy = 100
TotalPP = 40
Target = AllNearOthers
FunctionCode = CorrodeTargetItem
Flags = CanProtect,CanMirrorMove
Description = The user surrounds everything around it with highly acidic gas and melts away items they hold.
#-------------------------------
[GASTROACID]
Name = Gastro Acid
Type = POISON
Category = Status
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = NegateTargetAbility
Flags = CanProtect,CanMirrorMove
Description = The user hurls up its stomach acids on the foe. The fluid negates the effect of the target's Ability.
#-------------------------------
[POISONGAS]
Name = Poison Gas
Type = POISON
Category = Status
Accuracy = 90
TotalPP = 40
Target = AllNearFoes
FunctionCode = PoisonTarget
Flags = CanProtect,CanMirrorMove
Description = A cloud of poison gas is sprayed in the face of opposing Pokémon. It may poison those hit.
#-------------------------------
[POISONPOWDER]
Name = Poison Powder
Type = POISON
Category = Status
Accuracy = 75
TotalPP = 35
Target = NearOther
FunctionCode = PoisonTarget
Flags = CanProtect,CanMirrorMove,Powder
Description = The user scatters a cloud of poisonous dust on the target. It may poison the target.
#-------------------------------
[PURIFY]
Name = Purify
Type = POISON
Category = Status
Accuracy = 0
TotalPP = 20
Target = NearOther
FunctionCode = CureTargetStatusHealUserHalfOfTotalHP
Flags = CanProtect,CanMirrorMove
Description = The user heals the target's status condition. If so, it also restores the user's own HP.
#-------------------------------
[TOXIC]
Name = Toxic
Type = POISON
Category = Status
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = BadPoisonTarget
Flags = CanProtect,CanMirrorMove
Description = A move that leaves the target badly poisoned. Its poison damage worsens every turn.
#-------------------------------
[TOXICSPIKES]
Name = Toxic Spikes
Type = POISON
Category = Status
Accuracy = 0
TotalPP = 20
Target = FoeSide
FunctionCode = AddToxicSpikesToFoeSide
Description = The user lays a trap of poison spikes at the foe's feet. They poison foes that switch into battle.
#-------------------------------
[TOXICTHREAD]
Name = Toxic Thread
Type = POISON
Category = Status
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = PoisonTargetLowerTargetSpeed1
Flags = CanProtect,CanMirrorMove
Description = The user shoots poisonous threads to poison the target and lower the target's Speed stat.
#-------------------------------
[VENOMDRENCH]
Name = Venom Drench
Type = POISON
Category = Status
Accuracy = 100
TotalPP = 20
Target = AllNearFoes
FunctionCode = LowerPoisonedTargetAtkSpAtkSpd1
Flags = CanProtect
Description = Foes are drenched in an odd liquid that lowers the Attack, Sp. Atk, and Speed of poisoned Pokémon.
#-------------------------------
[PRISMATICLASER]
Name = Prismatic Laser
Type = PSYCHIC
Category = Special
Power = 160
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = AttackAndSkipNextTurn
Flags = CanProtect,CanMirrorMove
Description = The user shoots powerful lasers using the power of a prism. The user can't move on the next turn.
#-------------------------------
[PSYCHOBOOST]
Name = Psycho Boost
Type = PSYCHIC
Category = Special
Power = 140
Accuracy = 90
TotalPP = 5
Target = NearOther
FunctionCode = LowerUserSpAtk2
Flags = CanProtect,CanMirrorMove
Description = The user attacks the target at full power. The attack's recoil harshly reduces the user's Sp. Atk stat.
#-------------------------------
[FUTURESIGHT]
Name = Future Sight
Type = PSYCHIC
Category = Special
Power = 120
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = AttackTwoTurnsLater
Description = Two turns after this move is used, a hunk of psychic energy attacks the target.
#-------------------------------
[SYNCHRONOISE]
Name = Synchronoise
Type = PSYCHIC
Category = Special
Power = 120
Accuracy = 100
TotalPP = 10
Target = AllNearOthers
FunctionCode = FailsUnlessTargetSharesTypeWithUser
Flags = CanProtect,CanMirrorMove
Description = Using an odd shock wave, the user damages any Pokémon of the same type as the user.
#-------------------------------
[DREAMEATER]
Name = Dream Eater
Type = PSYCHIC
Category = Special
Power = 100
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = HealUserByHalfOfDamageDoneIfTargetAsleep
Flags = CanProtect,CanMirrorMove
Description = The user eats the dreams of a sleeping foe. It absorbs half the damage caused to heal the user's HP.
#-------------------------------
[PHOTONGEYSER]
Name = Photon Geyser
Type = PSYCHIC
Category = Special
Power = 100
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = CategoryDependsOnHigherDamageIgnoreTargetAbility
Flags = CanProtect,CanMirrorMove,CannotMetronome
Description = The user attacks with a pillar of light. This move the higher of the user's Attack or Sp. Atk stat.
#-------------------------------
[PSYSTRIKE]
Name = Psystrike
Type = PSYCHIC
Category = Special
Power = 100
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = UseTargetDefenseInsteadOfTargetSpDef
Flags = CanProtect,CanMirrorMove
Description = The user materializes an odd psychic wave to attack the target. This attack does physical damage.
#-------------------------------
[FREEZINGGLARE]
Name = Freezing Glare
Type = PSYCHIC
Category = Special
Power = 90
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = FreezeTarget
Flags = CanProtect,CanMirrorMove,CannotMetronome
EffectChance = 10
Description = The user shoots its psychic power from its eyes to attack. This may also leave the target frozen.
#-------------------------------
[PSYCHIC]
Name = Psychic
Type = PSYCHIC
Category = Special
Power = 90
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetSpDef1
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The target is hit by a strong telekinetic force. It may also reduce the target's Sp. Def stat.
#-------------------------------
[PSYCHICFANGS]
Name = Psychic Fangs
Type = PSYCHIC
Category = Physical
Power = 85
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = RemoveScreens
Flags = Contact,CanProtect,CanMirrorMove,Biting
Description = The user bites the target using psychic capabilities. This can also destroy Light Screen and Reflect.
#-------------------------------
[EERIESPELL]
Name = Eerie Spell
Type = PSYCHIC
Category = Special
Power = 80
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = LowerPPOfTargetLastMoveBy3
Flags = CanProtect,CanMirrorMove,Sound
EffectChance = 100
Description = The user attacks with tremendous psychic power. This also removes 3 PP from the target's last move.
#-------------------------------
[EXPANDINGFORCE]
Name = Expanding Force
Type = PSYCHIC
Category = Special
Power = 80
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = HitsAllFoesAndPowersUpInPsychicTerrain
Flags = CanProtect,CanMirrorMove
Description = The user attacks the target with psychic power. Powers up and hits all foes on Psychic Terrain.
#-------------------------------
[EXTRASENSORY]
Name = Extrasensory
Type = PSYCHIC
Category = Special
Power = 80
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = FlinchTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The user attacks with an odd, unseeable power. It may also make the target flinch.
#-------------------------------
[HYPERSPACEHOLE]
Name = Hyperspace Hole
Type = PSYCHIC
Category = Special
Power = 80
Accuracy = 0
TotalPP = 5
Target = NearOther
FunctionCode = RemoveProtectionsBypassSubstitute
Flags = CanMirrorMove,CannotMetronome
Description = Using a hyperspace hole, the user appears right next to the target and strikes. Skips protections.
#-------------------------------
[PSYSHOCK]
Name = Psyshock
Type = PSYCHIC
Category = Special
Power = 80
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = UseTargetDefenseInsteadOfTargetSpDef
Flags = CanProtect,CanMirrorMove
Description = The user materializes an odd psychic wave to attack the target. This attack does physical damage.
#-------------------------------
[ZENHEADBUTT]
Name = Zen Headbutt
Type = PSYCHIC
Category = Physical
Power = 80
Accuracy = 90
TotalPP = 15
Target = NearOther
FunctionCode = FlinchTarget
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 20
Description = The user focuses its willpower to its head and attacks the foe. It may also make the target flinch.
#-------------------------------
[LUSTERPURGE]
Name = Luster Purge
Type = PSYCHIC
Category = Special
Power = 70
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = LowerTargetSpDef1
Flags = CanProtect,CanMirrorMove
EffectChance = 50
Description = The user lets loose a damaging burst of light. It may also reduce the target's Sp. Def stat.
#-------------------------------
[MISTBALL]
Name = Mist Ball
Type = PSYCHIC
Category = Special
Power = 70
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = LowerTargetSpAtk1
Flags = CanProtect,CanMirrorMove,Bomb
EffectChance = 50
Description = A mistlike flurry of down envelops and damages the target. It may also lower the target's Sp. Atk.
#-------------------------------
[PSYCHOCUT]
Name = Psycho Cut
Type = PSYCHIC
Category = Physical
Power = 70
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = None
Flags = CanProtect,CanMirrorMove,HighCriticalHitRate
Description = The user tears at the target with blades formed by psychic power. Critical hits land more easily.
#-------------------------------
[PSYBEAM]
Name = Psybeam
Type = PSYCHIC
Category = Special
Power = 65
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = ConfuseTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The target is attacked with a peculiar ray. It may also cause confusion.
#-------------------------------
[HEARTSTAMP]
Name = Heart Stamp
Type = PSYCHIC
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 25
Target = NearOther
FunctionCode = FlinchTarget
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 30
Description = The user unleashes a vicious blow after its cute act makes the foe less wary. It may also cause flinching.
#-------------------------------
[CONFUSION]
Name = Confusion
Type = PSYCHIC
Category = Special
Power = 50
Accuracy = 100
TotalPP = 25
Target = NearOther
FunctionCode = ConfuseTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The target is hit by a weak telekinetic force. It may also leave the target confused.
#-------------------------------
[STOREDPOWER]
Name = Stored Power
Type = PSYCHIC
Category = Special
Power = 20
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = PowerHigherWithUserPositiveStatStages
Flags = CanProtect,CanMirrorMove
Description = The user attacks with stored power. The more the user's stats are raised, the greater the damage.
#-------------------------------
[MIRRORCOAT]
Name = Mirror Coat
Type = PSYCHIC
Category = Special
Power = 1
Accuracy = 100
TotalPP = 20
Target = None
Priority = -5
FunctionCode = CounterSpecialDamage
Flags = CanProtect
Description = A retaliation move that counters any special attack, inflicting double the damage taken.
#-------------------------------
[PSYWAVE]
Name = Psywave
Type = PSYCHIC
Category = Special
Power = 1
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = FixedDamageUserLevelRandom
Flags = CanProtect,CanMirrorMove
Description = The target is attacked with an odd psychic wave. The attack varies in intensity.
#-------------------------------
[AGILITY]
Name = Agility
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 30
Target = User
FunctionCode = RaiseUserSpeed2
Description = The user relaxes and lightens its body to move faster. It sharply boosts the Speed stat.
#-------------------------------
[ALLYSWITCH]
Name = Ally Switch
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 15
Target = User
Priority = 2
FunctionCode = UserSwapsPositionsWithAlly
Description = The user teleports using a strange power and switches its place with one of its allies.
#-------------------------------
[AMNESIA]
Name = Amnesia
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = RaiseUserSpDef2
Description = The user temporarily empties its mind to forget its concerns. It sharply raises the user's Sp. Def stat.
#-------------------------------
[BARRIER]
Name = Barrier
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = RaiseUserDefense2
Description = The user throws up a sturdy wall that sharply raises its Defense stat.
#-------------------------------
[CALMMIND]
Name = Calm Mind
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = RaiseUserSpAtkSpDef1
Description = The user quietly focuses its mind and calms its spirit to raise its Sp. Atk and Sp. Def stats.
#-------------------------------
[COSMICPOWER]
Name = Cosmic Power
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = RaiseUserDefSpDef1
Description = The user absorbs a mystical power from space to raise its Defense and Sp. Def stats.
#-------------------------------
[GRAVITY]
Name = Gravity
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 5
Target = BothSides
FunctionCode = StartGravity
Description = Gravity is intensified for five turns, making moves involving flying unusable and negating Levitation.
#-------------------------------
[GUARDSPLIT]
Name = Guard Split
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 10
Target = NearOther
FunctionCode = UserTargetAverageBaseDefSpDef
Flags = CanProtect
Description = The user employs its psychic power to average its Defense and Sp. Def stats with those of its target.
#-------------------------------
[GUARDSWAP]
Name = Guard Swap
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 10
Target = NearOther
FunctionCode = UserTargetSwapDefSpDefStages
Flags = CanProtect,CanMirrorMove
Description = The user employs its psychic power to switch changes to its Defense and Sp. Def with the target.
#-------------------------------
[HEALBLOCK]
Name = Heal Block
Type = PSYCHIC
Category = Status
Accuracy = 100
TotalPP = 15
Target = AllNearFoes
FunctionCode = DisableTargetHealingMoves
Flags = CanProtect,CanMirrorMove
Description = For five turns, the foe is prevented from using any moves, Abilities, or held items that recover HP.
#-------------------------------
[HEALPULSE]
Name = Heal Pulse
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 10
Target = Other
FunctionCode = HealTargetHalfOfTotalHP
Flags = CanProtect,Pulse
Description = The user emits a healing pulse which restores the target's HP by up to half of its max HP.
#-------------------------------
[HEALINGWISH]
Name = Healing Wish
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = UserFaintsHealAndCureReplacement
Description = The user faints. In return, the Pokémon taking its place will have its HP restored and status cured.
#-------------------------------
[HEARTSWAP]
Name = Heart Swap
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 10
Target = NearOther
FunctionCode = UserTargetSwapStatStages
Flags = CanProtect,CanMirrorMove
Description = The user employs its psychic power to switch stat changes with the target.
#-------------------------------
[HYPNOSIS]
Name = Hypnosis
Type = PSYCHIC
Category = Status
Accuracy = 60
TotalPP = 20
Target = NearOther
FunctionCode = SleepTarget
Flags = CanProtect,CanMirrorMove
Description = The user employs hypnotic suggestion to make the target fall into a deep sleep.
#-------------------------------
[IMPRISON]
Name = Imprison
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = DisableTargetMovesKnownByUser
Description = If the foe knows any move also known by the user, the foe is prevented from using it.
#-------------------------------
[INSTRUCT]
Name = Instruct
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 15
Target = NearOther
FunctionCode = TargetUsesItsLastUsedMoveAgain
Flags = CanProtect
Description = The user instructs the target to use the target's last move again.
#-------------------------------
[KINESIS]
Name = Kinesis
Type = PSYCHIC
Category = Status
Accuracy = 80
TotalPP = 15
Target = NearOther
FunctionCode = LowerTargetAccuracy1
Flags = CanProtect,CanMirrorMove
Description = The user distracts the target by bending a spoon. It lowers the target's accuracy.
#-------------------------------
[LIGHTSCREEN]
Name = Light Screen
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 30
Target = UserSide
FunctionCode = StartWeakenSpecialDamageAgainstUserSide
Description = A wondrous wall of light is put up to suppress damage from special attacks for five turns.
#-------------------------------
[LUNARDANCE]
Name = Lunar Dance
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = UserFaintsHealAndCureReplacementRestorePP
Flags = Dance
Description = The user faints. In return, the Pokémon taking its place will have its status and HP fully restored.
#-------------------------------
[MAGICCOAT]
Name = Magic Coat
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 15
Target = User
Priority = 4
FunctionCode = BounceBackProblemCausingStatusMoves
Description = A barrier reflects back to the target moves like Leech Seed and moves that damage status.
#-------------------------------
[MAGICPOWDER]
Name = Magic Powder
Type = PSYCHIC
Category = Status
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = SetTargetTypesToPsychic
Flags = CanProtect,CanMirrorMove,Powder
Description = The user scatters a cloud of magic powder that changes the target to Psychic type.
#-------------------------------
[MAGICROOM]
Name = Magic Room
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 10
Target = BothSides
FunctionCode = StartNegateHeldItems
Flags = CanMirrorMove
Description = The user creates a bizarre area in which Pokémon's held items lose their effects for five turns.
#-------------------------------
[MEDITATE]
Name = Meditate
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 40
Target = User
FunctionCode = RaiseUserAttack1
Description = The user meditates to awaken the power deep within its body and raise its Attack stat.
#-------------------------------
[MIRACLEEYE]
Name = Miracle Eye
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 40
Target = NearOther
FunctionCode = StartNegateTargetEvasionStatStageAndDarkImmunity
Flags = CanProtect,CanMirrorMove
Description = Enables the user to hit a Dark type with any type of move. It also enables the user to hit an evasive foe.
#-------------------------------
[POWERSPLIT]
Name = Power Split
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 10
Target = NearOther
FunctionCode = UserTargetAverageBaseAtkSpAtk
Flags = CanProtect
Description = The user employs its psychic power to average its Attack and Sp. Atk stats with those of the target.
#-------------------------------
[POWERSWAP]
Name = Power Swap
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 10
Target = NearOther
FunctionCode = UserTargetSwapAtkSpAtkStages
Flags = CanProtect,CanMirrorMove
Description = The user employs its psychic power to switch changes to its Attack and Sp. Atk with the target.
#-------------------------------
[POWERTRICK]
Name = Power Trick
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = UserSwapBaseAtkDef
Description = The user employs its psychic power to switch its Attack with its Defense stat.
#-------------------------------
[PSYCHICTERRAIN]
Name = Psychic Terrain
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 10
Target = BothSides
FunctionCode = StartPsychicTerrain
Description = Protects grounded Pokémon from priority moves and powers up Psychic-type moves for five turns.
#-------------------------------
[PSYCHOSHIFT]
Name = Psycho Shift
Type = PSYCHIC
Category = Status
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = GiveUserStatusToTarget
Flags = CanProtect,CanMirrorMove
Description = Using its psychic power of suggestion, the user transfers its status problems to the target.
#-------------------------------
[REFLECT]
Name = Reflect
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 20
Target = UserSide
FunctionCode = StartWeakenPhysicalDamageAgainstUserSide
Description = A wondrous wall of light is put up to suppress damage from physical attacks for five turns.
#-------------------------------
[REST]
Name = Rest
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = HealUserFullyAndFallAsleep
Description = The user goes to sleep for two turns. It fully restores the user's HP and heals any status problem.
#-------------------------------
[ROLEPLAY]
Name = Role Play
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 10
Target = NearOther
FunctionCode = SetUserAbilityToTargetAbility
Description = The user mimics the target completely, copying the target's natural Ability.
#-------------------------------
[SKILLSWAP]
Name = Skill Swap
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 10
Target = NearOther
FunctionCode = UserTargetSwapAbilities
Flags = CanProtect,CanMirrorMove
Description = The user employs its psychic power to exchange Abilities with the target.
#-------------------------------
[SPEEDSWAP]
Name = Speed Swap
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 10
Target = NearOther
FunctionCode = UserTargetSwapBaseSpeed
Flags = CanProtect,CanMirrorMove
Description = The user exchanges Speed stats with the target.
#-------------------------------
[TELEKINESIS]
Name = Telekinesis
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 15
Target = NearOther
FunctionCode = StartTargetAirborneAndAlwaysHitByMoves
Flags = CanProtect,CanMirrorMove
Description = The user makes the target float with its psychic power. The target is easier to hit for three turns.
#-------------------------------
[TELEPORT]
Name = Teleport
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
Priority = -6
FunctionCode = SwitchOutUserStatusMove
Description = The user switches place with a party Pokémon. Also warps to the last Pokémon Center visited.
#-------------------------------
[TRICK]
Name = Trick
Type = PSYCHIC
Category = Status
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = UserTargetSwapItems
Flags = CanProtect,CanMirrorMove
Description = The user catches the target off guard and swaps its held item with its own.
#-------------------------------
[TRICKROOM]
Name = Trick Room
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 5
Target = BothSides
Priority = -7
FunctionCode = StartSlowerBattlersActFirst
Flags = CanMirrorMove
Description = The user creates a bizarre area in which slower Pokémon get to move first for five turns.
#-------------------------------
[WONDERROOM]
Name = Wonder Room
Type = PSYCHIC
Category = Status
Accuracy = 0
TotalPP = 10
Target = BothSides
Priority = -7
FunctionCode = StartSwapAllBattlersBaseDefensiveStats
Flags = CanMirrorMove
Description = The user creates a bizarre area in which Pokémon's Defense and Sp. Def stats are swapped for 5 turns.
#-------------------------------
[HEADSMASH]
Name = Head Smash
Type = ROCK
Category = Physical
Power = 150
Accuracy = 80
TotalPP = 5
Target = NearOther
FunctionCode = RecoilHalfOfDamageDealt
Flags = Contact,CanProtect,CanMirrorMove
Description = The user attacks the foe with a hazardous, full-power headbutt. The user also takes terrible damage.
#-------------------------------
[ROCKWRECKER]
Name = Rock Wrecker
Type = ROCK
Category = Physical
Power = 150
Accuracy = 90
TotalPP = 5
Target = NearOther
FunctionCode = AttackAndSkipNextTurn
Flags = CanProtect,CanMirrorMove,Bomb
Description = The user launches a huge boulder at the target to attack. It must rest on the next turn, however.
#-------------------------------
[METEORBEAM]
Name = Meteor Beam
Type = ROCK
Category = Special
Power = 120
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = TwoTurnAttackChargeRaiseUserSpAtk1
Flags = CanProtect,CanMirrorMove
Description = Gathers space power and boosts its Sp. Atk stat on the first turn, attacks on the next turn.
#-------------------------------
[DIAMONDSTORM]
Name = Diamond Storm
Type = ROCK
Category = Physical
Power = 100
Accuracy = 95
TotalPP = 5
Target = AllNearFoes
FunctionCode = RaiseUserDefense2
Flags = CanProtect,CanMirrorMove,CannotMetronome
EffectChance = 50
Description = The user whips up a storm of diamonds to damage foes. This may also sharply raise the user's Defense stat.
#-------------------------------
[STONEEDGE]
Name = Stone Edge
Type = ROCK
Category = Physical
Power = 100
Accuracy = 80
TotalPP = 5
Target = NearOther
FunctionCode = None
Flags = CanProtect,CanMirrorMove,HighCriticalHitRate
Description = The user stabs the foe with sharpened stones from below. It has a high critical-hit ratio.
#-------------------------------
[POWERGEM]
Name = Power Gem
Type = ROCK
Category = Special
Power = 80
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = None
Flags = CanProtect,CanMirrorMove
Description = The user attacks with a ray of light that sparkles as if it were made of gemstones.
#-------------------------------
[ROCKSLIDE]
Name = Rock Slide
Type = ROCK
Category = Physical
Power = 75
Accuracy = 90
TotalPP = 10
Target = AllNearFoes
FunctionCode = FlinchTarget
Flags = CanProtect,CanMirrorMove
EffectChance = 30
Description = Large boulders are hurled at the foes to inflict damage. It may also make the targets flinch.
#-------------------------------
[ANCIENTPOWER]
Name = Ancient Power
Type = ROCK
Category = Special
Power = 60
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = RaiseUserMainStats1
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The user attacks with a prehistoric power. It may also raise all the user's stats at once.
#-------------------------------
[ROCKTOMB]
Name = Rock Tomb
Type = ROCK
Category = Physical
Power = 60
Accuracy = 95
TotalPP = 15
Target = NearOther
FunctionCode = LowerTargetSpeed1
Flags = CanProtect,CanMirrorMove
EffectChance = 100
Description = Boulders are hurled at the target. It also lowers the target's Speed by preventing its movement.
#-------------------------------
[ROCKTHROW]
Name = Rock Throw
Type = ROCK
Category = Physical
Power = 50
Accuracy = 90
TotalPP = 15
Target = NearOther
FunctionCode = None
Flags = CanProtect,CanMirrorMove
Description = The user picks up and throws a small rock at the target to attack.
#-------------------------------
[SMACKDOWN]
Name = Smack Down
Type = ROCK
Category = Physical
Power = 50
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = HitsTargetInSkyGroundsTarget
Flags = CanProtect,CanMirrorMove
Description = The user throws a stone or projectile to attack. A flying Pokémon will fall to the ground when hit.
#-------------------------------
[ACCELEROCK]
Name = Accelerock
Type = ROCK
Category = Physical
Power = 40
Accuracy = 100
TotalPP = 20
Target = NearOther
Priority = 1
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The user smashes into the target at high speed. This move always goes first.
#-------------------------------
[ROLLOUT]
Name = Rollout
Type = ROCK
Category = Physical
Power = 30
Accuracy = 90
TotalPP = 20
Target = NearOther
FunctionCode = MultiTurnAttackPowersUpEachTurn
Flags = Contact,CanProtect,CanMirrorMove
Description = The user continually rolls into the target over five turns. It becomes stronger each time it hits.
#-------------------------------
[ROCKBLAST]
Name = Rock Blast
Type = ROCK
Category = Physical
Power = 25
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = HitTwoToFiveTimes
Flags = CanProtect,CanMirrorMove,Bomb
Description = The user hurls hard rocks at the target. Two to five rocks are launched in quick succession.
#-------------------------------
[ROCKPOLISH]
Name = Rock Polish
Type = ROCK
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = RaiseUserSpeed2
Description = The user polishes its body to reduce drag. It can sharply raise the Speed stat.
#-------------------------------
[SANDSTORM]
Name = Sandstorm
Type = ROCK
Category = Status
Accuracy = 0
TotalPP = 10
Target = BothSides
FunctionCode = StartSandstormWeather
Description = Summons a five-turn sandstorm to hurt all combatants except the Rock, Ground, and Steel types.
#-------------------------------
[STEALTHROCK]
Name = Stealth Rock
Type = ROCK
Category = Status
Accuracy = 0
TotalPP = 20
Target = FoeSide
FunctionCode = AddStealthRocksToFoeSide
Description = The user lays a trap of levitating stones around the foe. The trap hurts foes that switch into battle.
#-------------------------------
[TARSHOT]
Name = Tar Shot
Type = ROCK
Category = Status
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = LowerTargetSpeed1MakeTargetWeakerToFire
Flags = CanProtect,CanMirrorMove
Description = Pours sticky tar over the target, lowering its Speed and making it weaker to Fire-type moves.
#-------------------------------
[WIDEGUARD]
Name = Wide Guard
Type = ROCK
Category = Status
Accuracy = 0
TotalPP = 10
Target = UserSide
Priority = 3
FunctionCode = ProtectUserSideFromMultiTargetDamagingMoves
Description = The user and its allies are protected from wide-ranging attacks for a turn. May fail if used in succession.
#-------------------------------
[DOOMDESIRE]
Name = Doom Desire
Type = STEEL
Category = Special
Power = 140
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = AttackTwoTurnsLater
Description = Two turns after this move is used, the user blasts the target with a concentrated bundle of light.
#-------------------------------
[STEELBEAM]
Name = Steel Beam
Type = STEEL
Category = Special
Power = 140
Accuracy = 95
TotalPP = 5
Target = NearOther
FunctionCode = UserLosesHalfOfTotalHP
Flags = CanProtect,CanMirrorMove,CannotMetronome
Description = The user fires a beam of steel that it collected from its entire body. This also damages the user.
#-------------------------------
[STEELROLLER]
Name = Steel Roller
Type = STEEL
Category = Physical
Power = 130
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = RemoveTerrain
Flags = Contact,CanProtect,CanMirrorMove
Description = The user attacks while destroying the terrain. This move fails when the ground isn't a terrain.
#-------------------------------
[BEHEMOTHBASH]
Name = Behemoth Bash
Type = STEEL
Category = Physical
Power = 100
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove,CannotMetronome
Description = The user becomes a gigantic shield and slams into the target.
#-------------------------------
[BEHEMOTHBLADE]
Name = Behemoth Blade
Type = STEEL
Category = Physical
Power = 100
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove,CannotMetronome
Description = The user becomes a gigantic sword and cuts the target.
#-------------------------------
[IRONTAIL]
Name = Iron Tail
Type = STEEL
Category = Physical
Power = 100
Accuracy = 75
TotalPP = 15
Target = NearOther
FunctionCode = LowerTargetDefense1
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 30
Description = The target is slammed with a steel-hard tail. It may also lower the target's Defense stat.
#-------------------------------
[SUNSTEELSTRIKE]
Name = Sunsteel Strike
Type = STEEL
Category = Physical
Power = 100
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = IgnoreTargetAbility
Flags = Contact,CanProtect,CanMirrorMove,CannotMetronome
Description = The user slams into the target with the force of a meteor. Can't be stopped by the target's Ability.
#-------------------------------
[METEORMASH]
Name = Meteor Mash
Type = STEEL
Category = Physical
Power = 90
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = RaiseUserAttack1
Flags = Contact,CanProtect,CanMirrorMove,Punching
EffectChance = 20
Description = The target is hit with a hard punch fired like a meteor. It may also raise the user's Attack.
#-------------------------------
[ANCHORSHOT]
Name = Anchor Shot
Type = STEEL
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = TrapTargetInBattle
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 100
Description = The user entangles the target with its anchor chain. The target becomes unable to flee.
#-------------------------------
[FLASHCANNON]
Name = Flash Cannon
Type = STEEL
Category = Special
Power = 80
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetSpDef1
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = The user gathers all its light energy and releases it at once. It may also lower the target's Sp. Def stat.
#-------------------------------
[IRONHEAD]
Name = Iron Head
Type = STEEL
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = FlinchTarget
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 30
Description = The foe slams the target with its steel-hard head. It may also make the target flinch.
#-------------------------------
[SMARTSTRIKE]
Name = Smart Strike
Type = STEEL
Category = Physical
Power = 70
Accuracy = 0
TotalPP = 10
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The user stabs the target with a sharp horn. This attack never misses.
#-------------------------------
[STEELWING]
Name = Steel Wing
Type = STEEL
Category = Physical
Power = 70
Accuracy = 90
TotalPP = 25
Target = NearOther
FunctionCode = RaiseUserDefense1
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 10
Description = The target is hit with wings of steel. It may also raise the user's Defense stat.
#-------------------------------
[DOUBLEIRONBASH]
Name = Double Iron Bash
Type = STEEL
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = HitTwoTimesFlinchTarget
Flags = Contact,CanProtect,Punching,CannotMetronome
EffectChance = 30
Description = The user rotates, centering the hex nut in its chest, and then strikes twice. May cause flinching.
#-------------------------------
[MIRRORSHOT]
Name = Mirror Shot
Type = STEEL
Category = Special
Power = 65
Accuracy = 85
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetAccuracy1
Flags = CanProtect,CanMirrorMove
EffectChance = 30
Description = The user looses a flash of energy from its polished body. It may also lower the target's accuracy.
#-------------------------------
[MAGNETBOMB]
Name = Magnet Bomb
Type = STEEL
Category = Physical
Power = 60
Accuracy = 0
TotalPP = 20
Target = NearOther
FunctionCode = None
Flags = CanProtect,CanMirrorMove,Bomb
Description = The user launches steel bombs that stick to the target. This attack will not miss.
#-------------------------------
[GEARGRIND]
Name = Gear Grind
Type = STEEL
Category = Physical
Power = 50
Accuracy = 85
TotalPP = 15
Target = NearOther
FunctionCode = HitTwoTimes
Flags = Contact,CanProtect,CanMirrorMove
Description = The user attacks by throwing two steel gears at its target.
#-------------------------------
[METALCLAW]
Name = Metal Claw
Type = STEEL
Category = Physical
Power = 50
Accuracy = 95
TotalPP = 35
Target = NearOther
FunctionCode = RaiseUserAttack1
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 10
Description = The target is raked with steel claws. It may also raise the user's Attack stat.
#-------------------------------
[BULLETPUNCH]
Name = Bullet Punch
Type = STEEL
Category = Physical
Power = 40
Accuracy = 100
TotalPP = 30
Target = NearOther
Priority = 1
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove,Punching
Description = The user strikes the target with tough punches as fast as bullets. This move always goes first.
#-------------------------------
[GYROBALL]
Name = Gyro Ball
Type = STEEL
Category = Physical
Power = 1
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = PowerHigherWithTargetFasterThanUser
Flags = Contact,CanProtect,CanMirrorMove,Bomb
Description = The user tackles the target with a high-speed spin. The slower the user, the greater the damage.
#-------------------------------
[HEAVYSLAM]
Name = Heavy Slam
Type = STEEL
Category = Physical
Power = 1
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = PowerHigherWithUserHeavierThanTarget
Flags = Contact,CanProtect,CanMirrorMove,TramplesMinimize
Description = The user slams into the foe with its heavy body. The heavier the user, the greater the damage.
#-------------------------------
[METALBURST]
Name = Metal Burst
Type = STEEL
Category = Physical
Power = 1
Accuracy = 100
TotalPP = 10
Target = None
FunctionCode = CounterDamagePlusHalf
Flags = CanProtect,CanMirrorMove
Description = The user retaliates with much greater power against the target that last inflicted damage on it.
#-------------------------------
[AUTOTOMIZE]
Name = Autotomize
Type = STEEL
Category = Status
Accuracy = 0
TotalPP = 15
Target = User
FunctionCode = RaiseUserSpeed2LowerUserWeight
Description = The user sheds part of its body to make itself lighter and sharply raise its Speed stat.
#-------------------------------
[GEARUP]
Name = Gear Up
Type = STEEL
Category = Status
Accuracy = 0
TotalPP = 20
Target = UserAndAllies
FunctionCode = RaisePlusMinusUserAndAlliesAtkSpAtk1
Description = The user engages its gears to raise the Attack and Sp. Atk of allies with the Plus or Minus Ability.
#-------------------------------
[IRONDEFENSE]
Name = Iron Defense
Type = STEEL
Category = Status
Accuracy = 0
TotalPP = 15
Target = User
FunctionCode = RaiseUserDefense2
Description = The user hardens its body's surface like iron, sharply raising its Defense stat.
#-------------------------------
[KINGSSHIELD]
Name = King's Shield
Type = STEEL
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
Priority = 4
FunctionCode = ProtectUserFromDamagingMovesKingsShield
Description = Protects itself from damage. It also harshly lowers the Attack of attackers that make contact.
#-------------------------------
[METALSOUND]
Name = Metal Sound
Type = STEEL
Category = Status
Accuracy = 85
TotalPP = 40
Target = NearOther
FunctionCode = LowerTargetSpDef2
Flags = CanProtect,CanMirrorMove,Sound
Description = A horrible sound like scraping metal harshly reduces the target's Sp. Def stat.
#-------------------------------
[SHIFTGEAR]
Name = Shift Gear
Type = STEEL
Category = Status
Accuracy = 0
TotalPP = 10
Target = User
FunctionCode = RaiseUserAtk1Spd2
Description = The user rotates its gears, raising its Attack and sharply raising its Speed.
#-------------------------------
[HYDROCANNON]
Name = Hydro Cannon
Type = WATER
Category = Special
Power = 150
Accuracy = 90
TotalPP = 5
Target = NearOther
FunctionCode = AttackAndSkipNextTurn
Flags = CanProtect,CanMirrorMove
Description = The target is hit with a watery blast. The user must rest on the next turn, however.
#-------------------------------
[WATERSPOUT]
Name = Water Spout
Type = WATER
Category = Special
Power = 150
Accuracy = 100
TotalPP = 5
Target = AllNearFoes
FunctionCode = PowerHigherWithUserHP
Flags = CanProtect,CanMirrorMove
Description = The user spouts water to damage the foe. The lower the user's HP, the less powerful it becomes.
#-------------------------------
[HYDROPUMP]
Name = Hydro Pump
Type = WATER
Category = Special
Power = 110
Accuracy = 80
TotalPP = 5
Target = NearOther
FunctionCode = None
Flags = CanProtect,CanMirrorMove
Description = The target is blasted by a huge volume of water launched under great pressure.
#-------------------------------
[ORIGINPULSE]
Name = Origin Pulse
Type = WATER
Category = Special
Power = 110
Accuracy = 85
TotalPP = 10
Target = AllNearFoes
FunctionCode = None
Flags = CanProtect,CanMirrorMove,Pulse,CannotMetronome
Description = The user attacks opposing Pokémon with countless beams of light that glow a deep and brilliant blue.
#-------------------------------
[STEAMERUPTION]
Name = Steam Eruption
Type = WATER
Category = Special
Power = 110
Accuracy = 95
TotalPP = 5
Target = NearOther
FunctionCode = BurnTarget
Flags = CanProtect,CanMirrorMove,ThawsUser,CannotMetronome
EffectChance = 30
Description = The user immerses the target in superheated steam. This may also leave the target with a burn.
#-------------------------------
[CRABHAMMER]
Name = Crabhammer
Type = WATER
Category = Physical
Power = 100
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove,HighCriticalHitRate
Description = The target is hammered with a large pincer. Critical hits land more easily.
#-------------------------------
[AQUATAIL]
Name = Aqua Tail
Type = WATER
Category = Physical
Power = 90
Accuracy = 90
TotalPP = 10
Target = NearOther
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The user attacks by swinging its tail as if it were a vicious wave in a raging storm.
#-------------------------------
[MUDDYWATER]
Name = Muddy Water
Type = WATER
Category = Special
Power = 90
Accuracy = 85
TotalPP = 10
Target = AllNearFoes
FunctionCode = LowerTargetAccuracy1
Flags = CanProtect,CanMirrorMove
EffectChance = 30
Description = The user attacks by shooting muddy water at the opposing team. It may also lower the target's accuracy.
#-------------------------------
[SPARKLINGARIA]
Name = Sparkling Aria
Type = WATER
Category = Special
Power = 90
Accuracy = 100
TotalPP = 10
Target = AllNearOthers
FunctionCode = CureTargetBurn
Flags = CanProtect,CanMirrorMove,Sound
EffectChance = 100
Description = The user bursts into song, emitting many bubbles. Any burnt Pokémon will be healed by these bubbles.
#-------------------------------
[SURF]
Name = Surf
Type = WATER
Category = Special
Power = 90
Accuracy = 100
TotalPP = 15
Target = AllNearOthers
FunctionCode = DoublePowerIfTargetUnderwater
Flags = CanProtect,CanMirrorMove
Description = It swamps the area around the user with a giant wave. It can also be used for crossing water.
#-------------------------------
[FISHIOUSREND]
Name = Fishious Rend
Type = WATER
Category = Physical
Power = 85
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = DoublePowerIfTargetNotActed
Flags = Contact,CanProtect,CanMirrorMove
Description = The user rends the target with its hard gills. Power doubles if the user moves first.
#-------------------------------
[LIQUIDATION]
Name = Liquidation
Type = WATER
Category = Physical
Power = 85
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetDefense1
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 20
Description = The user slams into the target using a full-force blast of water. May lower the target's Defense.
#-------------------------------
[DIVE]
Name = Dive
Type = WATER
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = TwoTurnAttackInvulnerableUnderwater
Flags = Contact,CanProtect,CanMirrorMove
Description = Diving on the first turn, the user rises and hits on the next turn. It can be used to dive in the ocean.
#-------------------------------
[SCALD]
Name = Scald
Type = WATER
Category = Special
Power = 80
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = BurnTarget
Flags = CanProtect,CanMirrorMove,ThawsUser
EffectChance = 30
Description = The user shoots boiling hot water at its target. It may also leave the target with a burn.
#-------------------------------
[SNIPESHOT]
Name = Snipe Shot
Type = WATER
Category = Special
Power = 80
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = CannotBeRedirected
Flags = CanProtect,CanMirrorMove,HighCriticalHitRate
Description = The user ignores any effects that redirect moves, allowing this move to hit the chosen target.
#-------------------------------
[WATERPLEDGE]
Name = Water Pledge
Type = WATER
Category = Special
Power = 80
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = WaterPledge
Flags = CanProtect,CanMirrorMove
Description = A column of water strikes the target. When combined with its fire equivalent, it makes a rainbow.
#-------------------------------
[WATERFALL]
Name = Waterfall
Type = WATER
Category = Physical
Power = 80
Accuracy = 100
TotalPP = 15
Target = NearOther
FunctionCode = FlinchTarget
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 20
Description = The user charges at the target and may make it flinch. It can also be used to climb a waterfall.
#-------------------------------
[RAZORSHELL]
Name = Razor Shell
Type = WATER
Category = Physical
Power = 75
Accuracy = 95
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetDefense1
Flags = Contact,CanProtect,CanMirrorMove
EffectChance = 50
Description = The user cuts the foe with sharp shells. It may also lower the target's Defense stat.
#-------------------------------
[BRINE]
Name = Brine
Type = WATER
Category = Special
Power = 65
Accuracy = 100
TotalPP = 10
Target = NearOther
FunctionCode = DoublePowerIfTargetHPLessThanHalf
Flags = CanProtect,CanMirrorMove
Description = If the target's HP is down to about half, this attack will hit with double the power.
#-------------------------------
[BUBBLEBEAM]
Name = Bubble Beam
Type = WATER
Category = Special
Power = 65
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = LowerTargetSpeed1
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = A spray of bubbles is forcefully ejected at the target. It may also lower its Speed stat.
#-------------------------------
[OCTAZOOKA]
Name = Octazooka
Type = WATER
Category = Special
Power = 65
Accuracy = 85
TotalPP = 10
Target = NearOther
FunctionCode = LowerTargetAccuracy1
Flags = CanProtect,CanMirrorMove,Bomb
EffectChance = 50
Description = The user attacks by spraying ink in the foe's face or eyes. It may also lower the target's accuracy.
#-------------------------------
[FLIPTURN]
Name = Flip Turn
Type = WATER
Category = Physical
Power = 60
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = SwitchOutUserDamagingMove
Flags = Contact,CanProtect,CanMirrorMove
Description = After making its attack, the user rushes back to switch places with a party Pokémon in waiting.
#-------------------------------
[WATERPULSE]
Name = Water Pulse
Type = WATER
Category = Special
Power = 60
Accuracy = 100
TotalPP = 20
Target = Other
FunctionCode = ConfuseTarget
Flags = CanProtect,CanMirrorMove,Pulse
EffectChance = 20
Description = The user attacks the target with a pulsing blast of water. It may also confuse the target.
#-------------------------------
[AQUAJET]
Name = Aqua Jet
Type = WATER
Category = Physical
Power = 40
Accuracy = 100
TotalPP = 20
Target = NearOther
Priority = 1
FunctionCode = None
Flags = Contact,CanProtect,CanMirrorMove
Description = The user lunges at the target at a speed that makes it almost invisible. It is sure to strike first.
#-------------------------------
[BUBBLE]
Name = Bubble
Type = WATER
Category = Special
Power = 40
Accuracy = 100
TotalPP = 30
Target = AllNearFoes
FunctionCode = LowerTargetSpeed1
Flags = CanProtect,CanMirrorMove
EffectChance = 10
Description = A spray of countless bubbles is jetted at the opposing team. It may also lower the targets' Speed stats.
#-------------------------------
[WATERGUN]
Name = Water Gun
Type = WATER
Category = Special
Power = 40
Accuracy = 100
TotalPP = 25
Target = NearOther
FunctionCode = None
Flags = CanProtect,CanMirrorMove
Description = The target is blasted with a forceful shot of water.
#-------------------------------
[CLAMP]
Name = Clamp
Type = WATER
Category = Physical
Power = 35
Accuracy = 85
TotalPP = 15
Target = NearOther
FunctionCode = BindTarget
Flags = Contact,CanProtect,CanMirrorMove
Description = The target is clamped and squeezed by the user's very thick and sturdy shell for four to five turns.
#-------------------------------
[WHIRLPOOL]
Name = Whirlpool
Type = WATER
Category = Special
Power = 35
Accuracy = 85
TotalPP = 15
Target = NearOther
FunctionCode = BindTargetDoublePowerIfTargetUnderwater
Flags = CanProtect,CanMirrorMove
Description = Traps foes in a violent swirling whirlpool for four to five turns.
#-------------------------------
[SURGINGSTRIKES]
Name = Surging Strikes
Type = WATER
Category = Physical
Power = 25
Accuracy = 100
TotalPP = 5
Target = NearOther
FunctionCode = HitThreeTimesAlwaysCriticalHit
Flags = Contact,CanProtect,CanMirrorMove,CannotMetronome
Description = Hits three times in a row with mastery of the Water style. This attack always deals critical hits.
#-------------------------------
[WATERSHURIKEN]
Name = Water Shuriken
Type = WATER
Category = Special
Power = 15
Accuracy = 100
TotalPP = 20
Target = NearOther
Priority = 1
FunctionCode = HitTwoToFiveTimesOrThreeForAshGreninja
Flags = CanProtect,CanMirrorMove
Description = The user hits the target with throwing stars 2-5 times in a row. This move always goes first.
#-------------------------------
[AQUARING]
Name = Aqua Ring
Type = WATER
Category = Status
Accuracy = 0
TotalPP = 20
Target = User
FunctionCode = StartHealUserEachTurn
Description = The user envelops itself in a veil made of water. It regains some HP on every turn.
#-------------------------------
[LIFEDEW]
Name = Life Dew
Type = WATER
Category = Status
Accuracy = 0
TotalPP = 10
Target = UserAndAllies
FunctionCode = HealUserAndAlliesQuarterOfTotalHP
Flags = CannotMetronome
Description = The user scatters mysterious water around and restores the HP of itself and its allies.
#-------------------------------
[RAINDANCE]
Name = Rain Dance
Type = WATER
Category = Status
Accuracy = 0
TotalPP = 5
Target = BothSides
FunctionCode = StartRainWeather
Description = The user summons a heavy rain that falls for five turns, powering up Water-type moves.
#-------------------------------
[SOAK]
Name = Soak
Type = WATER
Category = Status
Accuracy = 100
TotalPP = 20
Target = NearOther
FunctionCode = SetTargetTypesToWater
Flags = CanProtect,CanMirrorMove
Description = The user shoots a torrent of water at the target and changes the target's type to Water.
#-------------------------------
[WATERSPORT]
Name = Water Sport
Type = WATER
Category = Status
Accuracy = 0
TotalPP = 15
Target = BothSides
FunctionCode = StartWeakenFireMoves
Description = The user soaks itself with water. The move weakens Fire-type moves while the user is in the battle.
#-------------------------------
[WITHDRAW]
Name = Withdraw
Type = WATER
Category = Status
Accuracy = 0
TotalPP = 40
Target = User
FunctionCode = RaiseUserDefense1
Description = The user withdraws its body into its hard shell, raising its Defense stat.
