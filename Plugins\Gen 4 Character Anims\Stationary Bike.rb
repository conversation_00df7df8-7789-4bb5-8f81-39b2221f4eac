class Game_Player
  alias __cycle_stop__set_movement_type set_movement_type unless method_defined?(:__cycle_stop__set_movement_type)
  def set_movement_type(*args)
    type = args[0]
    case type
    when :cycling_stopped
      begin
        meta = GameData::PlayerMetadata.get($player&.character_ID || 1)
        self.move_speed = 5
        if meta && meta.respond_to?(:cycle_stop_charset)
          new_charset = pbGetPlayerCharset(meta.cycle_stop_charset)
          @character_name = new_charset if new_charset
        end
      rescue => e
        Console.echo_error("Error in cycling stopped animation: #{e.message}")
      end
    else
      __cycle_stop__set_movement_type(*args)
    end
  end
end

alias __cycle_stop__pbUpdateVehicle pbUpdateVehicle unless defined?(__cycle_stop__pbUpdateVehicle)
def pbUpdateVehicle
  if $PokemonGlobal&.bicycle
    $game_player.set_movement_type(:cycling_stopped)
    return
  end
  __cycle_stop__pbUpdateVehicle
end
