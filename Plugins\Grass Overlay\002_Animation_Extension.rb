#==============================================================================
# Extension to AnimationContainerSprite to support callbacks when animations end
#==============================================================================

class AnimationContainerSprite < RPG::Sprite
  alias original_update update
  
  def update
    original_update
    # Check if animation has finished and we have a callback
    if !effect? && @on_animation_end
      callback = @on_animation_end
      @on_animation_end = nil
      callback.call
    end
  end
end
