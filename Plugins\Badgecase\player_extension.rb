#===============================================================================
# Extension to Player class for Badgecase plugin
#===============================================================================
class Player < Trainer
  # @return [Array<Boolean>] whether the player has met each Gym Leader (true if met)
  attr_accessor :metGymLeader
  
  alias badgecase_initialize initialize
  def initialize(name, trainer_type)
    badgecase_initialize(name, trainer_type)
    @metGymLeader = [false] * 8
  end
  
  # Check if the player has met a specific gym leader
  # @param gym_leader_id [Integer] the ID of the gym leader (0-7)
  # @return [Boolean] whether the player has met the gym leader
  def met_gym_leader?(gym_leader_id)
    return false if !@metGymLeader
    return @metGymLeader[gym_leader_id]
  end
  
  # Set whether the player has met a specific gym leader
  # @param gym_leader_id [Integer] the ID of the gym leader (0-7)
  # @param value [Boolean] whether the player has met the gym leader
  def set_met_gym_leader(gym_leader_id, value = true)
    @metGymLeader = [false] * 8 if !@metGymLeader
    @metGymLeader[gym_leader_id] = value
  end
end
