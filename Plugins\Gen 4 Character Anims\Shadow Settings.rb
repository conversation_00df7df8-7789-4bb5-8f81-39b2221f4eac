if !defined?(OWShadowSettings)
  module OWShadowSettings
    # Set this to true if you want the event name and character name blacklists to be case sensitive.
    CASE_SENSITIVE_BLACKLISTS = false

    # If an event name contains one of these words, it will not have a shadow.
    SHADOWLESS_EVENT_NAME     = [
      "door", "nurse", "Healing balls", "Mart", "SmashRock", "StrengthBoulder",
      "CutTree", "HeadbuttTree", "BerryPlant", ".shadowless", ".noshadow", ".sl"
    ]

    # If the character file and event uses contains one of these words in its filename, it will not have a shadow.
    SHADOWLESS_CHARACTER_NAME = ["nil"]
  end
end
