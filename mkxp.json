//==============================================================================
// Since this is a JSON file, comments are written with // instead of a #. Any
// backslashes in file paths written in here need to be escaped (i.e. a single
// backslash becomes a double backslash). If that's too much hassle, you can use
// a single forward slash instead (even on Windows).
//==============================================================================
{
    //==========================================================================
    // Things you may want to/should change.
    //==========================================================================

    // The name shown in the game window's title. Is the game's display name.
    // Note that the "Game Title" defined in RPG Maker XP should not have
    // accented characters in it, which is why this property is useful.
    //
    "windowTitle": "Pokémon Essentials v21.1",

    // The game window's width and height, in pixels.
    //
    "defScreenW": 512,
    "defScreenH": 384,

    // The path to the game window's icon. For Linux only. (Default: none)
    //
    // "iconPath": "/path/to/icon.png",

    // Enable F12 to make the game soft reset.
    //
    // "enableReset": true,

    // Enable F1 to open the keybinding menu.
    //
    // "enableSettings": true,

    // The names of each input, as seen in the F1 keybinding menu. The names
    // have no other use.
    //
    "bindingNames": {
      "c": "Use",
      "b": "Back",
      "a": "Action",
      "x": "JumpUp",
      "y": "JumpDown",
      "z": "Special",
      "l": "(unused)",
      "r": "(unused)"
    },

    // The name of this game's save folder. If undefined, the save folder's name
    // will be the "Game Title" as defined in RPG Maker XP. It should not
    // contain any accented characters.
    //
    // "dataPathApp": "Pokemon Essentials v21",

    // If defined, the game's save folder goes into a folder with this name (in
    // C:\Users\<USER>\AppData\Roaming). If undefined, the game's save folder
    // goes directly into C:\Users\<USER>\AppData\Roaming. It should not
    // contain any accented characters.
    // This acts as a company/creator name, and this folder could contain save
    // folders for multiple games made by the same company/creator.
    //
    // "dataPathOrg": "PKMN Essentials",

    // SoundFont to use for midi playback (via fluidsynth).
    //
    "midiSoundFont": "soundfont.sf2",

    // Number of streams to open for BGM tracks. If the game needs multi-track
    // audio, this should be set to as many available tracks as the game needs.
    // Maximum: 16.
    //
    // "BGMTrackCount": 1,

    // Number of OpenAL sources to allocate for SE playback. If there are a lot
    // of sounds playing at the same time and audibly cutting each other off,
    // try increasing this number. Maximum: 64.
    //
    // "SESourceCount": 6,

    // Index all accessible assets via their lower case path (emulates Windows
    // case insensitivity).
    //
    // "pathCache": true,

    //==========================================================================
    // Window size, scaling, rendering and frame rate.
    // You shouldn't need to change any of these.
    //==========================================================================

    // Whether the game starts in full-screen mode (this can always be toggled
    // with Alt-Enter during runtime).
    //
    // "fullscreen": false,

    // Whether you can toggle full-screen mode by pressing Alt + Enter with
    // either the left or right Alt key.
    //
    // "anyAltToggleFS": false,

    // Whether the game window is freely resizable.
    //
    // "winResizable": true,

    // Whether the game screen's aspect ratio is preserved when resizing the
    // window. If false, the game screen will stretch and distort to fill the
    // entire window.
    //
    // "fixedAspectRatio": true,

    // When resizing the game screen to fit the window, whether to scale up the
    // game screen by an integer amount (as large as the current window size
    // allows) before doing any last additional scalings to fill part/all of the
    // remaining window space (or none at all if integerScalingLastMile is
    // disabled).
    // If fixedAspectRatio is disabled, the integer scale factors in horizontal
    // and vertical direction can differ depending on how much space is
    // available; otherwise they are forced to the smaller of the two.
    //
    // "integerScalingActive": false,

    // When integerScalingActive is enabled, this option controls whether the
    // scaled game screen is further scaled (with linear interpolation when
    // smoothScaling is enabled) to fill the rest of the game window.
    // Note that this option still respects fixedAspectRatio.
    //
    // "integerScalingLastMile": true,

    // Whether to apply linear interpolation when the game screen is upscaled.
    //
    // "smoothScaling": false,

    // Whether to apply Lanczos3 interpolation when the game screen is upscaled
    // (typically higher quality than the linear smoothScaling).
    //
    // "lanczos3Scaling": false,

    // Workaround for buggy graphics drivers that don't properly synchronize
    // texture access. This is most apparent when text doesn't show up or the
    // map tileset doesn't render at all.
    //
    "subImageFix": true,

    // Sync screen redraws to the monitor refresh rate, to reduce screen
    // tearing.
    //
    "vsync": true,

    // Use a fixed framerate that is approx. equal to the native screen refresh
    // rate. This is different from "fixedFramerate" because the actual frame
    // rate is reported back to the game, ensuring correct timers. If the screen
    // refresh rate cannot be determined, this option is force-disabled. This
    // option may be force-disabled at build time.
    //
    "syncToRefreshrate": true,

    // The game's frame rate, if "syncToRefreshrate" is false.
    //
    // "fixedFramerate": 60,
}
