#===============================================================================
# Custom Pause Menu Scene class
#===============================================================================
class CustomPauseMenu_Scene < PokemonPauseMenu_Scene
  # Animation constants
  FADE_IN_DURATION = 0.1         # Duration in seconds for fade-in animation
  FADE_OUT_DURATION = 0.1        # Duration in seconds for fade-out animation
  BUBBLE_ANIMATION_DURATION = 0.15 # Duration for bubble selection animation
  COLON_BLINK_INTERVAL = 0.5     # Time between colon blinks in seconds

  # UI element opacity constants
  BACKGROUND_OPACITY = 128       # Half opacity for background circles
  FOREGROUND_OPACITY = 255       # Full opacity for UI elements

  def pbStartScene
    # Initialize viewport but don't call super to avoid creating default UI elements
    @viewport = Viewport.new(0, 0, Graphics.width, Graphics.height)
    @viewport.z = 99999
    @sprites = {}
    @original_opacities = {}

    # Create basic UI elements
    createBackgroundElements
    createCommandWindows

    # Load icon bitmaps
    loadIconBitmaps

    # Initialize UI displays
    initializeMoneyDisplay
    initializeClockDisplay
    initializeSeasonDisplay
    initializeWeatherDisplay

    # Set initial opacity for all UI elements
    setInitialOpacities

    # Play the menu open sound
    pbSEPlay("GUI menu open")

    # Fade in the UI elements when the menu first opens
    pbFadeInUI

    # Start a timer to update the clock display for blinking colon
    @clock_update_timer = System.uptime
  end

  # Create background UI elements
  def createBackgroundElements
    # Background circles
    @sprites["background_inner"] = Sprite.new(@viewport)
    @sprites["background_inner"].bitmap = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/", "circle_inner")
    @sprites["background_inner"].x = Graphics.width / 2 - @sprites["background_inner"].bitmap.width / 2
    @sprites["background_inner"].y = Graphics.height / 2 - @sprites["background_inner"].bitmap.height / 2
    @sprites["background_inner"].z = 0
    @sprites["background_inner"].opacity = 0  # Start with zero opacity for fade-in

    @sprites["background_outer"] = Sprite.new(@viewport)
    @sprites["background_outer"].bitmap = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/", "circle_outer")
    @sprites["background_outer"].x = Graphics.width / 2 - @sprites["background_outer"].bitmap.width / 2
    @sprites["background_outer"].y = Graphics.height / 2 - @sprites["background_outer"].bitmap.height / 2
    @sprites["background_outer"].z = 1
    @sprites["background_outer"].opacity = 0  # Start with zero opacity for fade-in

    # Top bar
    @sprites["top_bar"] = Sprite.new(@viewport)
    @sprites["top_bar"].bitmap = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/", "top_bar")
    @sprites["top_bar"].x = Graphics.width / 2 - @sprites["top_bar"].bitmap.width / 2
    @sprites["top_bar"].y = 0
    @sprites["top_bar"].z = 2
    @sprites["top_bar"].opacity = 0  # Start with zero opacity for fade-in
  end

  # Create command windows (hidden but needed for functionality)
  def createCommandWindows
    # Command window (needed for functionality)
    @sprites["cmdwindow"] = Window_CommandPokemon.new([])
    @sprites["cmdwindow"].visible = false
    @sprites["cmdwindow"].viewport = @viewport

    # Info and help windows (needed for some menu functionality)
    @sprites["infowindow"] = Window_UnformattedTextPokemon.newWithSize("", 0, 0, 32, 32, @viewport)
    @sprites["infowindow"].visible = false
    @sprites["helpwindow"] = Window_UnformattedTextPokemon.newWithSize("", 0, 0, 32, 32, @viewport)
    @sprites["helpwindow"].visible = false

    @infostate = false
    @helpstate = false
  end

  # Load all icon bitmaps
  def loadIconBitmaps
    @icon_bitmaps = {}
    # Load gender-specific bag icons
    if $player.female?
      @icon_bitmaps[:bag] = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/Icons/", "bag_female")
    else
      @icon_bitmaps[:bag] = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/Icons/", "bag_male")
    end
    # Load other icons
    @icon_bitmaps[:pokedex] = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/Icons/", "Pokedex")
    @icon_bitmaps[:party] = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/Icons/", "Party")
    @icon_bitmaps[:trainer_card] = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/Icons/", "TrainerCard")
    @icon_bitmaps[:save] = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/Icons/", "Save")
    @icon_bitmaps[:quit] = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/Icons/", "Quit")
    @icon_bitmaps[:debug] = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/Icons/", "Debug")
  end

  # Set initial opacity values for all UI elements
  def setInitialOpacities
    # Define UI elements and their opacity values
    background_elements = ["background_inner", "background_outer"]
    foreground_elements = ["top_bar", "money_display", "clock_display", "season_display", "weather_display"]

    # Store original opacities for background elements
    background_elements.each do |element|
      @original_opacities[@sprites[element]] = BACKGROUND_OPACITY if @sprites[element]
    end

    # Store original opacities for foreground elements
    foreground_elements.each do |element|
      @original_opacities[@sprites[element]] = FOREGROUND_OPACITY if @sprites[element]
    end

    # Set initial opacity to 0 for fade-in effect
    foreground_elements.each do |element|
      @sprites[element].opacity = 0 if @sprites[element]
    end
  end

  # Override to show our custom UI instead of the default command window
  def pbShowMenu
    # Don't make the command window visible, but we still need it for functionality
    @sprites["cmdwindow"].visible = false
    @sprites["infowindow"].visible = @infostate
    @sprites["helpwindow"].visible = @helpstate

    # Update all UI displays
    updateAllDisplays

    # Fade in our custom UI elements
    pbFadeInUI

    # Start a timer to update the clock display for blinking colon
    @clock_update_timer = System.uptime
  end

  # Override to hide our custom UI when needed (e.g., when save screen appears)
  def pbHideMenu
    # Hide the default windows
    super

    # Fade out all UI elements
    pbFadeOutUI

    # Hide sprites after fade out
    hideAllSprites
  end

  # Hide all sprites but preserve their opacity values for later restoration
  def hideAllSprites
    @sprites.each_value do |sprite|
      if sprite.is_a?(Sprite) || sprite.is_a?(BitmapSprite)
        # Store original opacity if not already stored
        @original_opacities[sprite] = sprite.opacity if !@original_opacities[sprite]

        # Hide the sprite but restore its opacity for when it's shown again
        sprite.visible = false
        sprite.opacity = @original_opacities[sprite]
      end
    end
  end

  # Update all UI displays
  def updateAllDisplays
    updateMoneyDisplay
    updateClockDisplay
    updateSeasonDisplay
    updateWeatherDisplay
  end

  # Get all UI sprites that should be animated
  def getUISprites
    # List of all UI element keys
    ui_elements = [
      "background_inner", "background_outer", "top_bar",
      "money_display", "clock_display", "season_display",
      "weather_display", "custom_commands"
    ]

    # Filter to only include sprites that exist
    fade_sprites = ui_elements.map { |key| @sprites[key] }.compact
    return fade_sprites
  end

  # Fade in all UI elements with animation
  def pbFadeInUI
    # Get all custom UI elements that need to fade in
    fade_sprites = getUISprites

    # Make all sprites visible but with their current opacity
    fade_sprites.each do |sprite|
      sprite.visible = true

      # If no original opacity is stored for this sprite, set default values
      if !@original_opacities[sprite]
        # Background elements get partial opacity, others get full opacity
        is_background = (sprite == @sprites["background_inner"] || sprite == @sprites["background_outer"])
        @original_opacities[sprite] = is_background ? BACKGROUND_OPACITY : FOREGROUND_OPACITY
      end
    end

    # Perform fade in animation
    animateOpacity(fade_sprites, 0, @original_opacities, FADE_IN_DURATION)
  end

  # Override to show commands in our custom UI
  def pbShowCommands(commands)
    ret = -1
    cmdwindow = @sprites["cmdwindow"]
    cmdwindow.commands = commands
    cmdwindow.index = $game_temp.menu_last_choice
    cmdwindow.resizeToFit(commands)
    cmdwindow.x = Graphics.width - cmdwindow.width
    cmdwindow.y = 0
    cmdwindow.visible = false  # Hide the default window

    # Create our custom command display
    @sprites["custom_commands"] = BitmapSprite.new(Graphics.width, Graphics.height, @viewport)
    @sprites["custom_commands"].z = 3

    # Initialize animation variables
    initializeBubbleAnimationData

    # Draw the commands
    drawCustomCommands(commands, cmdwindow.index)

    # Animation frame rate control
    last_update_time = System.uptime
    animation_frame_rate = 60  # Target frames per second for animation
    frame_duration = 1.0 / animation_frame_rate

    loop do
      current_time = System.uptime
      frame_delta = current_time - last_update_time

      # Handle left/right navigation instead of up/down
      handleMenuNavigation(cmdwindow, commands)

      # Update animations at the target frame rate
      if frame_delta >= frame_duration
        drawCustomCommands(commands, cmdwindow.index)
        last_update_time = current_time
      end

      # Keep all UI displays updated
      updateAllDisplays

      Graphics.update
      Input.update
      pbUpdateSceneMap

      # Check for menu exit conditions
      if Input.trigger?(Input::BACK) || Input.trigger?(Input::ACTION)
        ret = -1
        break
      elsif Input.trigger?(Input::USE)
        ret = cmdwindow.index
        $game_temp.menu_last_choice = ret
        break
      end
    end

    # Clean up
    cleanupCustomCommands

    return ret
  end

  # Initialize bubble animation data
  def initializeBubbleAnimationData
    @last_selected_index = nil
    @bubble_animation_data = {}
    @selection_change_time = nil
  end

  # Handle menu navigation with left/right controls
  def handleMenuNavigation(cmdwindow, commands)
    if Input.trigger?(Input::LEFT)
      # Move selection left (decrease index)
      cmdwindow.index = (cmdwindow.index - 1) % commands.length
    elsif Input.trigger?(Input::RIGHT)
      # Move selection right (increase index)
      cmdwindow.index = (cmdwindow.index + 1) % commands.length
    end
  end

  # Clean up custom command resources
  def cleanupCustomCommands
    @sprites["custom_commands"].dispose if @sprites["custom_commands"]
    @sprites.delete("custom_commands")
  end

  # Get the appropriate icon for a menu command
  # Returns the bitmap for the icon
  def getIconForCommand(command)
    case command
    when _INTL("Pokédex")
      return @icon_bitmaps[:pokedex]
    when _INTL("Pokémon")
      return @icon_bitmaps[:party]
    when _INTL("Bag")
      return @icon_bitmaps[:bag]
    when _INTL("Trainer Card")
      return @icon_bitmaps[:trainer_card]
    when _INTL("Save")
      return @icon_bitmaps[:save]
    when _INTL("Options")
      return @icon_bitmaps[:debug]
    when _INTL("Debug")
      return @icon_bitmaps[:debug]
    when _INTL("Quit Game")
      return @icon_bitmaps[:quit]
    else
      # Default to nil for commands without specific icons
      return nil
    end
  end

  # Get position adjustments for each icon
  # Returns a hash with :x and :y offsets
  def getIconAdjustments(command)
    case command
    when _INTL("Pokédex")
      return { x: 3, y: 2 }
    when _INTL("Pokémon")
      return { x: 1, y: 0 }  
    when _INTL("Bag")
      return { x: 0, y: -2 }   
    when _INTL("Trainer Card")
      return { x: 0, y: 12 }   
    when _INTL("Save")
      return { x: 0, y: 2 }
    when _INTL("Quit Game")
      return { x: -4, y: -2 }
    when _INTL("Options")
      return { x: 0, y: 6 }
    when _INTL("Debug") 
      return { x: 0, y: 6 }
    else
      # Default to no adjustment for commands without specific icons
      return { x: 0, y: 0 }
    end
  end

  # Draw the custom command menu
  def drawCustomCommands(commands, index)
    return if !@sprites["custom_commands"]
    bitmap = @sprites["custom_commands"].bitmap
    bitmap.clear

    # Load bubble resources if needed
    loadBubbleResources

    # Get bubble mappings for menu options
    bubble_mappings = getBubbleMappings

    # Update animation data if selection changed
    updateSelectionAnimationData(index)

    # Collect bubble data for all commands
    bubble_data = collectBubbleData(commands, index, bubble_mappings)

    # Sort bubbles for proper layering
    sortBubblesByLayer(bubble_data)

    # Draw all bubbles in the sorted order
    drawBubbles(bitmap, bubble_data)
  end

  # Load bubble image resources
  def loadBubbleResources
    if !@bubble_selected_bitmap || !@bubble_unselected_bitmap
      @bubble_selected_bitmap = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/", "slot_selected")
      @bubble_unselected_bitmap = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/", "slot_unselected")
    end
  end

  # Get bubble mappings for menu options
  def getBubbleMappings
    # This maps menu option names to specific bubble indices
    return {
      _INTL("Pokédex") => 0,       # Pokedex bubble
      _INTL("Pokémon") => 1,       # Pokemon bubble
      _INTL("Bag") => 2,           # Bag bubble
      _INTL("Pokégear") => 3,      # Pokegear bubble
      _INTL("Town Map") => 3,      # Use same bubble as Pokegear for Town Map
      _INTL("Trainer Card") => 4,  # Trainer Card bubble
      _INTL("Save") => 5,          # Save bubble
      _INTL("Options") => 6,       # Options bubble
      _INTL("Debug") => 7,         # Debug bubble
      _INTL("Quit Game") => 8,     # Quit bubble
      # Special game modes
      _INTL("Quit") => 8,          # Safari Zone quit option
      _INTL("Quit Contest") => 8,  # Bug Contest quit option
    }
  end

  # Update animation data when selection changes
  def updateSelectionAnimationData(index)
    # Check if the selected index has changed
    if @last_selected_index != index
      # Play a sound effect when changing selection
      pbSEPlay("GUI sel cursor") if @last_selected_index != nil

      # Store the time when selection changed for animation
      @selection_change_time = System.uptime

      # Remember which bubbles changed state
      @bubble_animation_data.clear
      if @last_selected_index != nil
        # Previously selected bubble is now unselected
        @bubble_animation_data[@last_selected_index] = {
          from_selected: true,
          to_selected: false,
          start_time: System.uptime
        }
      end

      # Newly selected bubble
      @bubble_animation_data[index] = {
        from_selected: false,
        to_selected: true,
        start_time: System.uptime
      }

      @last_selected_index = index
    end
  end

  # Collect data for all bubbles
  def collectBubbleData(commands, index, bubble_mappings)
    # Bubble configuration
    bubble_width = 56
    bubble_height = 88
    bubble_y = 206       # Y position of the bubbles
    bubble_spacing = -8  # Space between bubbles
    icon_y_offset = 26   # Vertical offset from the top of the bubble
    center_x = Graphics.width / 2  # Center position for bubbles

    bubble_data = []

    commands.each_with_index do |command, i|
      # Calculate bubble data
      bubble_index = bubble_mappings[command] || (i % 9)
      src_rect = Rect.new(bubble_index * bubble_width, 0, bubble_width, bubble_height)
      relative_position = i - index
      bubble_x = center_x - (bubble_width / 2) + (relative_position * (bubble_width + bubble_spacing))

      # Determine if this bubble is animating
      is_animating = @bubble_animation_data[i] &&
                    System.uptime - @bubble_animation_data[i][:start_time] < BUBBLE_ANIMATION_DURATION

      # Calculate animation progress (0.0 to 1.0)
      progress = is_animating ?
                (System.uptime - @bubble_animation_data[i][:start_time]) / BUBBLE_ANIMATION_DURATION :
                1.0
      progress = [0.0, [progress, 1.0].min].max  # Clamp between 0 and 1

      # Calculate bubble Y position
      current_bubble_y = calculateBubbleYPosition(i, index, is_animating, progress, bubble_y)

      # Get the icon data if available
      icon_data = getIconDataForBubble(command, i, index, is_animating, progress, bubble_x, current_bubble_y, icon_y_offset)

      # Store all the bubble data for drawing later
      bubble_data << {
        index: i,
        selected: i == index,
        x: bubble_x,
        y: current_bubble_y,
        src_rect: src_rect,
        icon_data: icon_data,
        relative_position: relative_position.abs,  # Store absolute distance from selected bubble
        is_animating: is_animating,
        animation_progress: progress
      }
    end

    return bubble_data
  end

  # Calculate the Y position for a bubble
  def calculateBubbleYPosition(bubble_index, selected_index, is_animating, progress, base_y)
    if is_animating
      # Get animation data
      anim = @bubble_animation_data[bubble_index]

      # Determine start and end positions
      start_y = anim[:from_selected] ? base_y : base_y + 4
      end_y = anim[:to_selected] ? base_y : base_y + 4

      # Add a slight bounce effect for selection
      if anim[:to_selected]
        # When becoming selected, add a bounce
        bounce_offset = Math.sin(progress * Math::PI) * 3
        return lerp(start_y, end_y - bounce_offset, BUBBLE_ANIMATION_DURATION,
                   anim[:start_time], System.uptime)
      else
        # Simple lerp for deselection
        return lerp(start_y, end_y, BUBBLE_ANIMATION_DURATION,
                   anim[:start_time], System.uptime)
      end
    else
      # Not animating, use final position
      return bubble_index == selected_index ? base_y : base_y + 4
    end
  end

  # Get icon data for a bubble
  def getIconDataForBubble(command, bubble_index, selected_index, is_animating, progress, bubble_x, bubble_y, icon_y_offset)
    icon_bitmap = getIconForCommand(command)
    return nil if !icon_bitmap

    # Get position adjustments for this icon
    adjustments = getIconAdjustments(command)

    full_width = icon_bitmap.width
    height = icon_bitmap.height
    half_width = full_width / 2
    bubble_width = 56  # Width of the bubble

    # Calculate the icon position (centered on the bubble) with adjustments
    icon_x = bubble_x + (bubble_width / 2) - (half_width / 2) + adjustments[:x]
    icon_y = bubble_y + icon_y_offset + adjustments[:y]

    # Determine which half of the icon to use
    if is_animating && @bubble_animation_data[bubble_index][:from_selected] != @bubble_animation_data[bubble_index][:to_selected]
      # Crossfade between unselected and selected icons during animation
      return {
        bitmap: icon_bitmap,
        x: icon_x,
        y: icon_y,
        animating: true,
        progress: progress,
        from_selected: @bubble_animation_data[bubble_index][:from_selected],
        to_selected: @bubble_animation_data[bubble_index][:to_selected],
        src_rect_unselected: Rect.new(0, 0, half_width, height),
        src_rect_selected: Rect.new(half_width, 0, half_width, height)
      }
    else
      # Not animating or same state, use appropriate half
      icon_src_x = bubble_index == selected_index ? half_width : 0
      icon_src_rect = Rect.new(icon_src_x, 0, half_width, height)

      return {
        bitmap: icon_bitmap,
        x: icon_x,
        y: icon_y,
        animating: false,
        src_rect: icon_src_rect
      }
    end
  end

  # Sort bubbles for proper layering
  def sortBubblesByLayer(bubble_data)
    bubble_data.sort! do |a, b|
      if a[:selected] && !b[:selected]
        1  # Selected bubble comes last (on top)
      elsif !a[:selected] && b[:selected]
        -1 # Selected bubble comes last (on top)
      elsif !a[:selected] && !b[:selected]
        # For unselected bubbles, sort by relative position (smaller = closer = on top)
        b[:relative_position] <=> a[:relative_position]  # Reverse order so closer is on top
      else
        0
      end
    end
  end

  # Draw all bubbles in the sorted order
  def drawBubbles(bitmap, bubble_data)
    bubble_data.each do |data|
      # Draw the bubble background
      drawBubbleBackground(bitmap, data)

      # Draw the icon if it exists
      drawBubbleIcon(bitmap, data) if data[:icon_data]
    end
  end

  # Draw the bubble background
  def drawBubbleBackground(bitmap, data)
    if data[:is_animating]
      # For animating bubbles, handle the transition
      anim = @bubble_animation_data[data[:index]]

      if anim[:from_selected] && !anim[:to_selected]
        # Transitioning from selected to unselected
        selected_opacity = 255 * (1.0 - data[:animation_progress])
        unselected_opacity = 255 * data[:animation_progress]

        # Draw both with appropriate opacity
        bitmap.blt(data[:x], data[:y], @bubble_selected_bitmap, data[:src_rect], selected_opacity)
        bitmap.blt(data[:x], data[:y], @bubble_unselected_bitmap, data[:src_rect], unselected_opacity)
      elsif !anim[:from_selected] && anim[:to_selected]
        # Transitioning from unselected to selected
        unselected_opacity = 255 * (1.0 - data[:animation_progress])
        selected_opacity = 255 * data[:animation_progress]

        # Draw both with appropriate opacity
        bitmap.blt(data[:x], data[:y], @bubble_unselected_bitmap, data[:src_rect], unselected_opacity)
        bitmap.blt(data[:x], data[:y], @bubble_selected_bitmap, data[:src_rect], selected_opacity)
      end
    else
      # Not animating, draw normal bubble
      if data[:selected]
        bitmap.blt(data[:x], data[:y], @bubble_selected_bitmap, data[:src_rect])
      else
        bitmap.blt(data[:x], data[:y], @bubble_unselected_bitmap, data[:src_rect])
      end
    end
  end

  # Draw the icon for a bubble
  def drawBubbleIcon(bitmap, data)
    icon = data[:icon_data]
    if icon[:animating]
      # Handle animating icons (crossfade between selected/unselected)
      if icon[:from_selected] && !icon[:to_selected]
        # Transitioning from selected to unselected
        selected_opacity = 255 * (1.0 - data[:animation_progress])
        unselected_opacity = 255 * data[:animation_progress]

        # Draw both with appropriate opacity
        bitmap.blt(icon[:x], icon[:y], icon[:bitmap], icon[:src_rect_selected], selected_opacity)
        bitmap.blt(icon[:x], icon[:y], icon[:bitmap], icon[:src_rect_unselected], unselected_opacity)
      elsif !icon[:from_selected] && icon[:to_selected]
        # Transitioning from unselected to selected
        unselected_opacity = 255 * (1.0 - data[:animation_progress])
        selected_opacity = 255 * data[:animation_progress]

        # Draw both with appropriate opacity
        bitmap.blt(icon[:x], icon[:y], icon[:bitmap], icon[:src_rect_unselected], unselected_opacity)
        bitmap.blt(icon[:x], icon[:y], icon[:bitmap], icon[:src_rect_selected], selected_opacity)
      end
    else
      # Not animating, draw normal icon
      bitmap.blt(icon[:x], icon[:y], icon[:bitmap], icon[:src_rect])
    end
  end

  # Initialize the money display
  def initializeMoneyDisplay
    # Load number and cash symbol images
    @number_bitmap = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/", "ui_numbers")
    @cash_symbol_bitmap = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/", "UI_CashSymbol")

    # Create sprite for money display
    @sprites["money_display"] = BitmapSprite.new(Graphics.width, Graphics.height, @viewport)
    @sprites["money_display"].z = 3

    # Update the money display
    updateMoneyDisplay
  end

  # Update the money display with current money
  def updateMoneyDisplay
    return if !@sprites["money_display"]

    # Clear the bitmap
    bitmap = @sprites["money_display"].bitmap
    bitmap.clear

    # Get player's money as a string with formatting
    money_string = $player.money.to_s_formatted.gsub(",", "")

    # Configuration
    number_width = 10  # Width of each number (10 pixels)
    number_height = 10 # Height of each number in the bitmap (10 pixels)
    number_spacing = -2  # Spacing between numbers
    cash_symbol_spacing = 2  # Spacing between cash symbol and first number

    # Get dimensions
    cash_symbol_width = @cash_symbol_bitmap.width
    cash_symbol_height = @cash_symbol_bitmap.height

    # Calculate total width of the money display
    total_width = (money_string.length * (number_width + number_spacing)) +
                  cash_symbol_width + cash_symbol_spacing

    # Position on the right side of the screen
    x_position = Graphics.width - total_width - 72  # 72 pixels from the right edge
    y_position = 8  # 8 pixels from the top

    # Calculate vertical alignment for the cash symbol and numbers
    if cash_symbol_height > number_height
      number_y = y_position + ((cash_symbol_height - number_height) / 2)
      cash_y = y_position
    else
      number_y = y_position
      cash_y = y_position + ((number_height - cash_symbol_height) / 2)
    end

    # Draw the cash symbol first
    bitmap.blt(x_position, cash_y, @cash_symbol_bitmap,
               Rect.new(0, 0, cash_symbol_width, cash_symbol_height))

    # Update x position for numbers
    x_position += cash_symbol_width + cash_symbol_spacing

    # Draw each digit
    drawNumberString(bitmap, money_string, x_position, number_y, number_width, number_height, number_spacing)
  end

  # Draw a string of numbers using the number bitmap
  # @param bitmap [Bitmap] The bitmap to draw on
  # @param number_string [String] The string of digits to draw
  # @param x [Integer] The starting x position
  # @param y [Integer] The y position
  # @param digit_width [Integer] The width of each digit
  # @param digit_height [Integer] The height of each digit
  # @param spacing [Integer] The spacing between digits
  # @return [Integer] The ending x position after drawing
  def drawNumberString(bitmap, number_string, x, y, digit_width, digit_height, spacing)
    current_x = x

    number_string.each_char do |digit|
      # Convert digit to index in the bitmap (1-9,0 order)
      digit_value = digit.to_i
      digit_index = (digit_value == 0) ? 9 : digit_value - 1  # 0 is at index 9, 1-9 are at indices 0-8

      # Calculate the position in the bitmap
      src_x = digit_index * digit_width
      src_rect = Rect.new(src_x, 0, digit_width, digit_height)

      # Draw the digit
      bitmap.blt(current_x, y, @number_bitmap, src_rect)

      # Move to the next position
      current_x += digit_width + spacing
    end

    return current_x
  end

  # Initialize the clock display
  def initializeClockDisplay
    # Load number and special character bitmaps
    @special_bitmap = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/", "UI_special")

    # Load AM and PM bitmaps
    @am_bitmap = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/", "AM")
    @pm_bitmap = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/", "PM")

    # Create sprite for clock display
    @sprites["clock_display"] = BitmapSprite.new(Graphics.width, Graphics.height, @viewport)
    @sprites["clock_display"].z = 3

    # Initialize colon blink timer
    @colon_visible = true
    @last_blink_time = System.uptime

    # Update the clock display
    updateClockDisplay
  end

  # Update the clock display with current time
  def updateClockDisplay
    return if !@sprites["clock_display"]

    # Clear the bitmap
    bitmap = @sprites["clock_display"].bitmap
    bitmap.clear

    # Get current time and format it
    time = pbGetTimeNow
    time_data = formatTimeFor12HourClock(time)

    # Configuration
    number_width = 10   # Width of each number (10 pixels)
    number_height = 10  # Height of each number in the bitmap (10 pixels)
    special_width = 10  # Width of special characters like colon (10 pixels)
    special_height = 10 # Height of special characters (10 pixels)
    number_spacing = -2 # Spacing between numbers
    colon_spacing = -2  # Spacing around colon
    am_pm_spacing = 2   # Spacing between time and AM/PM

    # Calculate total width of the clock display
    am_pm_width = (time_data[:am_pm] == "AM") ? @am_bitmap.width : @pm_bitmap.width
    total_width = calculateClockWidth(time_data, number_width, number_spacing,
                                     special_width, colon_spacing, am_pm_spacing, am_pm_width)

    # Position in the upper right corner
    x_position = Graphics.width - total_width - 4  # 4 pixels from the right edge
    y_position = 10  # 10 pixels from the top

    # Draw hour digits
    x_position = drawNumberString(bitmap, time_data[:hour], x_position, y_position,
                                 number_width, number_height, number_spacing)

    # Add spacing before colon and move 1 pixel to the right
    x_position += colon_spacing + 1

    # Update colon blinking state
    updateColonBlinkState

    # Draw colon if visible
    if @colon_visible
      x_position = drawColon(bitmap, x_position, y_position, special_width, special_height)
    else
      # Always advance position as if colon was drawn
      x_position += special_width
    end

    # Add spacing after colon
    x_position += colon_spacing

    # Draw minute digits
    x_position = drawNumberString(bitmap, time_data[:minute], x_position, y_position,
                                 number_width, number_height, number_spacing)

    # Add spacing before AM/PM
    x_position += am_pm_spacing

    # Draw AM/PM using the appropriate bitmap
    drawAmPm(bitmap, x_position, y_position, time_data[:am_pm])
  end

  # Format time for 12-hour clock display
  def formatTimeFor12HourClock(time)
    hour = time.hour
    am_pm = (hour >= 12) ? "PM" : "AM"
    hour = hour % 12
    hour = 12 if hour == 0  # 0 hour should display as 12 in 12-hour format

    return {
      hour: hour.to_s,
      minute: time.min.to_s.rjust(2, '0'),  # Ensure minutes have leading zero if needed
      am_pm: am_pm
    }
  end

  # Calculate the total width of the clock display
  def calculateClockWidth(time_data, number_width, number_spacing, special_width, colon_spacing, am_pm_spacing, am_pm_width)
    return (time_data[:hour].length * (number_width + number_spacing)) +
           special_width + (colon_spacing * 2) +
           (time_data[:minute].length * (number_width + number_spacing)) +
           am_pm_spacing +
           am_pm_width
  end

  # Update the colon blinking state
  def updateColonBlinkState
    current_time = System.uptime
    if current_time - @last_blink_time >= COLON_BLINK_INTERVAL
      @colon_visible = !@colon_visible
      @last_blink_time = current_time
    end
  end

  # Draw the colon for the clock
  def drawColon(bitmap, x, y, width, height)
    colon_index = 4  # Assuming colon is the 4th character (index 4) in the special bitmap
    src_x = colon_index * width
    src_rect = Rect.new(src_x, 0, width, height)
    bitmap.blt(x, y, @special_bitmap, src_rect)
    return x + width
  end

  # Draw AM/PM indicator
  def drawAmPm(bitmap, x, y, am_pm)
    if am_pm == "AM"
      bitmap.blt(x, y, @am_bitmap, Rect.new(0, 0, @am_bitmap.width, @am_bitmap.height))
    else # PM
      bitmap.blt(x, y, @pm_bitmap, Rect.new(0, 0, @pm_bitmap.width, @pm_bitmap.height))
    end
  end

  # Initialize the weather display
  def initializeWeatherDisplay
    # Create sprite for weather display
    @sprites["weather_display"] = BitmapSprite.new(Graphics.width, Graphics.height, @viewport)
    @sprites["weather_display"].z = 3

    # Update the weather display
    updateWeatherDisplay
  end

  # Update the weather display with current weather
  def updateWeatherDisplay
    return if !@sprites["weather_display"]

    # Clear the bitmap
    bitmap = @sprites["weather_display"].bitmap
    bitmap.clear

    # Get current weather type
    weather_type = getCurrentWeatherType

    # Load the appropriate weather image directly from the Weather folder
    weather_bitmap = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/Weather/", weather_type)
    return if !weather_bitmap

    # Position in the upper left corner
    y_position = 10  # 10 pixels from the top

    # Adjust x position based on weather type
    # Longer weather names (blizzard and heavy rain) should be positioned 8 pixels to the left
    if weather_type == "blizzard" || weather_type == "heavyrain"
      x_position = 2  # 2 pixels from the left edge (8 pixels left of normal position)
    else
      x_position = 10  # 10 pixels from the left edge (default position)
    end

    # Draw the weather image
    bitmap.blt(x_position, y_position, weather_bitmap,
               Rect.new(0, 0, weather_bitmap.width, weather_bitmap.height))
  end

  # Get the current weather type as a filename string
  def getCurrentWeatherType
    # Get current weather type
    weather_type = $game_screen.weather_type

    # Convert weather type to filename
    case weather_type
    when :None     then "clear"
    when :Rain     then "rain"
    when :Storm    then "storm"
    when :Snow     then "snow"
    when :Blizzard then "blizzard"
    when :Sandstorm then "sand"
    when :HeavyRain then "heavyrain"
    when :Sun      then "sunny"
    when :Fog      then "fog"
    else
      weather_type.to_s.downcase
    end
  end

  # Initialize the season display
  def initializeSeasonDisplay
    # Load season bitmaps
    loadSeasonBitmaps

    # Create sprite for season display
    @sprites["season_display"] = BitmapSprite.new(Graphics.width, Graphics.height, @viewport)
    @sprites["season_display"].z = 3

    # Update the season display
    updateSeasonDisplay
  end

  # Load all season-related bitmaps
  def loadSeasonBitmaps
    @season_bitmaps = {
      spring: RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/", "ui_spring"),
      summer: RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/", "ui_summer"),
      autumn: RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/", "ui_autumn"),
      winter: RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/", "ui_winter")
    }

    # Load the season title image ("- SEASON -")
    @season_title_bitmap = RPG::Cache.load_bitmap("Graphics/UI/Pause Menu/", "season_title")
  end

  # Update the season display with current season
  def updateSeasonDisplay
    return if !@sprites["season_display"]

    # Clear the bitmap
    bitmap = @sprites["season_display"].bitmap
    bitmap.clear

    # Get current season bitmap
    season_bitmap = getCurrentSeasonBitmap
    return if !season_bitmap

    # Position in the upper left corner for the season icon
    x_position = 194  # 194 pixels from the left edge
    y_position = 33   # 33 pixels from the top

    # Draw season title and icon
    drawSeasonTitle(bitmap, x_position, y_position, season_bitmap)
    drawSeasonIcon(bitmap, x_position, y_position, season_bitmap)
  end

  # Get the bitmap for the current season
  def getCurrentSeasonBitmap
    # Get current season (0=Spring, 1=Summer, 2=Autumn, 3=Winter)
    season = pbGetSeason

    # Map season index to bitmap
    case season
    when 0 then @season_bitmaps[:spring]
    when 1 then @season_bitmaps[:summer]
    when 2 then @season_bitmaps[:autumn]
    when 3 then @season_bitmaps[:winter]
    end
  end

  # Draw the season title ("- SEASON -")
  def drawSeasonTitle(bitmap, x_position, y_position, season_bitmap)
    title_x = x_position + (season_bitmap.width / 2) - (@season_title_bitmap.width / 2)
    title_y = y_position - @season_title_bitmap.height - 16  # Position 16 pixels above the season icon

    bitmap.blt(title_x, title_y, @season_title_bitmap,
               Rect.new(0, 0, @season_title_bitmap.width, @season_title_bitmap.height))
  end

  # Draw the season icon
  def drawSeasonIcon(bitmap, x_position, y_position, season_bitmap)
    bitmap.blt(x_position, y_position, season_bitmap,
               Rect.new(0, 0, season_bitmap.width, season_bitmap.height))
  end

  # Animate opacity change for a group of sprites
  # @param sprites [Array<Sprite>] Array of sprites to animate
  # @param start_opacity [Integer, Hash] Starting opacity (or hash of sprite => opacity)
  # @param end_opacity [Integer, Hash] Ending opacity (or hash of sprite => opacity)
  # @param duration [Float] Duration of animation in seconds
  def animateOpacity(sprites, start_opacity, end_opacity, duration)
    return if sprites.empty?

    timer_start = System.uptime

    loop do
      # Calculate progress (0.0 to 1.0)
      current_time = System.uptime
      progress = (current_time - timer_start) / duration
      progress = [0.0, [progress, 1.0].min].max  # Clamp between 0 and 1

      # Update opacities for each sprite
      sprites.each do |sprite|
        # Determine start and end opacity for this sprite
        start_value = start_opacity.is_a?(Hash) ? start_opacity[sprite] : start_opacity
        end_value = end_opacity.is_a?(Hash) ? end_opacity[sprite] : end_opacity

        # Apply the opacity change
        sprite.opacity = lerp(start_value, end_value, duration, timer_start, current_time)
      end

      # Update clock display for blinking colon
      updateClockDisplay

      # Update graphics
      Graphics.update
      Input.update
      pbUpdateSceneMap

      # Break when fully animated
      break if progress >= 1.0
    end
  end

  # Fade out all UI elements with animation
  def pbFadeOutUI
    # Get all visible UI elements that need to fade out
    fade_sprites = getUISprites.select { |sprite| sprite.visible }

    # Store original opacities for restoration if needed
    fade_sprites.each do |sprite|
      @original_opacities[sprite] = sprite.opacity if !@original_opacities[sprite]
    end

    # Perform fade out animation
    animateOpacity(fade_sprites, @original_opacities, 0, FADE_OUT_DURATION)
  end

  def pbEndScene
    # Fade out all UI elements before disposing them
    pbFadeOutUI

    # Dispose all bitmaps
    disposeBitmaps

    # Dispose all sprites and viewport
    pbDisposeSpriteHash(@sprites)
    @viewport.dispose
  end

  # Dispose all bitmap resources
  def disposeBitmaps
    # Group bitmaps by category for easier management
    single_bitmaps = [
      # Bubble bitmaps
      @bubble_selected_bitmap, @bubble_unselected_bitmap,
      # Money display bitmaps
      @number_bitmap, @cash_symbol_bitmap,
      # Clock display bitmaps
      @special_bitmap, @am_bitmap, @pm_bitmap,
      # Season title bitmap
      @season_title_bitmap
    ]

    # Dispose all single bitmaps
    single_bitmaps.each { |bitmap| bitmap&.dispose }

    # Dispose hash-based bitmap collections
    [@season_bitmaps, @icon_bitmaps].each do |bitmap_hash|
      bitmap_hash&.each_value { |bitmap| bitmap&.dispose }
    end

    # Note: Weather display bitmaps are loaded and disposed on-demand by the cache system
  end
end

#===============================================================================
# Override the Scene_Map's call_menu method to use our custom pause menu
#===============================================================================
class Scene_Map
  alias original_call_menu call_menu

  def call_menu
    $game_temp.menu_calling = false
    $game_temp.in_menu = true
    $game_player.straighten
    $game_map.update
    sscene = CustomPauseMenu_Scene.new
    sscreen = PokemonPauseMenu.new(sscene)
    sscreen.pbStartPokemonMenu
    $game_temp.in_menu = false
  end
end
