#===============================================================================
# Utility functions for Badgecase plugin
#===============================================================================

# Sets a gym leader as met by the player
# @param gym_leader_id [Integer] the ID of the gym leader (0-7)
def pbSetMetGymLeader(gym_leader_id)
  return if !$player
  if $player.respond_to?(:set_met_gym_leader)
    $player.set_met_gym_leader(gym_leader_id)
  elsif $player.respond_to?(:metGymLeader)
    # Initialize the array if it doesn't exist
    $player.metGymLeader = [false] * 8 if !$player.metGymLeader
    $player.metGymLeader[gym_leader_id] = true
  end
end

# Checks if the player has met a specific gym leader
# @param gym_leader_id [Integer] the ID of the gym leader (0-7)
# @return [Boolean] whether the player has met the gym leader
def pbHasMetGymLeader?(gym_leader_id)
  return false if !$player
  if $player.respond_to?(:met_gym_leader?)
    return $player.met_gym_leader?(gym_leader_id)
  elsif $player.respond_to?(:metGymLeader) && $player.metGymLeader
    return $player.metGymLeader[gym_leader_id]
  end
  return false
end
