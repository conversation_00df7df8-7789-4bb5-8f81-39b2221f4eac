{\rtf1\ansi\deff3\adeflang1025
{\fonttbl{\f0\froman\fprq2\fcharset0 Times New Roman;}{\f1\froman\fprq2\fcharset2 Symbol;}{\f2\fswiss\fprq2\fcharset0 Arial;}{\f3\froman\fprq0\fcharset0 Times New Roman;}{\f4\fnil\fprq0\fcharset2 Symbol;}{\f5\froman\fprq2\fcharset128 Times New Roman;}{\f6\fnil\fprq2\fcharset0 Microsoft YaHei;}{\f7\fswiss\fprq0\fcharset128 Arial;}}
{\colortbl;\red0\green0\blue0;\red255\green51\blue51;\red51\green51\blue255;\red128\green128\blue128;}
{\stylesheet{\s0\snext0{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\nowidctlpar\cf0\hich\af3\langfe1033\dbch\af3\afs24\lang1081\loch\f3\fs24\lang1033 Default;}
{\*\cs15\snext15\loch\f4 RTF_Num 2 1;}
{\*\cs16\snext16 RTF_Num 2 2;}
{\*\cs17\snext17 RTF_Num 2 3;}
{\*\cs18\snext18 RTF_Num 2 4;}
{\*\cs19\snext19 RTF_Num 2 5;}
{\*\cs20\snext20 RTF_Num 2 6;}
{\*\cs21\snext21 RTF_Num 2 7;}
{\*\cs22\snext22 RTF_Num 2 8;}
{\*\cs23\snext23 RTF_Num 2 9;}
{\*\cs24\snext24 RTF_Num 3 1;}
{\*\cs25\snext25 RTF_Num 3 2;}
{\*\cs26\snext26 RTF_Num 3 3;}
{\*\cs27\snext27 RTF_Num 3 4;}
{\*\cs28\snext28 RTF_Num 3 5;}
{\*\cs29\snext29 RTF_Num 3 6;}
{\*\cs30\snext30 RTF_Num 3 7;}
{\*\cs31\snext31 RTF_Num 3 8;}
{\*\cs32\snext32 RTF_Num 3 9;}
{\s33\sbasedon0\snext34{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\sb240\sa120\keepn\cf0\hich\af6\langfe2052\dbch\af2\loch\f2\fs28\lang1033 Heading;}
{\s34\sbasedon0\snext34{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\sb0\sa120\cf0\hich\af3\langfe2052\dbch\af3\loch\f3\fs24\lang1033 Text body;}
{\s35\sbasedon34\snext35{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\sb0\sa120\cf0\hich\af3\langfe2052\dbch\af7\loch\f3\fs24\lang1033 List;}
{\s36\sbasedon0\snext36{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\sb120\sa120\cf0\i\hich\af3\langfe2052\dbch\af7\ai\loch\f3\fs24\lang1033 Caption;}
{\s37\sbasedon0\snext37{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf0\hich\af3\langfe2052\dbch\af7\loch\f3\fs24\lang1033 Index;}
}{\*\listtable{\list\listtemplateid1
{\listlevel\levelnfc0\leveljc0\levelstartat1\levelfollow0{\leveltext \'01\'b7;}{\levelnumbers;}\fi0\li0}
{\listlevel\levelnfc0\leveljc0\levelstartat1\levelfollow0{\leveltext \'02\'01.;}{\levelnumbers\'01;}\fi-360\li1080}
{\listlevel\levelnfc0\leveljc0\levelstartat1\levelfollow0{\leveltext \'02\'02.;}{\levelnumbers\'01;}\fi-360\li1440}
{\listlevel\levelnfc0\leveljc0\levelstartat1\levelfollow0{\leveltext \'02\'03.;}{\levelnumbers\'01;}\fi-360\li1800}
{\listlevel\levelnfc0\leveljc0\levelstartat1\levelfollow0{\leveltext \'02\'04.;}{\levelnumbers\'01;}\fi-360\li2160}
{\listlevel\levelnfc0\leveljc0\levelstartat1\levelfollow0{\leveltext \'02\'05.;}{\levelnumbers\'01;}\fi-360\li2520}
{\listlevel\levelnfc0\leveljc0\levelstartat1\levelfollow0{\leveltext \'02\'06.;}{\levelnumbers\'01;}\fi-360\li2880}
{\listlevel\levelnfc0\leveljc0\levelstartat1\levelfollow0{\leveltext \'02\'07.;}{\levelnumbers\'01;}\fi-360\li3240}
{\listlevel\levelnfc0\leveljc0\levelstartat1\levelfollow0{\leveltext \'02\'08.;}{\levelnumbers\'01;}\fi-360\li3600}\listid1}
{\list\listtemplateid2
{\listlevel\levelnfc0\leveljc0\levelstartat1\levelfollow0{\leveltext \'00;}{\levelnumbers;}\fi-432\li432}
{\listlevel\levelnfc0\leveljc0\levelstartat1\levelfollow0{\leveltext \'00;}{\levelnumbers;}\fi-576\li576}
{\listlevel\levelnfc0\leveljc0\levelstartat1\levelfollow0{\leveltext \'00;}{\levelnumbers;}\fi-720\li720}
{\listlevel\levelnfc0\leveljc0\levelstartat1\levelfollow0{\leveltext \'00;}{\levelnumbers;}\fi-864\li864}
{\listlevel\levelnfc0\leveljc0\levelstartat1\levelfollow0{\leveltext \'00;}{\levelnumbers;}\fi-1008\li1008}
{\listlevel\levelnfc0\leveljc0\levelstartat1\levelfollow0{\leveltext \'00;}{\levelnumbers;}\fi-1152\li1152}
{\listlevel\levelnfc0\leveljc0\levelstartat1\levelfollow0{\leveltext \'00;}{\levelnumbers;}\fi-1296\li1296}
{\listlevel\levelnfc0\leveljc0\levelstartat1\levelfollow0{\leveltext \'00;}{\levelnumbers;}\fi-1440\li1440}
{\listlevel\levelnfc0\leveljc0\levelstartat1\levelfollow0{\leveltext \'00;}{\levelnumbers;}\fi-1584\li1584}\listid2}
}{\listoverridetable{\listoverride\listid1\listoverridecount0\ls1}{\listoverride\listid2\listoverridecount0\ls2}}{\info{\creatim\yr0\mo0\dy0\hr0\min0}{\revtim\yr0\mo0\dy0\hr0\min0}{\printim\yr0\mo0\dy0\hr0\min0}{\comment OpenOffice}{\vern4150}}\deftab720\deftab720\deftab720\deftab720\deftab720\deftab720\deftab720\deftab720\deftab720\deftab720\deftab720\deftab720\deftab720\deftab720\deftab720\deftab720\deftab720\deftab720\deftab720\deftab720

{\*\pgdsctbl
{\pgdsc0\pgdscuse195\pgwsxn12240\pghsxn15840\marglsxn1800\margrsxn1800\margtsxn1440\margbsxn1440\pgdscnxt0 Default;}}
\formshade{\*\pgdscno0}\paperh15840\paperw12240\margl1800\margr1800\margt1440\margb1440\sectd\sbknone\sectunlocked1\pgndec\pgwsxn12240\pghsxn15840\marglsxn1800\margrsxn1800\margtsxn1440\margbsxn1440\ftnbj\ftnstart1\ftnrstcont\ftnnar\aenddoc\aftnrstcont\aftnstart1\aftnnrlc
\pgndec\pard\plain \s0{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\nowidctlpar\cf0\hich\af3\langfe1033\dbch\af3\afs24\lang1081\loch\f3\fs24\lang1033\sl276\slmult1\qc\sb0\sa200{\i\ul\ulc0\afs48\ai\loch\f4\rtlch \ltrch\loch\fs48\lang9\loch\f5
TO DO LIST}
\par \pard\plain \s0{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\nowidctlpar\cf0\hich\af3\langfe1033\dbch\af3\afs24\lang1081\loch\f3\fs24\lang1033\sl276\slmult1\qc\sb0\sa200{\i\ul\ulc0\afs32\ai\rtlch \ltrch\loch\fs32\lang9\loch\f5
Can be completed in any order. Important is in }{\cf2\i\ul\ulc0\afs32\ai\rtlch \ltrch\loch\fs32\lang9\loch\f5
red}
\par \pard\plain \s0{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\nowidctlpar\cf0\hich\af3\langfe1033\dbch\af3\afs24\lang1081\loch\f3\fs24\lang1033{\listtext\pard\plain \loch\f4 \'b7\tab}\ilvl0\ls1 \li720\ri0\lin720\rin0\fi0\sl276\slmult1\tx720\li720\ri0\lin720\rin0\fi0\sb0\sa200{\afs28\rtlch \ltrch\loch\fs28\lang9
Autumn, Spring and Winter Tiles}
\par \pard\plain \s0{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\nowidctlpar\cf0\hich\af3\langfe1033\dbch\af3\afs24\lang1081\loch\f3\fs24\lang1033{\listtext\pard\plain \loch\f4 \'b7\tab}\ilvl0\ls1 \li720\ri0\lin720\rin0\fi0\sl276\slmult1\tx720\li720\ri0\lin720\rin0\fi0\sb0\sa200{\afs28\rtlch \ltrch\fs28\lang9
 }{\afs28\rtlch \ltrch\loch\fs28\lang9
Detailed Battle Backgrounds}
\par \pard\plain \s0{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\nowidctlpar\cf0\hich\af3\langfe1033\dbch\af3\afs24\lang1081\loch\f3\fs24\lang1033{\listtext\pard\plain \loch\f4 \'b7\tab}\ilvl0\ls1 \li720\ri0\lin720\rin0\fi0\sl276\slmult1\tx720\li720\ri0\lin720\rin0\fi0\sb0\sa200{\afs28\rtlch \ltrch\loch\fs28\lang9
Autumn, Spring and Winter Autotiles}
\par \pard\plain \s0{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\nowidctlpar\cf0\hich\af3\langfe1033\dbch\af3\afs24\lang1081\loch\f3\fs24\lang1033{\listtext\pard\plain \loch\f4 \'b7\tab}\ilvl0\ls1 \li720\ri0\lin720\rin0\fi0\sl276\slmult1\tx720\li720\ri0\lin720\rin0\fi0\sb0\sa200{\afs28\rtlch \ltrch\loch\fs28\lang9
Sprites for Fakemon (overworld and in battle)}
\par \pard\plain \s0{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\nowidctlpar\cf0\hich\af3\langfe1033\dbch\af3\afs24\lang1081\loch\f3\fs24\lang1033{\listtext\pard\plain \loch\f4 \'b7\tab}\ilvl0\ls1 \li720\ri0\lin720\rin0\fi0\sl276\slmult1\tx720\li720\ri0\lin720\rin0\fi0\sb0\sa200{\afs28\rtlch \ltrch\loch\fs28\lang9
Sprites for Trainers (overworld and in battle)}
\par \pard\plain \s0{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\nowidctlpar\cf0\hich\af3\langfe1033\dbch\af3\afs24\lang1081\loch\f3\fs24\lang1033{\listtext\pard\plain \loch\f4 \'b7\tab}\ilvl0\ls1 \li720\ri0\lin720\rin0\fi0\sl276\slmult1\tx720\li720\ri0\lin720\rin0\fi0\sb0\sa200{\cf2\afs28\rtlch \ltrch\loch\fs28\lang9
Choosing Pokemon UI for when player gets to collect their partner}
\par \pard\plain \s0{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\nowidctlpar\cf0\hich\af3\langfe1033\dbch\af3\afs24\lang1081\loch\f3\fs24\lang1033{\listtext\pard\plain \loch\f4 \'b7\tab}\ilvl0\ls1 \li720\ri0\lin720\rin0\fi0\sl276\slmult1\tx720\li720\ri0\lin720\rin0\fi0\sb0\sa200{\cf2\afs28\rtlch \ltrch\loch\fs28\lang9
Choosing Player UI/Sprites for beginning of game}
\par \pard\plain \s0{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\nowidctlpar\cf0\hich\af3\langfe1033\dbch\af3\afs24\lang1081\loch\f3\fs24\lang1033{\listtext\pard\plain \loch\f4 \'b7\tab}\ilvl0\ls1 \li720\ri0\lin720\rin0\fi0\sl276\slmult1\tx720\li720\ri0\lin720\rin0\fi0\sb0\sa200{\afs28\rtlch \ltrch\loch\fs28\lang9
Sprites/Art for Gender Neutral Player}
\par \pard\plain \s0{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\nowidctlpar\cf0\hich\af3\langfe1033\dbch\af3\afs24\lang1081\loch\f3\fs24\lang1033{\listtext\pard\plain \loch\f4 \'b7\tab}\ilvl0\ls1 \li720\ri0\lin720\rin0\fi0\sl276\slmult1\tx720\li720\ri0\lin720\rin0\fi0\sb0\sa200{\cf2\afs28\rtlch \ltrch\loch\fs28\lang9
Finalize design for door on Professor Acacia's lab}
\par \pard\plain \s0{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\nowidctlpar\cf0\hich\af3\langfe1033\dbch\af3\afs24\lang1081\loch\f3\fs24\lang1033{\listtext\pard\plain \loch\f4 \'b7\tab}\ilvl0\ls1 \li720\ri0\lin720\rin0\fi0\sl276\slmult1\tx720\li720\ri0\lin720\rin0\fi0\sb0\sa200{\cf3\afs28\rtlch \ltrch\loch\fs28\lang9
Sprites for TV screen lit up and computer (DONE)}
\par \pard\plain \s0{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\nowidctlpar\cf0\hich\af3\langfe1033\dbch\af3\afs24\lang1081\loch\f3\fs24\lang1033{\listtext\pard\plain \loch\f4 \'b7\tab}\ilvl0\ls1 \li720\ri0\lin720\rin0\fi0\sl276\slmult1\tx720\li720\ri0\lin720\rin0\fi0\sb0\sa200{\afs28\rtlch \ltrch\loch\fs28\lang9
Title Screen}
\par \pard\plain \s0{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\nowidctlpar\cf0\hich\af3\langfe1033\dbch\af3\afs24\lang1081\loch\f3\fs24\lang1033{\listtext\pard\plain \loch\f4 \'b7\tab}\ilvl0\ls1 \li720\ri0\lin720\rin0\fi0\sl276\slmult1\tx720\li720\ri0\lin720\rin0\fi0\sb0\sa200{\afs28\rtlch \ltrch\loch\fs28\lang9
UI Designs}
\par \pard\plain \s0{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\nowidctlpar\cf0\hich\af3\langfe1033\dbch\af3\afs24\lang1081\loch\f3\fs24\lang1033{\listtext\pard\plain \loch\f4 \'b7\tab}\ilvl0\ls1 \li720\ri0\lin720\rin0\fi0\sl276\slmult1\tx720\li720\ri0\lin720\rin0\fi0\sb0\sa200{\afs28\rtlch \ltrch\loch\fs28\lang9
Tiles}
\par \pard\plain \s0{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\nowidctlpar\cf0\hich\af3\langfe1033\dbch\af3\afs24\lang1081\loch\f3\fs24\lang1033{\listtext\pard\plain \loch\f4 \'b7\tab}\ilvl0\ls1 \li720\ri0\lin720\rin0\fi0\sl276\slmult1\tx720\li720\ri0\lin720\rin0\fi0\sb0\sa200{\afs28\rtlch \ltrch\loch\fs28\lang9
Beach Grass}
\par \pard\plain \s0{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\nowidctlpar\cf0\hich\af3\langfe1033\dbch\af3\afs24\lang1081\loch\f3\fs24\lang1033{\listtext\pard\plain \loch\f4 \'b7\tab}\ilvl0\ls1 \li720\ri0\lin720\rin0\fi0\sl276\slmult1\tx720\li720\ri0\lin720\rin0\fi0\sb0\sa200{\afs28\rtlch \ltrch\loch\fs28\lang9
Flowers}
\par \pard\plain \s0{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\nowidctlpar\cf0\hich\af3\langfe1033\dbch\af3\afs24\lang1081\loch\f3\fs24\lang1033{\listtext\pard\plain \loch\f4 \'b7\tab}\ilvl0\ls1 \li720\ri0\lin720\rin0\fi0\sl276\slmult1\tx720\li720\ri0\lin720\rin0\fi0\sb0\sa200{\afs28\rtlch \ltrch\loch\fs28\lang9
Animated Water}
\par }