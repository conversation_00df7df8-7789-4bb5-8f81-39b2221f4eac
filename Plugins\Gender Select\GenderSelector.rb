#===============================================================================
# Gender Selector UI
#===============================================================================
class GenderSelector_Scene
  # Constants for gender and skin color options
  GENDERS = [
    _INTL("Male"),
    _INTL("Female"),
    _INTL("Neutral")
  ]

  SKIN_COLORS = [
    _INTL("Light"),
    _INTL("Tan"),
    _INTL("Brown"),
    _INTL("Dark")
  ]

  # Portrait dimensions and spacing
  PORTRAIT_WIDTH = 75
  PORTRAIT_HEIGHT = 76
  PORTRAIT_SPACING_X = 26
  PORTRAIT_SPACING_Y = 8

  # Grid starting position
  GRID_START_X = 214
  GRID_START_Y = 28

  # Arrow animation settings
  ARROW_BOB_SPEED = 0.05
  ARROW_BOB_RANGE = 3

  # Character fade settings
  FADE_SPEED = 10

  # Gender prefixes for filenames
  GENDER_PREFIXES = ["malechoice", "femchoice", "neutralchoice"]

  def initialize
    @viewport = Viewport.new(0, 0, Graphics.width, Graphics.height)
    @viewport.z = 99999
    @sprites = {}
    @gender_index = 0
    @skin_color_index = 0
    @portrait_sprites = []
    @selected_portrait = 0

    # Fading effect variables
    @fading = false
    @fade_direction = 0  # 0 = fading out, 1 = fading in
    @new_gender = 0
    @new_skin_color = 0

    # Arrow bobbing animation variables
    @arrow_bob_timer = 0
    @arrow_base_x = 0
  end

  def pbStartScene
    @sprites["background"] = IconSprite.new(0, 0, @viewport)
    @sprites["background"].setBitmap("Graphics/Plugins/Gender Select/bg")
    @sprites["shadow"] = IconSprite.new(0, 0, @viewport)
    @sprites["shadow"].setBitmap("Graphics/UI/Trainer Card/cardshadow")
    @sprites["shadow"].x = 32
    @sprites["shadow"].y = 318
    @sprites["shadow"].opacity = 120
    @sprites["trainer"] = IconSprite.new(40, 94, @viewport)
    @sprites["trainer"].setBitmap("Graphics/UI/Trainer Card/POKEMONTRAINER_Julian")
    @sprites["textbar"] = IconSprite.new(12, 51, @viewport)
    @sprites["textbar"].setBitmap("Graphics/Plugins/Gender Select/bar")
    @sprites["overlay"] = BitmapSprite.new(Graphics.width, Graphics.height, @viewport)
    pbSetSystemFont(@sprites["overlay"].bitmap)
    textpos = [
      [_INTL("Who are you?"), 36, 65, 0, Color.new(255, 255, 255), Color.new(125, 125, 125)]
    ]
    pbDrawTextPositions(@sprites["overlay"].bitmap, textpos)
    create_portrait_grid
    @sprites["arrow"] = IconSprite.new(0, 0, @viewport)
    @sprites["arrow"].setBitmap("Graphics/Plugins/Gender Select/yellow_arrow")
    update_arrow_position
    all_sprites = {}
    @sprites.each do |key, sprite|
      all_sprites[key] = sprite
    end

    @portrait_sprites.each_with_index do |portrait_data, i|
      all_sprites["portrait_#{i}"] = portrait_data[:sprite] if portrait_data[:sprite]
    end
    
    pbFadeInAndShow(all_sprites) { pbUpdate }
  end

  def create_portrait_grid
    # Clear any existing portrait sprites
    @portrait_sprites.each do |portrait_data|
      portrait_data[:sprite].dispose if portrait_data[:sprite]
      portrait_data[:bitmap].dispose if portrait_data[:bitmap]
    end
    @portrait_sprites.clear

    # Create portraits for all three genders (columns) and four skin colors (rows)
    gender_files = ["maleskinsel", "femaleskinsel", "neutralskinsel"]

    # Create the grid of portraits
    GENDERS.size.times do |gender|
      SKIN_COLORS.size.times do |skin_color|
        # Create portrait for this gender and skin color
        sprite = BitmapSprite.new(PORTRAIT_WIDTH, PORTRAIT_HEIGHT, @viewport)

        # Load the appropriate portrait file
        portrait_file = gender_files[gender]
        portrait_bitmap = Bitmap.new("Graphics/Plugins/Gender Select/#{portrait_file}")

        # Each skin color has two states (normal and highlighted)
        # Normal state is on the left, highlighted state is on the right
        src_x = 0  # Start with normal (unhighlighted) state
        src_y = skin_color * PORTRAIT_HEIGHT

        # Copy the appropriate portion of the bitmap
        sprite.bitmap.blt(0, 0, portrait_bitmap,
                         Rect.new(src_x, src_y, PORTRAIT_WIDTH, PORTRAIT_HEIGHT))

        # Position the sprite in the grid
        sprite.x = GRID_START_X + (gender * (PORTRAIT_WIDTH + PORTRAIT_SPACING_X))
        sprite.y = GRID_START_Y + (skin_color * (PORTRAIT_HEIGHT + PORTRAIT_SPACING_Y))

        # Store the sprite and the source bitmap for later use
        @portrait_sprites.push({
          sprite: sprite,
          bitmap: portrait_bitmap,
          gender: gender,
          skin_color: skin_color,
          highlighted: false
        })
      end
    end

    # Find the portrait that matches the current gender and skin color
    @selected_portrait = find_portrait_index(@gender_index, @skin_color_index)

    # Set the selected portrait as highlighted initially
    highlight_portrait(@selected_portrait)
  end

  def find_portrait_index(gender, skin_color)
    @portrait_sprites.each_with_index do |portrait_data, index|
      if portrait_data[:gender] == gender && portrait_data[:skin_color] == skin_color
        return index
      end
    end
    return 0
  end

  def highlight_portrait(index)
    return if index >= @portrait_sprites.size

    # Reset all portraits to unhighlighted state
    @portrait_sprites.each_with_index do |portrait_data, i|
      if i == index
        # Highlight this portrait
        portrait_data[:highlighted] = true
        src_x = portrait_data[:bitmap].width / 2  # Highlighted state is on the right half
        src_y = portrait_data[:skin_color] * portrait_data[:sprite].height
        portrait_data[:sprite].bitmap.clear
        portrait_data[:sprite].bitmap.blt(0, 0, portrait_data[:bitmap],
                                         Rect.new(src_x, src_y, portrait_data[:sprite].width, portrait_data[:sprite].height))
      elsif portrait_data[:highlighted]
        # Unhighlight this portrait
        portrait_data[:highlighted] = false
        src_x = 0  # Normal state is on the left half
        src_y = portrait_data[:skin_color] * portrait_data[:sprite].height
        portrait_data[:sprite].bitmap.clear
        portrait_data[:sprite].bitmap.blt(0, 0, portrait_data[:bitmap],
                                         Rect.new(src_x, src_y, portrait_data[:sprite].width, portrait_data[:sprite].height))
      end
    end

    # Update the gender and skin color based on the selected portrait
    if index < @portrait_sprites.size
      new_gender = @portrait_sprites[index][:gender]
      new_skin_color = @portrait_sprites[index][:skin_color]

      # If gender changed, start the fade effect
      if new_gender != @gender_index
        start_fade_effect(new_gender, new_skin_color)
      else
        # Just update the skin color without fading
        @skin_color_index = new_skin_color
        update_trainer_graphic
      end
    end
  end

  def start_fade_effect(new_gender, new_skin_color)
    @fading = true
    @fade_timer = 0
    @fade_direction = 0  # Start by fading out
    @new_gender = new_gender
    @new_skin_color = new_skin_color
  end

  def update_fade_effect
    return if !@fading

    if @fade_direction == 0  # Fading out
      @sprites["trainer"].opacity -= FADE_SPEED
      if @sprites["trainer"].opacity <= 0
        @fade_direction = 1  # Switch to fading in
        @gender_index = @new_gender
        @skin_color_index = @new_skin_color
        update_trainer_graphic
      end
    else  # Fading in
      @sprites["trainer"].opacity += FADE_SPEED
      if @sprites["trainer"].opacity >= 255
        @fading = false  # Done fading
      end
    end
  end

  def update_arrow_position
    return if @selected_portrait >= @portrait_sprites.size || @portrait_sprites.empty?

    target_sprite = @portrait_sprites[@selected_portrait][:sprite]
    @arrow_base_x = target_sprite.x - 16  # Store the base x position
    @sprites["arrow"].x = @arrow_base_x   # Set initial position
    @sprites["arrow"].y = target_sprite.y + 26
  end

  def update_arrow_animation
    return if !@sprites["arrow"]

    # Update the bob timer
    @arrow_bob_timer += ARROW_BOB_SPEED

    # Calculate the new x position using a sine wave for smooth bobbing
    bob_offset = Math.sin(@arrow_bob_timer) * ARROW_BOB_RANGE
    @sprites["arrow"].x = @arrow_base_x + bob_offset
  end

  def pbEndScene
    # Create a combined hash of all sprites for fading
    all_sprites = {}

    # Add main sprites
    @sprites.each do |key, sprite|
      all_sprites[key] = sprite
    end

    # Add portrait sprites with unique keys
    @portrait_sprites.each_with_index do |portrait_data, i|
      all_sprites["portrait_#{i}"] = portrait_data[:sprite] if portrait_data[:sprite]
    end

    # Fade out all sprites together
    pbFadeOutAndHide(all_sprites) { pbUpdate }

    # Dispose portrait sprites
    @portrait_sprites.each do |portrait_data|
      portrait_data[:sprite].dispose if portrait_data[:sprite]
      portrait_data[:bitmap].dispose if portrait_data[:bitmap]
    end
    @portrait_sprites.clear

    # Dispose other sprites
    pbDisposeSpriteHash(@sprites)
    @viewport.dispose
  end

  def pbUpdate
    pbUpdateSpriteHash(@sprites)
    update_arrow_animation  # Update the arrow bobbing animation
  end

  def pbMain
    loop do
      Graphics.update
      Input.update
      pbUpdate

      # Update fade effect if active
      update_fade_effect

      # Only allow input if not fading
      if !@fading
        old_selection = @selected_portrait

        if Input.trigger?(Input::UP)
          # Move up one row (same gender, previous skin color)
          current_gender = @portrait_sprites[@selected_portrait][:gender]
          current_skin = @portrait_sprites[@selected_portrait][:skin_color]

          if current_skin > 0
            new_index = find_portrait_index(current_gender, current_skin - 1)
            if new_index >= 0
              @selected_portrait = new_index
              pbPlayCursorSE
            end
          end
        elsif Input.trigger?(Input::DOWN)
          # Move down one row (same gender, next skin color)
          current_gender = @portrait_sprites[@selected_portrait][:gender]
          current_skin = @portrait_sprites[@selected_portrait][:skin_color]

          if current_skin < 3
            new_index = find_portrait_index(current_gender, current_skin + 1)
            if new_index >= 0
              @selected_portrait = new_index
              pbPlayCursorSE
            end
          end
        elsif Input.trigger?(Input::LEFT)
          # Move left one column (previous gender, same skin color)
          current_gender = @portrait_sprites[@selected_portrait][:gender]
          current_skin = @portrait_sprites[@selected_portrait][:skin_color]

          if current_gender > 0
            new_index = find_portrait_index(current_gender - 1, current_skin)
            if new_index >= 0
              @selected_portrait = new_index
              pbPlayCursorSE
            end
          end
        elsif Input.trigger?(Input::RIGHT)
          # Move right one column (next gender, same skin color)
          current_gender = @portrait_sprites[@selected_portrait][:gender]
          current_skin = @portrait_sprites[@selected_portrait][:skin_color]

          if current_gender < 2
            new_index = find_portrait_index(current_gender + 1, current_skin)
            if new_index >= 0
              @selected_portrait = new_index
              pbPlayCursorSE
            end
          end
        elsif Input.trigger?(Input::USE)
          pbPlayDecisionSE
          break
        elsif Input.trigger?(Input::BACK)
          pbPlayCancelSE
          @selected_portrait = 0
          break
        end

        # If selection changed, update highlighting and arrow position
        if old_selection != @selected_portrait
          highlight_portrait(@selected_portrait)
          update_arrow_position
        end
      end
    end

    # Wait for any fade effect to complete
    while @fading
      Graphics.update
      Input.update
      pbUpdate
      update_fade_effect
    end

    # Return the selected gender and skin color
    gender = @portrait_sprites[@selected_portrait][:gender]
    skin_color = @portrait_sprites[@selected_portrait][:skin_color]
    return [gender, skin_color]
  end

  def update_trainer_graphic
    # Get the selected gender and skin color
    gender = @gender_index
    skin_color = @skin_color_index

    # Get the gender prefix from the constants
    gender_prefix = GENDER_PREFIXES[gender]

    # Construct the filename with the skin color
    filename = sprintf("Graphics/Plugins/Gender Select/Skin Choices/%s_%d", gender_prefix, skin_color)

    # Set the bitmap
    if pbResolveBitmap(filename)
      @sprites["trainer"].setBitmap(filename)
    else
      # Fallback to skin color 0 if the specific skin color isn't found
      fallback_filename = sprintf("Graphics/Plugins/Gender Select/Skin Choices/%s_0", gender_prefix)
      if pbResolveBitmap(fallback_filename)
        @sprites["trainer"].setBitmap(fallback_filename)
      end
    end
  end
end

#===============================================================================
# Gender Selector Screen
#===============================================================================
class GenderSelector_Screen
  def initialize(scene)
    @scene = scene
  end

  def pbStartScreen
    @scene.pbStartScene
    result = @scene.pbMain
    @scene.pbEndScene
    return result
  end
end

#===============================================================================
# Main method to call the Gender Selector
#===============================================================================
def pbGenderSelector
  scene = GenderSelector_Scene.new
  screen = GenderSelector_Screen.new(scene)
  result = screen.pbStartScreen

  # Get the selected gender and skin color
  gender_index = result[0]
  skin_color_index = result[1]

  # Show confirmation message based on gender
  gender_name = GenderSelector_Scene::GENDERS[gender_index]
  confirm_message = case gender_index
                    when 0 then _INTL("So you're a boy?")
                    when 1 then _INTL("So you're a girl?")
                    when 2 then _INTL("So you're gender neutral?")
                    end

  if pbConfirmMessage(confirm_message)
    # Set the player's gender and skin color if $player exists
    if $player
      character_id = gender_index + 1
      $player.character_ID = character_id
      $player.skin_color = skin_color_index
      $game_variables[32] = gender_index
      $game_map.need_refresh = true if $game_map
      $game_player.refresh_charset if $game_player
      default_name = case gender_index
                     when 0 then _INTL("Julian")
                     when 1 then _INTL("Julia")
                     when 2 then _INTL("Jay")
                     end

      name = pbEnterPlayerName(_INTL("Your name?"), 1, Settings::MAX_PLAYER_NAME_SIZE, default_name)
      $player.name = name if name && !name.empty?
    end

    return result
  else
    # Player canceled, so call the gender selector again
    return pbGenderSelector
  end
end