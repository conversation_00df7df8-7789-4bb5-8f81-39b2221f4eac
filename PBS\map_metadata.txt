﻿# See the documentation on the wiki to learn how to edit this file.
#-------------------------------
[002]   # Prof. <PERSON>'s Lab (Intro)
Name = Prof. <PERSON>'s Lab
MapPosition = 0,23,14
#-------------------------------
[003]   # Route 111
Name = Route 111
Outdoor = true
ShowArea = true
Bicycle = true
MapPosition = 0,5,14
BattleBack = sandfield
Environment = None
#-------------------------------
[004]   # Prof. <PERSON><PERSON>'s Lab
Name = Prof. <PERSON>'s Lab
MapPosition = 0,23,14
Flags = PokemonLab
#-------------------------------
[005]   # Resident's Home
Name = Heartwood City
#-------------------------------
[006]   # Heartwood City
Name = Heartwood City
Outdoor = true
ShowArea = true
Bicycle = true
MapPosition = 0,5,12
#-------------------------------
[007]   # Forest Gatehouse
Name = Heartwood Forest
MapPosition = 0,0,10
#-------------------------------
[008]   # Seren Island
Name = Seren Island
Outdoor = true
ShowArea = true
Bicycle = true
MapPosition = 0,32,16
Flags = SerenIsland
#-------------------------------
[010]   # Fall Temple
Name = Fall Temple
Bicycle = true
MapPosition = 0,0,10
MapSize = 0,
Environment = Cave
#-------------------------------
[011]   # Route 111
Name = Route 111
MapPosition = 0,2,12
#-------------------------------
[012]   # Violet's Home
Name = Violet's Home
MapPosition = 0,23,14
#-------------------------------
[014]   # \PN's Home
Name = Fohlen's Home
HealingSpot = 8,16,26
MapPosition = 0,23,14
#-------------------------------
[017]   # Spring Temple
Name = Weathered Cavern
ShowArea = true
Bicycle = true
MapPosition = 0,23,14
#-------------------------------
[020]   # Heartwood City Gym
Name = Heartwood Gym
#-------------------------------
[021]   # Hidden Grotto
Name = Hidden Grotto
Outdoor = true
ShowArea = true
Bicycle = true
MapPosition = 0,2,12
Flags = HiddenGrotto
#-------------------------------
[022]   # Resident's Home
Name = Resident's Home
MapPosition = 0,23,14
#-------------------------------
[023]   # Prof. Acacia's Home
Name = Prof. Acacia's Home
MapPosition = 0,23,14
#-------------------------------
[024]   # Heartwood Poké Center
Name = Heartwood City
MapPosition = 0,2,10
#-------------------------------
[025]   # Heartwood Poké Mart
Name = Heartwood City
#-------------------------------
[030]   # Howling Woods
Name = Howling Forest
Outdoor = true
ShowArea = true
Bicycle = true
MapPosition = 0,0,10
Environment = ForestGrass
Flags = Forest
#-------------------------------
[032]   # Test Map Outside
Name = Test Map Outside
Outdoor = true
#-------------------------------
[033]   # Fall Temple
Name = Fall Temple
Bicycle = true
Environment = Cave
#-------------------------------
[034]   # Heartwood Gatehouse
Name = Heartwood City
#-------------------------------
[035]   # Heartwood Orchard
Name = Heartwood Orchard
Outdoor = true
ShowArea = true
Bicycle = true
MapPosition = 0,2,10
Flags = FirstGym
#-------------------------------
[036]   # Heartwood Forest
Name = Heartwood Forest
Outdoor = true
ShowArea = true
Bicycle = true
MapPosition = 0,0,10
MapSize = 1,
Environment = ForestGrass
