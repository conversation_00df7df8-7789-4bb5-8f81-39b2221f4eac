# Surf Jumping Animation

This feature adds a custom jumping sprite for the player when transitioning to surf.

## Setup Instructions

1. Place your jumping sprite images in the `Graphics/Characters` folder:
   - For male characters: `malejumping.png`
   - For female characters: `femalejumping.png`

2. The sprite sheet should follow the standard Pokémon Essentials character format:
   - 4 columns (representing animation frames)
   - 4 rows (representing directions: down, left, right, up)
   - Each cell should be 32x32 pixels (or whatever your game's character size is)

3. The sprite will automatically be used when the player jumps onto water when using Surf.

## How It Works

When the player uses Surf and jumps onto water, the game will:
1. Temporarily change the player's sprite to the jumping sprite
2. Perform the jump animation
3. Change to the regular surfing sprite once on the water

## Customization

You can customize the jumping sprites for each player character by editing the `PBS/metadata.txt` file:

```
[1]  # Player character ID
TrainerType = POKEMONTRAINER_Name
...
SurfCharset = surfsprite
SurfJumpCharset = jumpsprite  # This is the sprite used during the jump
...
```

## Troubleshooting

If the jumping animation doesn't appear:
1. Make sure your sprite files are correctly named and placed in the Graphics/Characters folder
2. Check that the SurfJumpCharset entry is correctly added in the metadata.txt file
3. Verify that the sprite sheet follows the standard character format
